server.contextPath=/
# åå¸å¼ç«¯å£
server.port=8100
# tomcatæå¤§çº¿ç¨æ°ï¼é»è®¤ä¸º200
server.tomcat.max-threads=500
# tomcatçURIç¼ç 
server.tomcat.uri-encoding=UTF-8
# å­æ¾Tomcatçæ¥å¿ãDumpç­æä»¶çä¸´æ¶æä»¶å¤¹ï¼é»è®¤ä¸ºç³»ç»çtmpæä»¶å¤¹ï¼å¦ï¼C:\Users\<USER>\AppData\Local\Tempï¼
server.tomcat.basedir=/springboot/allinone
# æå¼TomcatçAccessæ¥å¿ï¼å¹¶å¯ä»¥è®¾ç½®æ¥å¿æ ¼å¼çæ¹æ³ï¼
server.tomcat.access-log-enabled=true
# æ¥å¿æä»¶ç®å½
logging.path=/springboot/allinone
# æ¥å¿æä»¶åç§°ï¼é»è®¤ä¸ºspring.log
logging.file=myapp.log

logging.config=classpath:logback-spring.xml
#logging.level.org.springframework.web=INFO  

#server.ssl.key-store: classpath:mp_keystore.p12
#server.ssl.key-store-password: szlanyou123
#server.ssl.keyStoreType: PKCS12
#server.ssl.keyAlias: szlanyou_mp

#ActiveProfile
spring.profiles.active=dev

#jackson
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.locale=zh_CN
spring.jackson.time-zone=GMT+8
spring.mvc.locale=zh_CN 

# Enable support of multi-part uploads.
multipart.enabled=true 
# Max file size. Values can use the suffixed "MB" or "KB" to indicate a Megabyte or Kilobyte size.
spring.servlet.multipart.max-file-size=50MB
# Max request size. Values can use the suffixed "MB" or "KB" to indicate a Megabyte or Kilobyte size.
spring.servlet.multipart.max-request-size=100MB

#--------------------ç­ä¿¡éªè¯ç éç½®--------------------
#mp.auth.smsContentFormat=ç­ä¿¡éªè¯ç :%s
mp.auth.smsContentFormat=\u767b\u5f55\u9a8c\u8bc1\u7801:%s
#--------------------ç­ä¿¡éªè¯ç éç½®--------------------

#--------------------CCéæéç½®--------------------

mp.auth.cc.tompKey=ccmpjavaccmpjava

mp.auth.cc.toccKey=ccmpjavaccmpjava

#--------------------CCéæéç½®--------------------

#Job Service
#å¹³å°
#0  é»è®¤å¼ï¼cp)
#1  eap
mp.job.platform=0
#é®ä»¶æ«æé´é
mp.job.alertServiceMailInterval=0/12 * * * * ?
#ä¸å¡é®ä»¶æéåéé¢ç
mp.job.businessRemindEmailInterval=0/12 * * * * ?
#ä¸ªäººæéè®¾ç½®åºç¨åï¼å·¥ä½æµ
mp.job.remind.wfFunctionCode=CPP010404
#ä¸å¡æéåºç¨åè½ç¼å·ï¼
mp.job.remind.businessFunctionCode=CPP010404
#å¼ææéåééè¯¯ï¼å°è¯æ¬¡æ°
mp.job.remind.wfFailTime=3
#ä¸å¡æéåééè¯¯ï¼å°è¯æ¬¡æ°
mp.job.remind.businessFaileTime=3
#é®ä»¶ç­¾å
#mp.job.mailSignature=é®ä»¶ç­¾å
mp.job.mailSignature=\u90ae\u4ef6\u7b7e\u540d
#valueä¸ºtrueæ¶,æ¯é®ä»¶çæµè¯æ¨¡å¼,æµè¯æ¨¡å¼å°ä¸ä¼åç®æ ç¨æ·åéé®ä»¶
mp.job.mailTest=false
#é®ä»¶çæµè¯æ¨¡å¼æ¶,ææé®ä»¶é½å°åéè³è¯¥é®ä»¶
mp.job.testMailAccount=<EMAIL>
#é®ä»¶åéåææ°æ¶é´æ®µ
#æ ¼å¼è¯´æï¼24å°æ¶å¶ï¼åé¢æ¶é´å¿é¡»å°äºåé¢æ¶é´ï¼å¦åæ æ
#ä¸­é´ç¨è±æéå·éå¼
mp.job.emailNoDisturbTimes=00:00-8:30,11:30-13:30,18:00-24:00
#ç­ä¿¡åéåææ°æ¶é´æ®µ
#æ ¼å¼åä¸
mp.job.sMSNoDisturbTimes=00:00-8:30,11:30-13:30,18:00-24:00
#é®ä»¶æ±æ»æ¶é´ï¼ä¸­é´ç¨éå·éå¼
mp.job.emailGatherTimes=08:30,18:00
#æ±æ»åéé®ä»¶æ é¢
#mp.job.emailGatherTitle=æ°[PQC]ç³»ç»é®ä»¶æé
mp.job.emailGatherTitle=\u65b0[PQC]\u7cfb\u7edf\u90ae\u4ef6\u63d0\u9192
#æ±æ»é®ä»¶æ¨¡æ¿
#mp.job.emailGatherModel=@sendtouser \u60a8\u597d<br/>\u65b0PQC\u4e2d\uff0c\u60a8\u603b\u5171\u5f85\u5904\u7406\u5f85\u529e\u6709@pengdingcount\u6761<br/><br/>\u8bf7\u5c3d\u5feb\u5904\u7406<a href='@url'>\u90ae\u4ef6\u6c47\u603b\u94fe\u63a5</a>,\u8c22\u8c22\uff01@sendtouser æ¨å¥½<br/>æ°PQCä¸­ï¼æ¨æ»å±å¾å¤çå¾åæ@pengdingcountæ¡<br/><br/>è¯·å°½å¿«å¤ç<a href='@url'>é®ä»¶æ±æ»é¾æ¥</a>,è°¢è°¢ï¼
mp.job.emailGatherModel=@sendtouser \u60a8\u597d<br/>\u65b0PQC\u4e2d\uff0c\u60a8\u603b\u5171\u5f85\u5904\u7406\u5f85\u529e\u6709@pengdingcount\u6761<br/><br/>\u8bf7\u5c3d\u5feb\u5904\u7406<a href='@url'>\u90ae\u4ef6\u6c47\u603b\u94fe\u63a5</a>,\u8c22\u8c22\uff01
#å¿éé¡¹
##é®ä»¶æ±æ»å®¡æ¹webæå¡æ¥å£ï¼å°localhostä¿®æ¹ä¸ºå¥å£å°åï¼å¦ï¼*************:8100
mp.job.emailGatherWebService=http://*************/
#ç­ä¿¡æå¡å¨IPå°å
mp.job.sms.serverID=************
#ç­ä¿¡æå¡ç»å½å
mp.job.sms.loginName=MP
#ç­ä¿¡æå¡ç»å½å¯ç 
mp.job.sms.loginPWD=XXX
#ç­ä¿¡æå¡å¨ç«¯å£å·
mp.job.sms.serverPort=8003
#è·åç­ä¿¡åæ§çé¢ç
mp.job.sms.rptInterval=0 0/1 * * * ?
#valueä¸ºtrueæ¶,æ¯ç­ä¿¡çæµè¯æ¨¡å¼,æµè¯æ¨¡å¼å°ä¸ä¼åç­ä¿¡æå¡å¨åéç­ä¿¡è¯·æ±
mp.job.sms.test=false
#ç­ä¿¡æ«æé´é,åä½ç§
mp.job.alertServiceSMSInterval=0/12 * * * * ?
#ä¸å¡ç­ä¿¡æéåéé¢çï¼åä½ï¼ç§ï¼
mp.job.businessRemindSmsInterval=0/12 * * * * ?
#èªå¨å®¡æ¹ä½ä¸æ¶é´é´é
mp.job.autoAuditInterval=0/12 * * * * ?
#å¿éé¡¹
#ç»å½æå¡å¨å°åï¼å°*************ä¿®æ¹ä¸ºå¥å£å°åï¼å¦ï¼*************:8100
mp.job.loginWebService=http://*************/mp/login/login.do
#æ°æ®å½æ¡£æ¡ä»¶ï¼éç½®è¿è¡çæ¶é´ç¹ï¼å¦éç½®1ï¼åè¿è¡æ¶é´ç¹å¨åæ¨1ç¹è¿è¡
mp.job.archive.runTime=0 0/10 * * * ?
#æ°æ®å½æ¡£æ¡ä»¶ï¼éç½®æ¯å¦æ§è¡å½æ¡£æå¡ï¼trueè¡¨ç¤ºæ§è¡ï¼falseè¡¨ç¤ºä¸æ§è¡
mp.job.archive.runStatus=true
#æ°æ®å½æ¡£æ¡ä»¶ï¼éç½®éè¦å½æ¡£æµç¨è·å®åå¤ä¹çæ°æ®ï¼åä½ï¼å¤©
mp.job.archive.dataInterval=60
#è½¬ç§»å¾åæ°æ®æ¶é´
mp.job.archive.transPendingTime=0 0 * * * ?

#è®¾ç½®ä»£çç¶æï¼1åéæ§è¡ä¸æ¬¡
mp.job.archive.wfAgentStatusTime=0 0/1 * * * ?

#æ¥å¿å½æ¡£æ¡ä»¶ï¼éç½®è¿è¡çæ¶é´ç¹ï¼å¦éç½®1ï¼åè¿è¡æ¶é´ç¹å¨åæ¨1ç¹è¿è¡ 
# æå»ºæ¶é´ Schedule ä¸­å¡«å 0 * * * * å¤ä¸ªæå»ºæ¶é´ä¹é´ï¼éè¿æ¢è¡ç¬¦éå¼ ç¬¬ä¸ä¸ªåæ°ä»£è¡¨çæ¯åé minuteï¼åå¼
#0~59ï¼ ç¬¬äºä¸ªåæ°ä»£è¡¨çæ¯å°æ¶ hourï¼åå¼ 0~23ï¼ ç¬¬ä¸ä¸ªåæ°ä»£è¡¨çæ¯å¤© dayï¼åå¼ 1~31ï¼ ç¬¬åä¸ªåæ°ä»£è¡¨çæ¯æ
# monthï¼åå¼ 1~12ï¼ æåä¸ä¸ªåæ°ä»£è¡¨çæ¯ææ weekï¼åå¼ 0~7ï¼0 å 7 é½æ¯è¡¨ç¤ºææå¤©ã
mp.job.archive.logFilingTime=0 26 9 * * ?
#æ¥å¿å½æ¡£æ¡ä»¶ï¼éç½®æ¯å¦æ§è¡å½æ¡£æå¡ï¼trueè¡¨ç¤ºæ§è¡ï¼falseè¡¨ç¤ºä¸æ§è¡
mp.job.archive.logFilingStatus=true
#è¿è¡æ¥å¿å½æ¡£æ¡ä»¶ï¼éç½®éè¦å½æ¡£æµç¨è·å®åå¤ä¹çæ°æ®ï¼åä½ï¼å¤©
mp.job.archive.log_runInterval=11
#ä¸å¡æ¥å¿å½æ¡£æ¡ä»¶ï¼éç½®éè¦å½æ¡£æµç¨è·å®åå¤ä¹çæ°æ®ï¼åä½ï¼å¤©
mp.job.archive.log_bssInterval=11
#èåæ¥å¿å½æ¡£æ¡ä»¶ï¼éç½®éè¦å½æ¡£æµç¨è·å®åå¤ä¹çæ°æ®ï¼åä½ï¼å¤©
mp.job.archive.log_navigationInterval=11
#æå¡è°ç¨æ¥å¿å½æ¡£æ¡ä»¶ï¼éç½®éè¦å½æ¡£æµç¨è·å®åå¤ä¹çæ°æ®ï¼åä½ï¼å¤©
mp.job.archive.log_invokingInterval=11
#æ¯å¦å¼å¯èªå¨åèµ·æå¡ 0ï¼ä¸å¼å¯  1ï¼å¼å¯
mp.job.autocreateOpen=1
#èªå¨åèµ·æå¡æ«æé´é
mp.job.autocreateInterval=0/12 * * * * ?
#å¿éé¡¹
#éç½®æ ¼å¼è¯´æï¼
#     æ¯ä¸ªåºç¨çéç½®é¡¹éç¨ | é´é
#     æ¯ä¸ªåºç¨éç¨ # é´é
#     å¹³å°è¯´æï¼1 iPhoneç«¯ï¼  2 iPadç«¯ï¼3 Androidç«¯ï¼åå«ææºãå¹³æ¿ï¼
#     æ ·ä¾ï¼å¹³å°1|MasterSecretKey1|AppKey1#å¹³å°2|MasterSecretKey2|AppKey2
#     éç½®æåæå¡åºç¨ä¿¡æ¯
mp.job.push.jPushKey=1|4ae744bd2ea227ce25f51149|6472d7dace866902f97221de#2|1fc3f64c1dfe8545dfd40ee2|b496edffe14a976e10a7907b#3|1fc3f64c1dfe8545dfd40ee2|b496edffe14a976e10a7907b
#æ¶æ¯æ¨éæéåå®¹ï¼æ ¼å¼å¦"[åå·¥å]åèµ·çå³äº[æµç¨å]çå³ç­äºé¡¹ï¼è¯·æ¨å®¡æ¹ï¼",å¶ä¸­"åå·¥å"ä½¿ç¨ emp_nameä»£æ¿,"æµç¨å"ä½¿ç¨nameä»£æ¿
#mp.job.push.alertMsg=[emp_name]åèµ·çå³äº[name]çå³ç­äºé¡¹ï¼è¯·æ¨å®¡æ¹ï¼
mp.job.push.alertMsg=\u3010emp_name\u3011\u53d1\u8d77\u7684\u5173\u4e8e[name]\u7684\u51b3\u7b56\u4e8b\u9879\uff0c\u8bf7\u60a8\u5ba1\u6279\uff01
#iOSæ¨éAPNSç¯å¢ï¼true çæç¯å¢ï¼false æµè¯ç¯å¢ï¼é»è®¤"true"
mp.job.push.apnsProduction=true
#æ¶æ¯æ¨éæ ç­¾ï¼ç¨æ¥åºåä¸åç³»ç»æ¨éï¼é»è®¤"DOA"
mp.job.push.pushTag=DOA
#Androidæç¤ºæ é¢ï¼é»è®¤"MPå¹³å°"
mp.job.push.androidTitle=MP-JAVA-Mysql
#æ¶æ¯æ¨éæ¶é´é´é
mp.job.push.pushInterval=0 0/2 * * * ?
#éç½®æç¤ºè¯­çæå¤§åè®¸é¿åº¦ï¼åä½å­ç¬¦é¿åº¦
mp.job.push.msgLength=40
#ä»»å¡å è½½çå¤é¨jarï¼èææºä¸ç¨
#ä»¥æ¯ä¸ªjarä»¥fileåè®®(Windowsä¸ä»¥file:/)å¼å§ï¼å¤ä¸ªjarä¹é´ç¨,åé
mp.job.external.jars=file:/home/<USER>/mpjob/ly.mp.project.job.sample.jar
#æå¡å¯å¨æ¶å¯ç¨æå¡çidï¼å¤ä¸ªidä¹é´ç¨,åé
mp.job.startJobs=082e2784cc4545c081b73e433efb03d1,102039409a7546f990ae25019f6d25ad,1e9380a928dc4bbd9e610c2e58a0fc6f,23afed65848b4c26bca1e5b583065679,33d4988629ce40e5acf9827119156d23,4917be7d0f1e418ead9ec02dac94ff01,4a32961509b645588cbf5b9a36c2cb4a,604e960fb41248979c2a6514a2385f1d,6e5d92e12e964c45ac1a1e484c8f83c8,763cec5e05164061a899c1f4b5bdaf65,78b6bbac36564d0eb50af83ef283d6c7,9943c67af06e470ead7de0ec4f0455a8,a54e39253b0640cfa509c15c23a903f5,b04318f3f3ff4f1aa34ff8a64c7de91c,b25b26a6064048d2ba1b9982e2419dfc

#==============================================================
#Configure Main Scheduler Properties  
#==============================================================
#éç½®éç¾¤æ¶ï¼quartzè°åº¦å¨çidï¼ç±äºéç½®éç¾¤æ¶ï¼åªæä¸ä¸ªè°åº¦å¨ï¼å¿é¡»ä¿è¯æ¯ä¸ªæå¡å¨è¯¥å¼é½ç¸åï¼å¯ä»¥ä¸ç¨ä¿®æ¹ï¼åªè¦æ¯ä¸ªamsé½ä¸æ ·å°±è¡
org.quartz.scheduler.instanceName=ly-mp-job
#éç¾¤ä¸­æ¯å°æå¡å¨èªå·±çidï¼AUTOè¡¨ç¤ºèªå¨çæï¼æ éä¿®æ¹
org.quartz.scheduler.instanceId=AUTO  
#==============================================================
#Configure ThreadPool  
#==============================================================
#quartzçº¿ç¨æ± çå®ç°ç±»ï¼æ éä¿®æ¹   
org.quartz.threadPool.class=org.quartz.simpl.SimpleThreadPool 
#quartzçº¿ç¨æ± ä¸­çº¿ç¨æ°ï¼å¯æ ¹æ®ä»»å¡æ°éåè´è´£åº¦æ¥è°æ´  
org.quartz.threadPool.threadCount=5   
#quartzçº¿ç¨ä¼åçº§
org.quartz.threadPool.threadPriority=5  
#==============================================================
#Configure JobStore  
#==============================================================
#è¡¨ç¤ºå¦ææä¸ªä»»å¡å°è¾¾æ§è¡æ¶é´ï¼èæ­¤æ¶çº¿ç¨æ± ä¸­æ²¡æå¯ç¨çº¿ç¨æ¶ï¼ä»»å¡ç­å¾çæå¤§æ¶é´ï¼å¦æç­å¾æ¶é´è¶è¿ä¸é¢éç½®çå¼(æ¯«ç§)ï¼æ¬æ¬¡å°±ä¸å¨æ§è¡ï¼èç­å¾ä¸ä¸æ¬¡æ§è¡æ¶é´çå°æ¥ï¼å¯æ ¹æ®ä»»å¡éåè´è´£ç¨åº¦æ¥è°æ´
org.quartz.jobStore.misfireThreshold=60000   
#å®ç°éç¾¤æ¶ï¼ä»»å¡çå­å¨å®ç°æ¹å¼ï¼org.quartz.impl.jdbcjobstore.JobStoreTXè¡¨ç¤ºæ°æ®åºå­å¨ï¼æ éä¿®æ¹ï¼ï¼org.quartz.simpl.RAMJobStoreå­æ¾å¨åå­ï¼éå¯ä¼ä¸¢å¤±ï¼æ­¤ç§æ¹å¼éå°åé¢çéç½®å é¤ï¼
org.quartz.jobStore.class=org.quartz.impl.jdbcjobstore.JobStoreTX 
#quartzå­å¨ä»»å¡ç¸å³æ°æ®çè¡¨çåç¼ï¼æ éä¿®æ¹ 
org.quartz.jobStore.tablePrefix=qrtz_ 
#è¿æ¥æ°æ®åºæ°æ®æºåç§°ï¼ä¸ä¸é¢éç½®ä¸­org.quartz.dataSource.myDSçmyDSä¸è´å³å¯ï¼å¯ä»¥æ éä¿®æ¹  
org.quartz.jobStore.dataSource=myDS
#æ¯å¦å¯ç¨éç¾¤ï¼å¯ç¨ï¼æ¹ä¸ºtrue,æ³¨æï¼å¯ç¨éç¾¤åï¼å¿é¡»éç½®ä¸é¢çæ°æ®æºï¼å¦åquartzè°åº¦å¨ä¼åå§åå¤±è´¥   
org.quartz.jobStore.isClustered=true  
#éç¾¤ä¸­æå¡å¨ç¸äºæ£æµé´é(æ¯«ç§)ï¼æ¯å°æå¡å¨é½ä¼æç§ä¸é¢éç½®çæ¶é´é´éå¾æå¡å¨ä¸­æ´æ°èªå·±çç¶æï¼å¦ææå°æå¡å¨è¶è¿ä»¥ä¸æ¶é´æ²¡æcheckinï¼è°åº¦å¨å°±ä¼è®¤ä¸ºè¯¥å°æå¡å¨å·²ç»downæï¼ä¸ä¼ååéä»»å¡ç»è¯¥å°æå¡å¨
org.quartz.jobStore.clusterCheckinInterval=20000
#==============================================================
#Non-Managed Configure Datasource  
#==============================================================

org.quartz.dataSource.myDS.maxConnections=10

spring.application.name=ly-mp-allinone-cloud