#æ ¹æ®ä½¿ç¨ä¸åçæ°æ®åºï¼æ«æä¸å°çDALå,å¤ä¸ªä»¥","éå·åé;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver
write.mp.jdbc.packagescan=com.ly.mp.**.mysql
#mpçæ°æ®åº(ä¸»åº)äºå¡ç­ç¥ æ®é: normal å¤åº(jta) : jta  å¤æå¡:tcc
write.mp.jdbc.transactionPolicy=normal

#æ²¡æéç½®è¿éç½®é¡¹ä¸å½±ååçº§,ä½ç¨æ¯è¿åçé»è®¤éåæ¥è¯¢ç»æè½¬æå¤§å.æ²¡æéç½®æèé»è®¤æ¯true:keyè½¬æå¤§å,false:keyä¸ä½è½¬æ¢
write.mp.jdbc.upperCaseColumn=true

write.mp.jdbc.name=mysql_mp
write.mp.jdbc.url=***************************************************************************************************************
write.mp.jdbc.username=XX
write.mp.jdbc.password=XX

#oracleéç½®ç¤ºä¾
#write.mp.jdbc.name=oracle_mp
#write.mp.jdbc.url=*****************************************
#write.mp.jdbc.username=XX
#write.mp.jdbc.password=XX

#sqlserveréç½®ç¤ºä¾
#write.mp.jdbc.name=sqlserver_mp
#write.mp.jdbc.url=***********************************************
#write.mp.jdbc.username=XX
#write.mp.jdbc.password=XX

mp.read.db.size=0

#druid datasource
#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE
druid.initialSize=3
druid.minIdle=3
druid.maxActive=20
druid.maxWait=60000
druid.timeBetweenEvictionRunsMillis=60000
druid.minEvictableIdleTimeMillis=300000
druid.validationQuery=select 1 from dual
druid.testWhileIdle=true
druid.testOnBorrow=false
druid.testOnReturn=false
druid.poolPreparedStatements=false
druid.maxPoolPreparedStatementPerConnectionSize=20
#druid.keepAlive=true
druid.phyTimeoutMillis=1200000
#wall,slf4j,stat
druid.filters=stat
#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3


#mp2.24å¢ééç½®
#mybatis
mybatis-plus.mapperLocations=classpath:/mapper/*Mapper.xml
#å®ä½æ«æï¼å¤ä¸ªpackageç¨éå·æèåå·åé
mybatis-plus.typeAliasesPackage=com.ly.mp.test.entity
mybatis-plus.typeEnumsPackage=com.ly.mp.test.entity.enums
#æ°æ®åºç¸å³éç½®
#ä¸»é®ç±»å  AUTO:"æ°æ®åºIDèªå¢", INPUT:"ç¨æ·è¾å¥ID",ID_WORKER:"å¨å±å¯ä¸ID (æ°å­ç±»åå¯ä¸ID)", UUID:"å¨å±å¯ä¸ID UUID";
mybatis-plus.global-config.db-config.id-type=UUID
#å­æ®µç­ç¥ IGNORED:"å¿½ç¥å¤æ­",NOT_NULL:"é NULL å¤æ­"),NOT_EMPTY:"éç©ºå¤æ­"
mybatis-plus.global-config.db-config.field-strategy=not_empty
#é©¼å³°ä¸åçº¿è½¬æ¢
mybatis-plus.global-config.db-config.column-underline=true
#æ°æ®åºå¤§åä¸åçº¿è½¬æ¢
#capital-mode: true
#é»è¾å é¤éç½®
mybatis-plus.global-config.db-config.logic-delete-value=0
mybatis-plus.global-config.db-config.logic-not-delete-value=1
#mybatis-plus.global-config.db-config.db-type=sqlserver
#å·æ°mapper è°è¯ç¥å¨
mybatis-plus.global-config.refresh=true
# åçéç½®
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false

#Rediséç½®
#å¤ä¸ªserversç¨éå·(",")éå¼,ä¸éè¦éç½®redisä»æºçIP,åªéè¦redisä¸»æºIP
#sentinelæ¨¡å¼çæ ¼å¼masterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,ä¾å¦mymaster?**************:63793,**************:63794
#å¼å
redis.session.servers=**************:6379
redis.session.password=mp123
redis.servers=**************:6379
#rediså¯ç ,ææredisæå¡å¯ç å¿é¡»ä¸æ ·
redis.password=mp123
#æå¤§è¿æ¥çº¿ç¨æ°
redis.pool.maxActive=10000
#è¿æ¥è¶æ¶æ¶é´(åä½:ç§)
redis.pool.timeout=3000
#ç¼å­æ¶é´(åä½:ç§)
redis.pool.expires=86400
#å¨è·åä¸ä¸ªjediså®ä¾æ¶ï¼æ¯å¦æåè¿è¡alidateæä½ï¼å¦æä¸ºtrueï¼åå¾å°çjediså®ä¾åæ¯å¯ç¨çï¼
redis.pool.testOnBorrow=true
#å¨returnç»poolæ¶ï¼æ¯å¦æåè¿è¡validateæä½ï¼
redis.pool.testOnReturn=true

#session time out (åä½:ç§)
session.timeout=1800

#é»è®¤ä¸ºfalseï¼ä¸åæ¥å¿ï¼true åæ¥å¿
#ç¨åºè¿è¡æ¥å¿
RunLog=true
#æå¡è°ç¨æ¥å¿
InvokingLog=true
#ä¸å¡æ°æ®æ¥å¿
BssLog=true

#éè¿MQè½¬åè®°å½æ¥å¿
# NONE:ä¸åæ¥å¿ï¼MQ:åMQï¼FILE:åæä»¶ï¼ALL:æææ¹å¼ï¼MQåæä»¶ï¼ï¼FILEæ¹å¼æ¶ï¼å¹³å°çæ¥å¿åæåè½æ æ³ä½¿ç¨
logStorageType=MQ

# æ¥å¿å½æ¡£
# æ¯å¦å¯ç¨æ¥å¿å½æ¡£
log.job.filing.enable=true
# å½æ¡£å¤ä¹ä¹åçæ°æ®
log.job.filing.interval=30
# å½æ¡£è§¦åçæ¶æºï¼æ¯æ1å·00:04:00
log.job.filing.cron=0 4 0 1 * ?
# æå¡è°ç¨æ¥å¿æåè½ç»è®¡ä½ä¸ï¼æ¯é2å°æ¶è·ä¸æ¬¡
log.job.invoking.func.cron=0 0 0/2 * * ?
# æå¡è°ç¨æ¥å¿æç¨æ·ç»è®¡ä½ä¸ï¼æ¯é2å°æ¶è·ä¸æ¬¡
log.job.invoking.user.cron=0 0 0/2 * * ?
# ç³»ç»è¿è¡æ¥å¿æåè½ç»è®¡ä½ä¸ï¼æ¯é2å°æ¶è·ä¸æ¬¡
log.job.run.func.cron=0 0 0/2 * * ?

#-----------------------------------
#  redissonåå¸å¼ééç½®
# æ¯å¦å¯ç¨åå¸å¼é true:å¯ç¨ false:ä¸å¯ç¨,é»è®¤ä¸å¯ç¨, å¦ææ²¡ç¨å°åå¸å¼é ä¸è¦å¯ç¨
redisson.redis.enable=true
# éç¾¤æ¶ï¼éè¦ææä¸»ä»èç¹å°å
redisson.redis.servers=**************:6379
redisson.redis.password=mp123
# çæ§éççé¨çè¶æ¶ï¼å®æºæè¿ç¨æäºéæ¾éçè¶æ¶æ¶é´ï¼ï¼åä½ï¼æ¯«ç§ãé»è®¤å¼ï¼30000
redisson.redis.lockWatchdogTimeout=20000
# éç¾¤ç¶ææ«æé´éæ¶é´ï¼åä½æ¯æ¯«ç§ãé»è®¤å¼ï¼ 1000
redisson.redis.scanInterval=1000
# å¤ä¸»èç¹çç¯å¢éï¼æ¯ä¸ª ä¸»èç¹çè¿æ¥æ± æå¤§å®¹éãè¿æ¥æ± çè¿æ¥æ°éèªå¨å¼¹æ§ä¼¸ç¼©ãé»è®¤å¼ï¼64
redisson.redis.masterConnectionPoolSize=64
# å¤ä»èç¹çç¯å¢éï¼æ¯ä¸ª ä»æå¡èç¹éç¨äºæ®éæä½ï¼é åå¸åè®¢éï¼è¿æ¥çè¿æ¥æ± æå¤§å®¹éãè¿æ¥æ± çè¿æ¥æ°éèªå¨å¼¹æ§ä¼¸ç¼©ãé»è®¤å¼ï¼64
redisson.redis.slaveConnectionPoolSize=64

#-----------------é®ç®±éç½®-------------------

#å¿éé¡¹
#é®ä»¶æå¡å¨å°å
mp.component.mailServer=mail-hd.dfl.com.cn

#å¿éé¡¹
#é®ä»¶æå¡å¨å°åç«¯å£
mp.component.mailServerPort=25

#å¿éé¡¹
#åéæéé®ä»¶çé®ç®±å¸å·
mp.component.mailAccount=<EMAIL>

#å¿éé¡¹
#åéæéé®ä»¶çé®ç®±å¯ç 
mp.component.mailAccountPWD=XXX

#åéæéé®ä»¶æ¾ç¤ºçé®ç®±å(è±æ)
mp.component.mailAccountName=MP System Mail
# ç³»ç»ç®¡çåé®ç®±,å¤ä¸ªä½¿ç¨éå·åé
mp.component.adminEmail=<EMAIL>

#-----------------é®ç®±éç½®-------------------

#-----------------æ¾åå¯ç é®ä»¶éç½®-------------------
mp.component.findpwd.title=æ¾åå¯ç é®ä»¶
#ç¬¬ä¸ä¸ªåæ°ä¸ºç¨æ·åï¼ç¬¬äºä¸ªåæ°ä¸ºè®¾ç½®æ°å¯ç çé¾æ¥
mp.component.findpwd.content=<br/>    å°æ¬çç¨æ· {0},æ¨å¥½<br/><br/>æ¨æ¶å°è¿å°çµå­é®ä»¶æ¯å ä¸ºæ¨ç³è¯·äºæ¾åå¯ç ï¼å¦æä¸æ¯æ¨æ¬äººç³è¯·ï¼è¯·ä¸ç¨çä¼è¿å°é®ä»¶ã<br/><br/>        è¯·ç¹å»é¾æ¥  {1} è®¾ç½®æ°å¯ç ã<br/><br/><br/><br/>æ³¨ï¼è¯·æ¨æ¶å°é®ä»¶å¨2å°æ¶åä½¿ç¨ï¼å¦åè¯¥é¾æ¥å¤±æã
#-----------------æ¾åå¯ç é®ä»¶éç½®-------------------

#-----------------MQéç½®--------------------
#æ¯å¦å¯ç¨AMQ(true,false)
mp.component.amqOpen=true
# MQç±»å 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (é»è®¤ä½¿ç¨1:RabbitMQ, å¦ææ²¡æè®¾ç½®amqType, ä¸ºå¼å®¹ä¹åçæ¬ä½¿ç¨ActiveMQ)
mp.component.amqType=3
mp.component.amqUrl=172.26.157.146:9876;172.26.157.147:9876
# MQç«¯å£ï¼åªå¯¹RabbitMQæç¨
mp.component.amqPort=5672
mp.component.amqUser=rocketadmin
mp.component.amqPwd=Mp@2020
#éåï¼ä»¥âéåé®:éåå:éåæ°é;éåé®:éåå:éåæ°éâä¸ºæ ¼å¼ï¼éåæ°éæªéæ¶ï¼é»è®¤ä¸º1ï¼æ³¨ï¼éåé®ä¸ä»£ç ç»å®ï¼ç¡®å®åä¸è½ä¿®æ¹ï¼
mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue

#æ¯å¦å¯ç¨å¾åæ¶æ¯, é»è®¤ä¸ºfalse, å¦ææ²¡ç¨å°å¾åæ¶æ¯, ä¸è¦å¯ç¨
mp.component.pendingMsg=true
#æ¯å¦å¯ç¨å¬åæ¶æ¯, é»è®¤ä¸ºfalse, å¦ææ²¡ç¨å°å¬åæ¶æ¯, ä¸è¦å¯ç¨
mp.component.noticeMsg=true
#æ¯å¦å¯ç¨CCæ¶æ¯, é»è®¤ä¸ºfalse, å¦ææ²¡ç¨å°CCæ¶æ¯, ä¸è¦å¯ç¨
mp.component.ccMsg=false
#-----------------MQéç½®--------------------

#-----------------ç­ä¿¡ç½å³éç½®--------------------
mp.component.smsServerID=************
mp.component.smsLoginName=MP
mp.component.smsLoginPWD=XXX
mp.component.smsServerPort=8003
#-----------------ç­ä¿¡ç½å³éç½®--------------------


#--------------------AdDomainéç½®--------------------
#æ¯å¦å¯ç¨ADå(true,false)
mp.component.adOpen=false

#å¤ä¸ªåï¼adHostãadDomainãadFilterProcãadPortãadUserãadPwdç¨âåå²
#ADåIP
mp.component.adHost=**************â*************

#ADååç¼,å¿é¡»æ¯@xxx.xxx;å¦@lymp.com
mp.component.adDomain=@lymp.comâ@lyeap.lanyou.com

#è¿æ»¤ç»ç»
mp.component.adFilterOrg=

#è·ååæä¸ªå±æ§å­æ®µçåå®¹
#è®¾ç½®åå±æ§å­æ®µåä¸ç¨æ·è¡¨ä¸­çé£ä¸ªå­æ®µå³è
mp.component.adFilterProc=sAMAccountNameâSamAccountName
mp.component.adTableField=user_name

#ADåç«¯å£,é»è®¤ç«¯å£389
mp.component.adPort=389â389

mp.component.adUser=administratorâadministrator

mp.component.adPwd=XXXâXXX

#--------------------AdDomainéç½®--------------------

#--------------------å¹³å°éç½®--------------------
#é¡¹ç®åç§°
mp.component.itemname=MPJAVA
mp.component.itemcode=f0ae9afe071411e8b5fb0050569d5d3a
#æ¯å¦Debugæ¨¡å¼(true æ¯; false å¦), Debugæ¨¡å¼ä¸éè¦Tokenå¼ºå¶éªè¯
mp.component.isdebug=false
#ç½ç«åå(http://172.26.223.68/æhttp://www.qq.com)
mp.component.rootUrl=http://172.26.223.XXX/
#  æ»å¨éªè¯ä½¿ç¨å¾ççå­æ¾ç®å½
mp.component.verifyPicDir=/home/<USER>/verifyPIC
#  é»è®¤ç§æ·id, åç§æ·æ¶è®¾ç½®ä¸ºç§æ·çid(å¼å®¹æ§çæ¬), å¤ç§æ·æ¶ä¸è¦è®¾ç½®é»è®¤ç§æ·id
mp.component.defaultTenancyId=
#--------------------å¹³å°éç½®--------------------

#-----------------çæ§å¹³å°éç½®--------------------
#æ¯å¦å¼å¯çæ§è·è¸ªæ¥å¿
mp.component.traceOpen=false
#æ¯å¦å¼å¯SQLçæ§è·è¸ªæ¥å¿
mp.component.traceSqlOpen=false
#å°äºèæ¶éå¼çä¸è®°å½(åä½:æ¯«ç§)
mp.component.traceThreshold=10
#çæ§URLå°å
mp.component.traceUrl=http://172.26.165.242:18000/mc/api/sendv10
#-----------------çæ§å¹³å°éç½®--------------------
# cc notify url
mp.component.cc.notifyUrl=http://testroute.szlanyou.com/msgpush_mp2.cgi
mp.component.cc.sysname=sys_mp2.1_mysql



#ç¨äºæ è®°MP å¾ååè¡¨ãå·²ååè¡¨æ¯å¦åæ¶æ¾ç¤ºç³»ç»å¾åæ ç­¾ï¼å¦ãå®¡æ¹ãããé©³åãããæéãããï¼
#é»è®¤ä¸ºfalseï¼ä¸åæ¶ï¼æ è®°ä¸ºtrueçæ¶ååæ¶
#pendingtitle_no_des=false
mp.wfengine.pendingTitleNoDes=true

#å·¥ä½æµèç¹åå§ååè°ç¨çå½æ°
#--------------------------------------paras-----------------------
# node_guid, node_from, node_type, autor_guid, 
# autor_name, Pend_Autor, gather_pending_id, gather_value, 
# orderauditor_delaytime, step_path_guid, step_guid, pending_type,
# extProV1, business_no, last_handler
#----------------------------------------------
mp.component.wfengine.onPendingCreatedInsert=
mp.component.wfengine.onPendingCreatedUpdate=

#å·¥ä½æµèç¹å®æåè°ç¨çå½æ° 
#--------------------------------------paras-----------------------
# node_guid, node_from, node_type, autor_guid, 
# autor_name, Pend_Autor, gather_pending_id, gather_value, 
# orderauditor_delaytime, step_path_guid, step_guid, pending_type,
# extProV1, business_no, last_handler
#----------------------------------------------
mp.component.wfengine.onNodeClosedInsert=
mp.component.wfengine.onNodeClosedUpdate=


#-----------------å¾®ä¿¡å¬ä¼å¹³å°éç½®--------------------
mp.component.wxgzAppID=wxa29f9b6dc75ece95
mp.component.wxgzAppSecret=4f53a83c5195359428942cf56563c764
mp.component.wxgzToken=szmplanyou
mp.component.wxgzReply=æ¬¢è¿å³æ³¨èåMPå¬ä¼å·
mp.component.wxgzLoginCallUrl=https://XXX.XXX/mp/login/wxgzlogincallback.do
mp.component.wxgzResponseUrl=https://mdp1.szlanyou.com/wxweb/wxindex.html
#-----------------å¾®ä¿¡å¬ä¼å¹³å°éç½®--------------------

#-----------------ä¼ä¸å¾®ä¿¡å¬ä¼å¹³å°éç½®--------------------
mp.component.ewxgzAppID=wwde346d80e24185f9
mp.component.ewxgzAppSecret=P2zzu7ngoSGjClmyvrVFSPHoJduMHY1-Dz8fC9HJz38
mp.component.ewxgzBindSecret=YjYvnd_9VUVW-vQmhG2XKI5CCCmNW1huA2fuSqt5MSk
mp.component.ewxgzToken=szmplanyou
mp.component.ewxgzLoginCallUrl=http://web01.dawnpro.cc/mp/login/ewxgzlogincallback.do
mp.component.ewxgzResponseUrl=http://web01.dawnpro.cc/wxweb/wxindex.html
mp.component.ewxgzBindResponseUrl=http://web01.dawnpro.cc/wxweb/wxbindindex.html
#-----------------ä¼ä¸å¾®ä¿¡å¬ä¼å¹³å°éç½®--------------------

#--------------------äººè¸è¯å«éç½®--------------------
mp.component.facecompareURL=http://XXX.XXX:10001/face/recog/group/compare
mp.component.faceQueryURL=http://XXX.XXX:10001/face/clustering/face/query
mp.component.faceCreateURL=http://XXX.XXX:10001/face/clustering/face/create
mp.component.faceEditUrl=http://XXX.XXX:10001/face/clustering/face/edit
mp.component.faceDelUrl=http://XXX.XXX:10001/face/clustering/face/delete
mp.component.faceGroup=LYAI
mp.component.faceScore=0.9
mp.component.faceNums=5
#å·è¸ç»å½æ¶ï¼0å³é­ï¼1æ»æåï¼2æ»å¤±è´¥ï¼
mp.component.faceTestModule=1
#--------------------äººè¸è¯å«éç½®--------------------

#Alipay äºç»´ç æ¯ä»ä¸æ¡ç æ¯ä»
mp.pay.alipay.qr.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.alipay.qr.support[0]=true
mp.pay.alipay.qr.alipayPublicKey[0]=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgR4kDsDe2A5ISVrqHZKj0SHm3iPGiw5P3LwEu1Rn4kSVAodRgcAgTr7jL7nAj8LF5f2Ow4Xk7H4OIecngOSZLOce/xXz/PIsUxuoQeIAox7pTuK9swTr7sZFl0COXHTSm5BSadR5jWkdQNeqTjZUxWIrrhZd2kTVUlzJhRbwkCACfAXWdS0lLDOGjcYfbOyo0zC8O/njG3e+MmU0eErQyQ+x0wdffTNHfJ2y5PvIrm988+pZvdu8uJzQTUpxjFmwNripI/Bjthng+8MBfPAI7/Zk9/hfn3RtMm1E2pu/btu6BjYkffp2D3oEcUEYTIyB0FInDgrYYLdewYyqmX2IjQIDAQAB
mp.pay.alipay.qr.appId[0]=2017110109661663
mp.pay.alipay.qr.privateKey[0]=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCkDLs+MtzdX4ptcaZ+E59Wt670Jtsr7vuiWFV/ZUQ4MmD0yaPOCglfgixCSCiHzqFmRn4+VI69+YFMdI7lCv0e9xNCmQjgN4lmow9PlIeOsXO8Ka8QKxVhYULfsZRdp6fF5SS0lkm/kmHOkIDCP8Tlipe1AA5xZhKqIhx7fPPY3Y64MehD7vqc8d8E7p9DykveX92DcYZOwYk/9GWkSlmiMa0uWbv/9e1pQaSy2l4iRWfxTsxLtq3FC1Zc/kJW2aEAXFZo3bftYfQY8BoVhVURk8b7ZY2VvV+osAYFpCAlra4m7UjGiBLqT7gsrW3EA1lm8F5YIAYhZKclmXtVJztzAgMBAAECggEAW/I20Dm5yOnPsF/OrUNaP0RcbsOIfCtKJkfUQ78CaWzzIsARa138uuc+3zeKX/PUSnqgL1c9WgUKD0wU+xMZo81foigb7W+zNy+VWUkqYTPcZk4GrM05Aod879ucCJH7WtN/qyfA1fq5jwk24ajUNsNjHDOX5L8NHwZXaG80TQubRUAIZoljCH+/WVA1IH6lFAhceQOTVRGhaCOFxDJgW5XgKL8ki5pzvf3D3Z25cor2ixkAwDeaQnedJvPrvfGg5XGebRAd9HQb2bA+/mlCFKesPTEiJk8jX39wCIqdhS+RJZtNVRqHPl0pWqTi+RxdMzLXjrVxdQSYyxX0ZPf2QQKBgQDNpeX8RtyY57Fev6bsW3Ne8nKjvyfc5FBx7e3krcC/JCWLI1YndjIVNHyOHn4V+3rwk7QS8BwU9NJaPHuuk92AV2RysDfvlfbmD8DxFw5uNaD+ByWlKPSf/vjWdORLRf4diogP4DYTaZcH/7NpnvuiyQae6buU93cxbAGaYnl5qwKBgQDMN280D+eUIB2EBtZcAhmclmqBCkm+NT6diencNVhBmtt14WzrsTgD2/9BfMr2YnlVuxvo77cSEQZjwg2jxz2XnsaE6zMoH/uug8LoGrPlAYPhtBDa6H75C2gvF3acT78FTGIw5V+Zk3rw9u8pGK9aZpqHPz6OI6suo6mOhXrNWQKBgCDuo0DeHC+EUvwoVtPc5UHcM42TbA/MpDFKd+E6DfbOFBEPDJnvLKAGsreTnH9qsUpbbOfneafFePYoX2oalcsas6RGIf8FFe/LsAsrtQzjG6/ydw3W3C3PCAxX1cNUJxiV+aoJLr+3Fg+a3CFa61MrPBswtPBrHLRWZn9Rq8BXAoGBAJY6VWj0JkS2V2Avc3Od699gW5rvyY3ON3DG6q2e5HzmgXk3Stwbs3xLU3yGY/xaNq4VhhOWfJMiyROLxmLsB+hI2fsf1rM2y/v2W+RI+HuH4M+hmiCflgB5Hrw9w3h7xacNKNKVef1NG8y1qvwNd7nF7vl9UfRYUu5tYSdCFDOJAoGAH3Qv4LS7hPJY7w+s7SFA+9nMdCxRhGlKGZpkBfzdG1sPZb1EVGnRYHl5o1c23hC9rJG5YT+RNTpuev8pBqC8Sbv0sK8bl5cKxb2M4J3xNDfUt9UvoX3k4PtYGqWHkc7s6FSGVcadm3RUDg1e0vl04LVs02FZrdDS83LJZoNb8IA=

#Alipay app pay properties
mp.pay.alipay.app.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.alipay.app.support[0]=true
mp.pay.alipay.app.timeoutExpress[0]=30m
mp.pay.alipay.app.appId[0]=2017020705549022
mp.pay.alipay.app.privateKey[0]=MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCIqo9sVoyc4XuTyZjnOqGQxDqlmtZd8FIs3RFpdc6V2/P+1WjGKa0ALGb5ds4+Ai13rxvk2qfz6N+0jkTlbdxdlFPUpJOukkhDvip2JB4jBv5AVj6iZNCwTvXC4bt1VNqdXWJPKJW3iMZtwM5q+JR9Mm8rHh5peRORTHEYYCLjhp5yT/Qd/FQjnrIaCQ6ZlAWqH1O6IjYA2egi2lK+BioaOSSfSf0lDRu91f8/zw3AaUgbNeDpCTVl4niMU3FMwAwIeNd8JMjedpl6YsOLi3Ozc3UqwWAJRWQ+Y5AZIBnBqTnnT6qJbcvwrY3WWcEWN7VB+kabC9roD2LZcR1h1U0fAgMBAAECggEAAgmQ48uSgxA1eGmL9v5/xBm4yhk50uKvRu3wwYK5FrDVvVnPGw0qABOpYVStFG2R4fPhVZMQ5+jz1Mw+KLFvlJGOgmAFt5eErkXaejJlpJ4JEkp22pLoLYivzzzwP3qJW8k1mvp+6OKIGsDCEwQrqoYi9CCZ0Xa1nPfnw7ZDLuMgPACWQ75fPUngnRbB7Uwty131NROK6FNt7/16ByQipevTMuhcOO9eF0s4XcoubStg0w5uL03UfIZ8x4ldD0O78Dafd2SeSRae45nVMdpoefI0GNnY+gRNOyhK/+S6akXaXINRxTDLypfA3gQ6XEzufuT8x7D2XypTHfjuaLRkCQKBgQDZGL96IlZQzYRHJWW6SViDbcJHbB4V3Oy+kpSoIq3SNrOpvkApE3/tHtxJxzSWVFt+5tlSTz5IyR4Ae1wWDxR5A46LQqYFCENGbdG1nz3djqTahuhhwGtN3PMyTgV8MqFY2vQv+oHJJsjVaf9pCD55g76aekhIk3QFOfzNBKLmMwKBgQChKBaQbj/TvIZl/GbMC43C0DYeHRUpWIFmgX15LxXYAUS6XAtd0TNM18DwKZcA5OY4tsi1+W2ynIMk/198naBBBMSmW4lUjWG1g1Zp+ghTO/MDsswy/MqQGkra/kU+tG4qUeDGrKUBo36r6AEn9xnHL9N4l8qhO4gp7QBl8hSZZQKBgC4pK/WdPXs658h09DdzBwYTHX8wRwlhC1nOMxu5G/qZtQP/twbE5auWp0JswArC7x2Bmm38+YJieSWjFUZ/eFvu3K1Rw5lIU32zNicHMBFfFkB89QZr8qUAuRlWK6Zn4ZTSIZ/eBSCvRX7TZgKARUBzOeEA5UPBTqcZ2F4DgVuvAoGAbXkU8uHhu8y6I3d0wTEsCGV2DbjF0kNMC7z05ihFF2mtLUcvdXiR96YsazhlWncjqO0JpQweJ5HISI6tZ7KP1PsPNs7BmE0+TZY9UlpF43y61Q1VR2GPnJovtVm64iChIWBjZ7KJmHZeqxo8BtEFkth7N9UtEZ+mHIzhk18B/T0CgYB2or6GPH+ZrnHvIHqQGh0a1vaBGuiUTwGIWPYHJSI92YsXQ2+pDodq/gM+aM99qtmEEJjsTn7iV446sds8LF8tcbS2QJucDltdvTm1dh8qU9ESHJU79wzaRFk+IKdieWNqD11kaidRNSz0i+BSgh6Y79jRmwiJ0G4rYRZpKdiTEA==
mp.pay.alipay.app.alipayPublicKey[0]=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgR4kDsDe2A5ISVrqHZKj0SHm3iPGiw5P3LwEu1Rn4kSVAodRgcAgTr7jL7nAj8LF5f2Ow4Xk7H4OIecngOSZLOce/xXz/PIsUxuoQeIAox7pTuK9swTr7sZFl0COXHTSm5BSadR5jWkdQNeqTjZUxWIrrhZd2kTVUlzJhRbwkCACfAXWdS0lLDOGjcYfbOyo0zC8O/njG3e+MmU0eErQyQ+x0wdffTNHfJ2y5PvIrm988+pZvdu8uJzQTUpxjFmwNripI/Bjthng+8MBfPAI7/Zk9/hfn3RtMm1E2pu/btu6BjYkffp2D3oEcUEYTIyB0FInDgrYYLdewYyqmX2IjQIDAQAB

#Weixin æ«ç ä¸æ¡ç æ¯ä»
mp.pay.weixin.qr.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.weixin.qr.support[0]=true
mp.pay.weixin.qr.appid[0]=wxa29f9b6dc75ece95
mp.pay.weixin.qr.mch_id[0]=1491587722
mp.pay.weixin.qr.certLocalPath[0]=/home/<USER>/certs/weixinqr_cert.p12
mp.pay.weixin.qr.certPassword[0]=1491587722
mp.pay.weixin.qr.key[0]=D15AF265019C48cc8D3F307C30E532DB
mp.pay.weixin.qr.spbill_create_ip[0]=**************

#Weixin app pay properties
mp.pay.weixin.app.tenancyId[0]=a3d32304-cc29-aa3a-982c-28be156444e9
mp.pay.weixin.app.support[0]=true
mp.pay.weixin.app.appid[0]=wxccf7dc136735ee23
mp.pay.weixin.app.mch_id[0]=1464996002
mp.pay.weixin.app.certLocalPath[0]=/home/<USER>/certs/weixinapp_cert.p12
mp.pay.weixin.app.certPassword[0]=1464996002
mp.pay.weixin.app.key[0]=D15AF265019C48cc8D3F307C30E532DB
mp.pay.weixin.app.spbill_create_ip[0]=**************
mp.pay.weixin.app.gateway[0]=http://mdp1.szlanyou.com:8083/mp/pay/weixin/app/gateway.do

#------------------æä»¶ä¸ä¼ éç½®--------------------
#æ¯å¦ä½¿ç¨nfsä¿å­ä¸ä¼ æä»¶,é»è®¤ä¸ºfalse
mp.component.useNfs=false
#mount nfså½ä»¤ 
mp.component.mountNfsCmd=mount -t nfs *************:/springboot/upload /springboot/fileupload/upload -o nolock,rsize=1024,wsize=1024,timeo=15
#ä¸ä¼ æä»¶ç±»å
mp.component.uploadFileFormat=doc|docx|xlsx|xls|pptx|ppt|txt|rar|zip|7z|jpg|png|gif|ico|icon|jpeg|bmp|xml|pdf|apk|aip|mp3|mp4|

#ä¸ä¼ æä»¶ä¿å­è·¯å¾
mp.component.uploadFilePath=upload
#ä¸ä¼ æä»¶ä¿å­æ¨¡å¼, 1 ç±»å«/æ¥æ(é»è®¤æ¹å¼) 2 æ¥æ/ç±»å«
mp.component.uploadSaveMode=1

# æ¯å¦å¯ç¨æ°´å°
mp.component.watermarkEnable=true
# éè¦æ·»å æ°´å°çè·¯å¾, é»è®¤ææè·¯å¾ã å¦åªéè¦å¨å¬åçæä»¶æ·»å æ°´å°ï¼è·¯å¾ä¸ºï¼mp/file/notice/upload.doï¼åéç½®mp.component.watermarkCategories=notice
mp.component.watermarkCategories=notice
# éè¦æ·»å æ°´å°çæä»¶ç±»å, å°å
mp.component.watermarkFileTypes=pdf,docx,xlsx,png,jpg,gif,bmp
# pdfæ°´å°å­ä½æä»¶è·¯å¾ï¼ï¼pdfboxé»è®¤ä¸æ¯æä¸­æï¼æ°´å°åå«ä¸­ææ¶ï¼éè¦éç½®åå«ä¸­æçå­ä½è·¯å¾ï¼
mp.component.watermarkPdfFont=
# æ°´å°å­ä½åç§°(å¦ï¼å®ä½), pdfä¸åæ­¤å½±å
mp.component.watermarkWordFont=å®ä½
# æ°´å°åå®¹
mp.component.watermarkContent=mp_watermark
# æ°´å°é¢è²
mp.component.watermarkColor=#aaaaaa
# æ°´å°å­ä½å¤§å°ï¼åä½ pt
mp.component.watermarkFontSize=30
# æ°´å°æè½¬è§åº¦ï¼éæ¶éï¼
mp.component.watermarkRotate=45
# æ°´å°éæåº¦
mp.component.watermarkAlpha=0.5
# æ°´å°é´çæ¨ªåé´éï¼åç´ ï¼
mp.component.watermarkSpaceX=200
# æ°´å°é´ççºµåé´éï¼åç´ ï¼
mp.component.watermarkSpaceY=100
# æ¯å¦éå¶excelåå®¹ï¼æ»è¡æ°ãæ»ååæ ¼æ°ï¼ï¼ç±äºpoiçéå¶ï¼å è½½exceléè¦å¤§éåå­ï¼å½excelä¸­åå®¹è¿å¤æ¶ï¼å®¹æå¯¼è´oom
mp.component.watermarkExcelLimit=true
# excelæä»¶çæå¤§sheetæ°
mp.component.watermarkExcelMaxSheets=100
# excelæä»¶çæå¤§æ»è¡æ°
mp.component.watermarkExcelMaxRows=5000
# excelæä»¶çæå¤§æ»ååæ ¼æ°
mp.component.watermarkExcelMaxCells=100000

#
#-----------------ç¨æ·é£é©éç½®--------------------
# æ£æ¥å¨çº¿ç¨æ·ç¶æä½ä¸
mp.component.onlineUser.checkCron=0 0/10 * * * ?
# åé¤ç¨æ·æä½åå¤å°åéä¹åçæ­£åé¤ï¼æåéç¥ç¨æ·ä¿å­
mp.component.onlineUser.timesInMinuteBerforeOffline=5
# æå¤§åæ¶å¨çº¿ç¨æ·æ°éï¼è¶è¿ç¦æ­¢å¶ä»ç¨æ·ç»å½ï¼0è¡¨ç¤ºä¸éå¶ï¼ç®¡çåä¸åéå¶ï¼é»è®¤å¼ä¸º0ï¼
mp.component.onlineUser.maxOnlineUserNumbers=0
#æ¯å¦ä¸ä¸ªè´¦å·åªè½åæ¶ç»å½ä¸å°çµè
mp.component.kickUser=true
#åè®¸ä¸ä¸ªè´¦å·åæ¶ç»éå å°çµèï¼mp.component.kickUser=trueæ¬è®¾ç½®çæï¼å¤§äº0æ­£æ´æ°ä¸ºææè®¾ç½®ï¼é»è®¤ä¸º1ï¼
mp.component.onlineUser.oneUserMaxOnlineNumbers=2

# IPå°åè§£æå¼å³
mp.component.risk.geoIP2Db=true
# GeoIP2æ°æ®åºæ¬å°å°åï¼æ ¹æ®IPè·åæ°æ®åºè¯¦ç»å°åæ°æ®åºçæ¬å°å°å
mp.component.risk.geoIP2DbPath=/home/<USER>/GeoLite2-City.mmdb
# ipå°åæ°ï¼è¶è¿è¯¥æ°å¼åè§ä¸ºæå¼å°ç»å½çé£é©ï¼å°äºè¯¥æ°å¼ååæipæ¯å¦å¨å¤ä¸ªåå¸ï¼æ¯åå­å¨é£é©
mp.component.risk.numOfIps=5
# apiè°ç¨æ¬¡æ°ï¼å½ä¸ä¸ªç¨æ·çapiè°ç¨æ¬¡æ°å¤§äºè¯¥å¼ï¼å¹¶ä¸æ»¡è¶³mp.component.risk.frequencyæ¡ä»¶æ¶ï¼æè§ä¸ºæapié£é©
mp.component.risk.numOfApi=50
# æ¯åéè°ç¨æ¬¡æ°ï¼å¤§äºè¯¥é¢çåè§ä¸ºç¨æ·è°ç¨APIæé£é©ï¼å¿é¡»åæ¶æ»¡è¶³mp.component.risk.numOfApiï¼
mp.component.risk.frequency=30
# æ¯å¦ç¨æ·é£é©åæä½ä¸
mp.component.risk.jobEnable=false
# è·ä½ä¸çæ¶é´
mp.component.risk.jobCron=0 0 4 * * ?
# é£é©éç¥é®ä»¶æ é¢
mp.component.risk.title=MPJAVAç³»ç»ç¨æ·å¸å·é£é©éç¥
# é£é©éç¥ç®¡çåé®ç®±
mp.component.risk.emails=<EMAIL>
# é£é©éç¥ç®¡çåææº,å¿é¡»çå®ææº
mp.component.risk.mobile=XXX
# é£é©éç¥ç®¡çåé®ä»¶åå®¹
mp.component.risk.adminContent={0}ç¨æ·é£é©åæå¦ä¸ï¼{1}
# é£é©éç¥ä¸ªäººé®ä»¶åå®¹
mp.component.risk.accountContent={0}æ¨çå¸å·[{1}]å­å¨é£é©ï¼è¯·åæ¶ä¿®æ¹å¯ç ï¼å­å¨é£é©ï¼{2}

#-----------------ç³»ç»æ£æ¥é®é¢éç¥é®ä»¶--------------------
mp.component.checkCron=0 0/10 * * * ?
mp.component.checkNoticeEnabled=false
mp.component.checkNoticeEmails=<EMAIL>
#licenseå°æåå¤å°å¤©å¼å§æé,é»è®¤60
mp.component.license.advanceDays=60
#æ¥æ¶licenseå°ææéé®ç®±å¸å·,å¤ä¸ªä½¿ç¨éå·åé
mp.component.license.emails=<EMAIL>
#æ¥æ¶licenseå°ææéææºå·,å¤ä¸ªä½¿ç¨éå·åé,å¿é¡»çå®ææº
mp.component.license.mobiles=***********
#licenseå°ææéé®ä»¶æ é¢
mp.component.license.title=MPJAVAç³»ç»Licenseå°ææé
#licenseå°ææéåå®¹æ¨¡æ¿
mp.component.license.content={0}æ¨çç³»ç»License{1}ï¼è¯·åæ¶æ´æ°License!

#-----------------APIæéæ¦æª--------------------
#æ¯å¦å¯ç¨APIæ°æ®éé,ä»ç¨äºé¡¹ç®å¼åãæµè¯é¶æ®µä½ä¸ºæ°æ®ééè¾å©ï¼ç³»ç»æ­£å¼ä¸çº¿å¿é¡»å³é­æ­¤åè½
mp.component.apipurview.dataCollection=false
#æ¯å¦å¯ç¨
mp.component.apipurview.enable=true
#éè¦æé¤apiæéæ£æ¥çurlå°å(å¤ä¸ªå°åç¨,åé)
mp.component.apipurview.exclude=/mp/org/user/getUserInfo.do,/mp/wfdesign/manager/navigation.do,mp/wfdesign/wfPermissionCtrl/getProcessCreateUserList.do,/mp/framework/usergzscan.do,/mp/framework/wxunbind.do,/mp/org/role/search.do,/mp/org/relation/list.do,/mp/org/user/select.do,/mp/notice/unreadnum.do,/mp/file/*,/mp/framework/CustomMenuBatch.do,/mp/login/*,/mp/api/*,/mp/wfengine/*,/mp/test/*,/mp/file/uploadRichText.do,/mp/log/GetNavigationLog.do,/mp/framework/face/*,/mp/file/delfile.do,/mp/framework/modifyPassword.do,/mp/framework/sysetsave.do,/mp/framework/sysetload.do,/mp/notice/detail.do,/mp/notice/list.do,/mp/framework/getMyMenuTree.do,/mp/org/userPurview/getCtrlPriv.do,/mp/framework/getWebFormListPaged.do,/mp/framework/getloginpage.do,/mp/framework/getUserLayout.do,/mp/framework/GetCustomMenu.do,/mp/framework/DelCustomMenu.do,/mp/framework/CustomMenuAdd.do,/mp/log/NavigationLogAdd.do
#-----------------APIæéæ¦æª--------------------

#-----------------ç§æ·æ ¡éªéç½®--------------------
#æ¯å¦å¯ç¨ç§æ·idæ ¡éª
mp.component.tenancy.tenancyIdCheck=true
#å¯ç¨ç§æ·idæ ¡éªåï¼åªäºapiè¯·æ±å¯ä»¥ä¸å¸¦tenancyIdè®¿é®
mp.component.tenancy.exclude=/mp/framework/*,/mp/notice/*,/mp/wfdesign/*,/mp/login/*,/mp/org/*,/mp/wfengine/*
#-----------------é»è®¤ç§æ·éç½®--------------------

#-----------------signåæ°å ç­¾æ ¡éª--------------------
#æ¯å¦å¯ç¨
mp.component.signprotect.enable=false
#éè¦æé¤ä¿æ¤urlå°å(å¤ä¸ªå°åç¨,åé)
mp.component.signprotect.exclude=
#ç­¾åå¯é¥
mp.component.signprotect.secret=Iv3RlGKyxrCDfu3a
#-----------------signåæ°å ç­¾æ ¡éª--------------------


#-----------------XSSä¿æ¤--------------------
#æ¯å¦å¯ç¨
mp.component.xssprotect.enable=false
#éè¦æé¤ä¿æ¤urlå°å(å¤ä¸ªå°åç¨,åé)
mp.component.xssprotect.exclude=
#éè¦ä¿æ¤çè¡¨è¾¾å¼
mp.component.xssprotect.expression=
#-----------------XSSä¿æ¤--------------------
#JTAéç½®
#----------------- JTAéç½® --------------------
spring.jta.atomikos.properties.defaultJtaTimeout=300000
spring.jta.atomikos.properties.maxTimeout=300000
spring.jta.atomikos.properties.maxActives=-1
#----------------- JTAéç½® --------------------

#----------------- å®å¨éç½® --------------------
# è·¨åè®¾ç½®, å¼ä¸º*: åè®¸è·¨ææå, æ²¡æå¼:ä¸åè®¸è·¨å, å¼ä¸ºæå®åå:å¯ä»¥è·¨æå®çå
mp.component.accessControlAllowOrigin=*
# éç¥é®ä»¶ï¼å¦ætitleæcontentæ²¡éç½®å¼ï¼åä¸ä¼åé®ä»¶
mp.component.disableUser.title=è´¦å·åç¨éç¥é®ä»¶
mp.component.disableUser.content=<br/>    å°æ¬çç¨æ· {0}, æ¨å¥½:<br/><br/>æ¨æ¶å°è¿å°çµå­é®ä»¶æ¯å ä¸ºæ¨çè´¦å·è¢«åç¨ãå¦ææçé®ï¼è¯·èç³»ç³»ç»ç®¡çåã
mp.component.lockUser.title=è´¦å·éå®éç¥é®ä»¶
mp.component.lockUser.content=<br/>    å°æ¬çç¨æ· {0}, æ¨å¥½:<br/><br/>æ¨æ¶å°è¿å°çµå­é®ä»¶æ¯å ä¸ºæ¨çè´¦å·å ç»å½å¤±è´¥æ¬¡æ°è¿å¤è¢«éå®ãå¦ææçé®ï¼è¯·èç³»ç³»ç»ç®¡çåã
#ç¨æ·è´¦å·å°æåå¤å°å¤©å¼å§æéç®¡çå,é»è®¤10
mp.component.expireUser.advanceDays=10
mp.component.expireUser.title=è´¦å·å°æéç¥é®ä»¶
mp.component.expireUser.content=<br/>    æ¨å¥½: <br/><br/>ä¸åç¨æ· {0} çè´¦å·å³å°å°æï¼è¯·æ£æ¥æ¯å¦éè¦æ´æ¹ç¨æ·çå¤±ææ¥æã
mp.component.changePass.title=æ´æ¹å¯ç éç¥é®ä»¶
mp.component.changePass.content=<br/>    å°æ¬çç¨æ· {0}, æ¨å¥½:<br/><br/>æ¨æ¶å°è¿å°çµå­é®ä»¶æ¯å ä¸ºæ¨çè´¦å·æ´æ¹äºå¯ç ãå¦ææçé®ï¼è¯·èç³»ç³»ç»ç®¡çåã
# æ¯å¦å¯ç¨é²éæ¾æ»å»
mp.component.antiReplayAttack=true
#è¾å¥å¯ç å¤å°æ¬¡åºç°éªè¯ç ,é»è®¤ä¸æ¬¡
mp.component.displayVerifyCode=0
#éªè¯ç ç±»åï¼0ï¼æ°å­å­æ¯ç»åï¼1ï¼æ±å­éªè¯ç ï¼2ï¼çº¯æ°å­éªè¯ç 
mp.component.vcodeType=2
#éªè¯ç é¿åº¦ï¼3~6ä½
mp.component.vcodeSize=4
# å¯¹éç½®é¡¹å å¯çå¯ç 
jasypt.encryptor.password=cOIHAgfFZ41NJaNnVB+ylA==
#----------------- å®å¨éç½® --------------------

#----------------- ä¸ååæ¥å¥éç½® --------------------
mp.component.oauth2.oauth2ClientId=mp20
mp.component.oauth2.oauth2RedirectUri=
mp.component.oauth2.oauth2GrantType=authorization_code
mp.component.oauth2.oauth2ResponseType=code
mp.component.oauth2.oauth2ClientSecret=mp20
mp.component.oauth2.oauth2Url=http://172.26.165.242:8021/
mp.component.oauth2.oauth2Authorize=/oauth/authorize
mp.component.oauth2.oauth2Token=/oauth/token
mp.component.oauth2.oauth2User=/api/user?access_token=
#----------------- ä¸ååæ¥å¥éç½® --------------------

# è¡¨åæä»¶å­æ¾ç®å½
mp.component.form.fileDir=/mpjava/form/files