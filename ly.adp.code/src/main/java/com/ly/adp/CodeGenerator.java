package com.ly.adp;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.builder.ConfigBuilder;
import com.baomidou.mybatisplus.generator.config.po.TableFill;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.AbstractTemplateEngine;
import com.baomidou.mybatisplus.generator.engine.VelocityTemplateEngine;

import java.util.*;

public class CodeGenerator {

    /**
     * <p>
     * MySQL 生成演示
     * </p>
     */
    public static void main(String[] args) {
        // 自定义需要填充的字段
        List<TableFill> tableFillList = new ArrayList<>();
        tableFillList.add(new TableFill("ASDD_SS", FieldFill.INSERT_UPDATE));
        String projectPath = System.getProperty("user.dir").replace("\\ly.adp.code","");
        String moduleName = "base";
        String entityPath = projectPath + "\\ly.adp." + moduleName + ".protocol\\";
        String mainPath = projectPath + "\\ly.adp." + moduleName + ".service\\";
        // 代码生成器
        AutoGenerator mpg = new AutoGenerator2().setGlobalConfig(
                // 全局配置
                new GlobalConfig()
                        .setOutputDir(mainPath + "src\\main\\java\\")//输出目录
                        .setFileOverride(true)// 是否覆盖文件
                        .setActiveRecord(false)// 开启 activeRecord 模式
                        .setEnableCache(false)// XML 二级缓存
                        .setBaseResultMap(true)// XML ResultMap
                        .setBaseColumnList(true)// XML columList
                        //.setKotlin(true) 是否生成 kotlin 代码
                        .setAuthor("ly-bucn")
                        // 自定义文件命名，注意 %s 会自动填充表实体属性！
                        // .setEntityName("%sEntity");
                        // .setMapperName("%sDao")
                        // .setXmlName("%sDao")
                        .setServiceName("I%sBiz")
                        .setServiceImplName("%sBiz")
                        .setControllerName("%sController")
        ).setDataSource(
                // 数据源配置
                new DataSourceConfig()
                        .setDbType(DbType.MYSQL)// 数据库类型
                        .setDriverName("com.mysql.cj.jdbc.Driver")
                        .setUsername("appuser")
                        .setPassword("app@user!!")
                        .setUrl("****************************************************************************************")
        ).setStrategy(
                // 策略配置
                new StrategyConfig()
                        // .setCapitalMode(true)// 全局大写命名
                        // .setDbColumnUnderline(true)//全局下划线命名
                        .setTablePrefix(new String[]{"t_eap_"})// 此处可以修改为您的表前缀
                        .setNaming(NamingStrategy.underline_to_camel)// 表名生成策略
                        .setInclude(new String[]{"t_eap_sys_role"}) // 需要生成的表
                        // .setExclude(new String[]{"test"}) // 排除生成的表
                        // 自定义实体父类
                        // .setSuperEntityClass("com.baomidou.demo.TestEntity")
                        // 自定义实体，公共字段
                        //  .setSuperEntityColumns(new String[]{"test_id"})
                        .setTableFillList(tableFillList)
                        // 自定义 mapper 父类
                        // .setSuperMapperClass("com.baomidou.demo.TestMapper")
                        // 自定义 service 父类
                        // .setSuperServiceClass("com.baomidou.demo.TestService")
                        // 自定义 service 实现类父类
                        // .setSuperServiceImplClass("com.baomidou.demo.TestServiceImpl")
                        // 自定义 controller 父类
                        // .setSuperControllerClass("com.baomidou.demo.TestController")
                        // 【实体】是否生成字段常量（默认 false）
                        // public static final String ID = "test_id";
                        // .setEntityColumnConstant(true)
                        // 【实体】是否为构建者模型（默认 false）
                        // public User setName(String name) {this.name = name; return this;}
                        // .setEntityBuilderModel(true)
                        // 【实体】是否为lombok模型（默认 false）<a href="https://projectlombok.org/">document</a>
                        // .setEntityLombokModel(true)
                        // Boolean类型字段是否移除is前缀处理
                        // .setEntityBooleanColumnRemoveIsPrefix(true)
                        .setRestControllerStyle(true)
//                 .setControllerMappingHyphenStyle(true)

        ).setPackageInfo(
                // 包配置
                new PackageConfig()
                        .setModuleName(moduleName)
                        .setParent("com.ly.adp")// 自定义包路径
                        .setService("service")
                        .setServiceImpl("service.impl")
                        .setMapper("dao.mapper")
                        .setController("controller")// 这里是控制器包名，默认 web
                        .setEntity("entities")

        ).setTemplate(
                // 关闭默认 xml 生成，调整生成 至 根目录
                new TemplateConfig()
                //.setXml(null)
                // 自定义模板配置，模板可以参考源码 /mybatis-plus/src/main/resources/template 使用 copy
                // 至您项目 src/main/resources/template 目录下，模板名称也可自定义如下配置：
                // .setController("...");
                // .setEntity("...");
                // .setMapper("...");
                // .setXml("...");
                // .setService("...");
                // .setServiceImpl("...");
        );


        InjectionConfig injectionConfig = new InjectionConfig() {
            @Override
            public void initMap() {

            }
        };

        FileOutConfig mapper = new FileOutConfig(
                "/templates/mapper.xml.vm") {
            // 自定义输出文件目录
            @Override
            public String outputFile(TableInfo tableInfo) {
                String mppPath = mainPath + "src/main/resources/mapper/" + tableInfo.getEntityName() + "Mapper.xml";
                return mppPath;
            }
        };

        FileOutConfig entity = new FileOutConfig(
                "/templates/entity.java.vm") {
            // 自定义输出文件目录
            @Override
            public String outputFile(TableInfo tableInfo) {
                PackageConfig pki = mpg.getPackageInfo();
                String path = (pki.getParent() + "." + pki.getEntity() + ".").replace(".", "/");
                String mppPath = entityPath + "src/main/java/" + path + tableInfo.getEntityName() + ".java";
                return mppPath;
            }
        };

        List<FileOutConfig> fileOutConfigList = new ArrayList<>();
        fileOutConfigList.add(mapper);
        fileOutConfigList.add(entity);
        injectionConfig.setFileOutConfigList(fileOutConfigList);
        mpg.setCfg(injectionConfig);

        mpg.execute();

        // 打印注入设置，这里演示模板里面怎么获取注入内容【可无】
        //System.err.println(mpg.getCfg().getMap().get("abc"));
    }


    public static class AutoGenerator2 extends AutoGenerator{
        @Override
        protected ConfigBuilder pretreatmentConfigBuilder(ConfigBuilder config) {
            config.getPathInfo().remove(ConstVal.ENTITY_PATH);
            config.getPathInfo().remove(ConstVal.XML_PATH);
            return super.pretreatmentConfigBuilder(config);
        }
    }
}
