package com.ly.mp.busicen.rule.stream;

import java.io.File;
import java.io.FileOutputStream;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.util.StringUtils;

import com.ly.mp.component.helper.StringHelper;


public class StreamExcelExport {
	
	HttpServletResponse response;
	SXSSFWorkbook wb = new SXSSFWorkbook(1000);
	StreamExcelData data;
	SXSSFSheet sheet;
	public Consumer<Set<Map<String, Object>>> export(StreamExcelData data, HttpServletResponse response) throws Exception {
		this.response = response;
		this.data=data;
		setHttpheader(response, data.getTitle());
		
		try {
			String sheetName = data.getTitle();
			if (null == sheetName) {
				sheetName = "Sheet1";
			}
			sheetName += LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
			sheet = wb.createSheet(sheetName);
			return writeExcel(wb, sheet, data);			
		} finally {
			
		}
	}
	
	public void flush() throws Exception{
		try {
			//autoSizeColumns(sheet, data.getColumns().length + 1);
			wb.write(response.getOutputStream());
		}
		finally {
			wb.close();
			response.getOutputStream().flush();
			response.getOutputStream().close();
		}
		
	}

	public void exportMultiSheet(String title, List<StreamExcelData> datas, HttpServletResponse response)
			throws Exception {
		setHttpheader(response, title);
		SXSSFWorkbook wb = new SXSSFWorkbook();
		try {
			datas.forEach(data -> {
				String sheetName = data.getTitle();
				if (null == sheetName) {
					sheetName = "Sheet1";
				}
//				sheetName+=LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
				SXSSFSheet sheet = wb.createSheet(sheetName);
				writeExcel(wb, sheet, data);
			});

			wb.write(response.getOutputStream());
		} finally {
			wb.close();
			response.getOutputStream().flush();
			response.getOutputStream().close();
		}
	}

	private Consumer<Set<Map<String, Object>>> writeExcel(SXSSFWorkbook wb, SXSSFSheet sheet, StreamExcelData data) {
		int rowIndex = 1;
		rowIndex = writeTitlesToExcel(wb, sheet,
				Arrays.asList(data.getColumns()).stream().map(m -> m[1]).collect(Collectors.toList()),
				data.getMergeCols());
		Consumer<Set<Map<String, Object>>> callback = writeRowsToExcel(wb, sheet, data.getData(), data.getColumns(), rowIndex, data.getConverts(), data.getExtend(),data.isIsdes(),data.getDesensitize());
		
		return callback;

	}

	private  int writeTitlesToExcel(SXSSFWorkbook wb, Sheet sheet, List<String> titles,
			Map<String, StreamExcelMerge> data) {

		int rowIndex = 0;
		int colIndex = 0;

		int mutiTitleRow = 0;
		// 判断是否多级表头
		if (data != null) {
			for (String key : data.keySet()) {
				if (data.get(key).getEndRow() > mutiTitleRow) {
					mutiTitleRow = data.get(key).getEndRow();
				}
			}

		}
		Font titleFont = wb.createFont();
		titleFont.setFontName("simsun");
		titleFont.setBold(true);
		// titleFont.setFontHeightInPoints((short) 14);
		titleFont.setColor(IndexedColors.BLACK.index);

		CellStyle titleStyle = wb.createCellStyle();
		titleStyle.setAlignment(HorizontalAlignment.CENTER);
		titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
		titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		titleStyle.setFont(titleFont);
		setBorder(titleStyle, BorderStyle.THIN);

		// 多级表头构造
		for (int i = 0; i < mutiTitleRow; i++) {
			Row titleRow = sheet.createRow(rowIndex);
			colIndex = 0;
			for (String key : data.keySet()) {
				if (data.get(key).getEndRow() >= (rowIndex + 1) && data.get(key).getBeginRow() <= (rowIndex + 1)) {

					for (int j = data.get(key).getBeginColumn(); j < data.get(key).getEndColumn(); j++) {
						Cell cell = titleRow.createCell(j - 1);
						cell.setCellValue(!StringUtils.hasText(key) ? "" : key);
						cell.setCellStyle(titleStyle);
					}
				}
			}
			rowIndex++;
		}

		Row titleRow = sheet.createRow(rowIndex);
		// titleRow.setHeightInPoints(25);
		colIndex = 0;

		for (String field : titles) {
			Cell cell = titleRow.createCell(colIndex);
			cell.setCellValue(!StringUtils.hasText(field) ? "" : field);
			cell.setCellStyle(titleStyle);
			colIndex++;
		}

		// 合并列头处理
		if (data != null) {
			for (String key : data.keySet()) {
				sheet.addMergedRegion(
						new CellRangeAddress(data.get(key).getBeginRow() - 1, data.get(key).getEndRow() - 1,
								data.get(key).getBeginColumn() - 1, data.get(key).getEndColumn() - 1));
			}
		}
		rowIndex++;
		return rowIndex;
	}

	private  Consumer<Set<Map<String, Object>>> writeRowsToExcel(SXSSFWorkbook wb, SXSSFSheet sheet,
			Consumer<Set<?>> data, String[][] columns, int rowIndex, Map<String, StreamExcelConvert> convertConf,
			StreamExcelExtend extend,boolean isdes,List<StreamExcelDesensitize> desensitize) {

		Font dataFont = wb.createFont();
		dataFont.setFontName("simsun");
		// dataFont.setFontHeightInPoints((short) 14);
		dataFont.setColor(IndexedColors.BLACK.index);

		CellStyle dataStyle = wb.createCellStyle();
		dataStyle.setAlignment(HorizontalAlignment.CENTER);
		dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		dataStyle.setFont(dataFont);
		setBorder(dataStyle, BorderStyle.THIN);

		AtomicInteger rowcount = new AtomicInteger(0);

		Consumer<Set<Map<String, Object>>> result = new Consumer<Set<Map<String, Object>>>() {
			@Override
			public void accept(Set<Map<String, Object>> ddd) {
				for (Map<String, Object> obj : ddd) {
					int rc = rowcount.getAndIncrement();
					Row dataRow = sheet.createRow(rc + rowIndex);
					for (int i = 0; i < columns.length; i++) {
						Cell cell = dataRow.createCell(i);
						String columnName = columns[i][0];
						Object columnValue = null;
						columnValue = obj.get(columnName);
						String valueT;
						if (convertConf == null) {
							valueT = columnValue == null ? "" : String.valueOf(columnValue);
						} else {
							StreamExcelConvert conf = convertConf.get(columnName);
							if (conf != null) {
								valueT = StreamExcelConvertManager.convertValue(columnValue, conf);
							} else {
								valueT = columnValue == null ? "" : String.valueOf(columnValue);
							}
						}
						valueT = desensitize(isdes, desensitize, columnName, valueT);//脱敏设置
						cell.setCellValue(valueT);
						cell.setCellStyle(dataStyle);
					}
				}

			}
		};

		return result;
	}
	
	public String desensitize(boolean isdes,List<StreamExcelDesensitize> desensitize,String ColumName,String value) {
		if(isdes||desensitize==null) {
			return value;
		}
		Optional<StreamExcelDesensitize> opt = desensitize.stream().filter(m->ColumName.equals(m.getField())).findAny();
		if(opt.isPresent()) {
			String mode = opt.get().getMode();
			return StreamDesensitizeSimple.doDesensitize(mode, value, null);
		}
		return value;
	}

	public void autoSizeColumns(Sheet sheet, int columnNumber) {

		for (int i = 0; i < columnNumber; i++) {

//	            int orgWidth = sheet.getColumnWidth(i);
			sheet.autoSizeColumn(i, true);
//	            int newWidth = (int) (sheet.getColumnWidth(i)  +100);
//	            if (newWidth > orgWidth) {
//	                sheet.setColumnWidth(i, newWidth);
//	            } else {
//	                sheet.setColumnWidth(i, orgWidth);
//	            }
		}
		try {
			// 获取当前列的宽度，然后对比本列的长度，取最大值
			for (int columnNum = 0; columnNum <= columnNumber; columnNum++) {
				int columnWidth = sheet.getColumnWidth(columnNum) / 256;
				for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
					Row currentRow;
					// 当前行未被使用过
					if (sheet.getRow(rowNum) == null) {
						currentRow = sheet.createRow(rowNum);
					} else {
						currentRow = sheet.getRow(rowNum);
					}

					if (currentRow.getCell(columnNum) != null) {
						Cell currentCell = currentRow.getCell(columnNum);
						int length = currentCell.toString().getBytes("GBK").length;
						if (columnWidth < length + 1) {
							columnWidth = length + 1;
						}
					}
				}
				sheet.setColumnWidth(columnNum, columnWidth * 256);
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
	}

	private  void setBorder(CellStyle style, BorderStyle border) {
		style.setBorderTop(border);
		style.setBorderLeft(border);
		style.setBorderRight(border);
		style.setBorderBottom(border);
	}

	public  void setHttpheader(HttpServletResponse response, String fileName) throws Exception {
		// 告诉浏览器用什么软件可以打开此文件
		response.setHeader("content-Type", "application/vnd.ms-excel");
		fileName += LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
		// 下载文件的默认名称
		response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
		response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
	}

	// 生成Excel上传到服务器
	public  String outPutFtp(StreamExcelData excelData1, StreamExcelData excelData2, StreamExcelData excelData3,
			Object fileName) throws Exception {
		SXSSFWorkbook wb = new SXSSFWorkbook();
		String fileUrl = "";
		FileOutputStream fout = null;
		try {

			if (StringHelper.IsEmptyOrNull(fileName)) {
				fileName = "file";
			}
			fileName += LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//			fileUrl+=StringHelper.GetGUID();
			// 判断文件夹是否存在
			File dirFile = new File("D:/upload/38215");
			if (!dirFile.exists()) {
				// 文件不存在时,应该创建文件夹
				dirFile.mkdirs();
			}
			fileUrl = "D:/upload/38215/" + fileName + ".xls";
			String sheetName1 = excelData1.getTitle();
			if (null == sheetName1) {
				sheetName1 = "Sheet1";
			}
			String sheetName2 = excelData2.getTitle();
			if (null == sheetName2) {
				sheetName2 = "Sheet2";
			}
			String sheetName3 = excelData3.getTitle();
			if (null == sheetName3) {
				sheetName3 = "Sheet3";
			}
			SXSSFSheet sheet1 = wb.createSheet(sheetName1);
			SXSSFSheet sheet2 = wb.createSheet(sheetName2);
			SXSSFSheet sheet3 = wb.createSheet(sheetName3);
			if (excelData1.getColumns() != null) {
				writeExcel(wb, sheet1, excelData1);
			}
			if (excelData2.getColumns() != null) {
				writeExcel(wb, sheet2, excelData2);
			}
			if (excelData3.getColumns() != null) {
				writeExcel(wb, sheet3, excelData3);
			}
			fout = new FileOutputStream(fileUrl);
			wb.write(fout);

		} finally {
			wb.close();
			if (fout != null) {
				fout.flush();
				fout.close();
			}
		}
		return fileUrl;

	}

}
