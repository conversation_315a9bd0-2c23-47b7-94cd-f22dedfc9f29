package com.ly.mp.busicen.rule.flow.action.plugin.function;

import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteFunctionBase;
import static com.ly.mp.busicen.rule.XruleStrUtils.*;

@Component
public class ActionExecuteFunctionContextSpelValue extends ActionExecuteFunctionBase{

	@Override
	public IActionResult execute(IAction action, IDataVolume dataVolume) {		
		ActionResult result = ActionResult.create();
		result.setSignal(Signal.CONTINUE);
		String content = action.content();		
		String param = splitXKH(content);
		ExpressionParser parser = new SpelExpressionParser();
		EvaluationContext context=new StandardEvaluationContext(dataVolume);
		Expression expression = parser.parseExpression(param);
		Object caculateData = expression.getValue(context);
		result.setData(caculateData);
		dataVolume.ext().put(action.action(), caculateData);
		String nextAction = defaultNextActionCode(action, "");
		result.setNextAction(nextAction);
		return result;
	}

	@Override
	public String name() {
		return "spelcalculate";
	}

}
