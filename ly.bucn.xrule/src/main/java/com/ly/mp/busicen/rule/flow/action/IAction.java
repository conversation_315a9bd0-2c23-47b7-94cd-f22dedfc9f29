package com.ly.mp.busicen.rule.flow.action;

import java.io.Serializable;
import java.util.Map;

public interface IAction  extends Serializable {
	
	public static final String EXTKEY_SPEL = "EXTKEY_SPEL";
	public static final String EXTKEY_RID = "EXTKEY_RID";
	public static final String EXTKEY_BRAND = "EXTKEY_BRAND";
	public static final String EXTKEY_LOG = "EXTKEY_LOG";
	public static final String EXTKEY_EXCEL = "EXTKEY_EXCEL";
	
	String action();
	
	String nextAction();
	
	String actionName();
	
	String content();
	
	String extKey();
	
	String ruleCode();
	
	OperationType operation();	
	
	String msg();
	
	String filter();
	
	boolean loopEnble();
	
	Map<String, Object> extention();

}
