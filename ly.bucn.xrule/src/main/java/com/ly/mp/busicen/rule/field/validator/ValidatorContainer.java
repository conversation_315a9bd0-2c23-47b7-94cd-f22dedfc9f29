package com.ly.mp.busicen.rule.field.validator;

import java.util.HashMap;
import java.util.Map;

public class ValidatorContainer {
	
	private Map<String, ValidatorBase> validators=new HashMap<String, ValidatorBase>();

	public Map<String, ValidatorBase> getValidators() {
		return validators;
	}

	public void setValidators(Map<String, ValidatorBase> validators) {
		this.validators = validators;
	}
	
	public ValidatorBase get(String name) {
		return validators.get(name);
	}
	
	

}
