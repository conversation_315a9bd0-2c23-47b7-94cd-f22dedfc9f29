package com.ly.mp.busicen.rule.flow.filter;

import com.ly.mp.busicen.rule.flow.action.IActionResult;

public class FlowResultImpl implements FlowResult{

	private IActionResult actionResult;
	
	private Throwable exception;
	
	@Override
	public IActionResult actionResult() {
		return actionResult;
	}

	@Override
	public Throwable exception() {
		return exception;
	}

	public IActionResult getActionResult() {
		return actionResult;
	}

	public void setActionResult(IActionResult actionResult) {
		this.actionResult = actionResult;
	}

	public Throwable getException() {
		return exception;
	}

	public void setException(Throwable exception) {
		this.exception = exception;
	}

}
