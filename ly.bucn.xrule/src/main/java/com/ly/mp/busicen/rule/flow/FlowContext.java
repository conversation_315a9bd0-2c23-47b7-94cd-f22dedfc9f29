package com.ly.mp.busicen.rule.flow;

import java.util.Map;

public class FlowContext implements IFlowContext{
	
	private FlowVolume flowVolume;
	
	private DataVolume dataVolume;
	
	public static FlowContext create() {
		FlowContext context = new FlowContext();
		context.dataVolume = new DataVolume();
		context.flowVolume = new FlowVolume();
		context.dataVolume.setUser(FlowUserMode.currentUser());		
		return context;
	}
	
	public static FlowContext create(String flow,Map<String, Object> data) {
		FlowContext context = create();
		context.flowVolume.setFlow(flow);
		context.dataVolume.setData(data);
		return context;
	}

	@Override
	public IFlowVolume flowVolume() {
		return flowVolume;
	}

	@Override
	public IDataVolume dataVolume() {
		return dataVolume;
	}

	public FlowVolume getFlowVolume() {
		return flowVolume;
	}

	public void setFlowVolume(FlowVolume flowVolume) {
		this.flowVolume = flowVolume;
	}

	public DataVolume getDataVolume() {
		return dataVolume;
	}

	public void setDataVolume(DataVolume dataVolume) {
		this.dataVolume = dataVolume;
	}	

}
