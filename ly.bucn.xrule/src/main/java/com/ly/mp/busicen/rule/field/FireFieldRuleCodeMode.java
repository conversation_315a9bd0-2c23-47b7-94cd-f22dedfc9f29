package com.ly.mp.busicen.rule.field;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.util.StringUtils;

import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.rule.field.execution.ValidFieldData;
import com.ly.mp.busicen.rule.field.execution.ValidResult;
import com.ly.mp.busicen.rule.field.execution.ValidResultCtn;
import com.ly.mp.busicen.rule.field.msg.IFieldMsg;
import com.ly.mp.busicen.rule.field.msg.ext.FieldMsg;
import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidData;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;
import com.ly.mp.busicen.rule.field.validator.Validation;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldAllNotNull;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldChineseValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldDateOrderValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldEmailValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldEnumValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldExcelHeaderValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldIntegerValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldLengthValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldLetterNumberValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldLetterValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldLongDateValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldLowLetterValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldNotNullValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldNumber2Valid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldNumberOrderValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldNumberValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldPhoneValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldPostCodeValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldRegexValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldShortDateValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldSpelValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldTelPhoneValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldTelValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.FieldUpLetterValid;
import com.ly.mp.busicen.rule.field.validator.plugin.codeMode.ValidatorBaseAbstract;



/**
 * 	规则引擎校验 代码实现
 * 	[]中的消息编码改为直接取值
 */
public class FireFieldRuleCodeMode {
	private enum Singleton {
		INSTANCE;

		private final FireFieldRuleCodeMode instance;

		Singleton() {
			instance = new FireFieldRuleCodeMode();
		}

		private FireFieldRuleCodeMode getInstance() {
			return instance;
		}
	}
	private FireFieldRuleCodeMode() {
		// 初始化校验类
		validatorBaseImpl.put("chinese", new FieldChineseValid());
		validatorBaseImpl.put("email", new FieldEmailValid());
		validatorBaseImpl.put("enum", new FieldEnumValid());
		validatorBaseImpl.put("integer", new FieldIntegerValid());
		validatorBaseImpl.put("length", new FieldLengthValid());
		validatorBaseImpl.put("letternumber", new FieldLetterNumberValid());
		validatorBaseImpl.put("letter", new FieldLetterValid());
		validatorBaseImpl.put("longdate", new FieldLongDateValid());
		validatorBaseImpl.put("lowletter", new FieldLowLetterValid());
		validatorBaseImpl.put("notnull", new FieldNotNullValid());
		validatorBaseImpl.put("number", new FieldNumberValid());
		validatorBaseImpl.put("number2", new FieldNumber2Valid());
		validatorBaseImpl.put("phone", new FieldPhoneValid());
		validatorBaseImpl.put("postcode", new FieldPostCodeValid());
		validatorBaseImpl.put("regex", new FieldRegexValid());
		validatorBaseImpl.put("shortdate", new FieldShortDateValid());
		validatorBaseImpl.put("telphone", new FieldTelPhoneValid());
		validatorBaseImpl.put("tel", new FieldTelValid());
		validatorBaseImpl.put("upletter", new FieldUpLetterValid());
		validatorBaseImpl.put("allnotnull", new FieldAllNotNull());
		validatorBaseImpl.put("dateorder", new FieldDateOrderValid());
		validatorBaseImpl.put("numberorder", new FieldNumberOrderValid());
		validatorBaseImpl.put("spel", new FieldSpelValid());
		validatorBaseImpl.put("excelheader", new FieldExcelHeaderValid());
		// 初始化默认错误信息
		defaultMessageTitle.put("COM-V-allnotnull", "不能同时为空");
		defaultMessageTitle.put("COM-V-chinese", "只能为中文");
		defaultMessageTitle.put("COM-V-dateorder", "日期大小不合理");
		defaultMessageTitle.put("COM-V-email", "不符合邮箱规则");
		defaultMessageTitle.put("COM-V-enum", "取值范围只能为%s");
		defaultMessageTitle.put("COM-V-excelheader", "应包含:%s");
		defaultMessageTitle.put("COM-V-integer", "只能为整数且范围在%s-%s");
		defaultMessageTitle.put("COM-V-integergt", "只能为整数且大于%s");
		defaultMessageTitle.put("COM-V-integerlt", "只能为整数且小于%s");
		defaultMessageTitle.put("COM-V-length", "长度范围在%s-%s");
		defaultMessageTitle.put("COM-V-letter", "只能为字母");
		defaultMessageTitle.put("COM-V-letternumber", "字母或数字");
		defaultMessageTitle.put("COM-V-longdate", "只能为日期(YYYY-MM-DD hh:mm:ss)");
		defaultMessageTitle.put("COM-V-lowletter", "只能为小写字母");
		defaultMessageTitle.put("COM-V-notnull", "不能为空");
		defaultMessageTitle.put("COM-V-number", "只能是数值类型且范围在%s-%s");
		defaultMessageTitle.put("COM-V-number2", "只能是数值类型且范围在%s-%s");
		defaultMessageTitle.put("COM-V-number2gt", "只能是数值类型且大于%s");
		defaultMessageTitle.put("COM-V-number2lt", "只能是数值类型且小于%s");
		defaultMessageTitle.put("COM-V-numbergt", "只能是数值类型且大于等于%s");
		defaultMessageTitle.put("COM-V-numberlt", "只能是数值类型且小于等于%s");
		defaultMessageTitle.put("COM-V-numberorder", "大小比较不正确");
		defaultMessageTitle.put("COM-V-phone", "只能为手机");
		defaultMessageTitle.put("COM-V-postcode", "只能为邮编");
		defaultMessageTitle.put("COM-V-regex", "正则表达式校验异常");
		defaultMessageTitle.put("COM-V-shortdate", "只能为日期(YYYY-MM-DD)");
		defaultMessageTitle.put("COM-V-spel", "校验失败");
		defaultMessageTitle.put("COM-V-tel", "只能为电话");
		defaultMessageTitle.put("COM-V-telphone", "手机或电话格式不正确");
		defaultMessageTitle.put("COM-V-upletter", "只能为大写字母");
	}
	public static FireFieldRuleCodeMode getInstance() {
		return Singleton.INSTANCE.getInstance();
	}

	private Map<String,ValidatorBaseAbstract> validatorBaseImpl=new ConcurrentHashMap<>();

	private Map<String,String> defaultMessageTitle=new ConcurrentHashMap<>();

	private IFieldMsg getMsg(String name, String errMsg) {
		FieldMsg fieldMsg = new FieldMsg();
		if(defaultMessageTitle.containsKey(errMsg)) {
			errMsg=defaultMessageTitle.get(errMsg);
		}
		fieldMsg.setMessageTitle(errMsg);
		return fieldMsg;
	}



	private Object[] strs2objs(String[] param) {
		if (param==null) {
			return null;
		}
		Object[] result = new Object[param.length];
		for (int i = 0; i < param.length; i++) {
			result[i]=param[i];
		}
		return result;
	}

	private ValidResult validField(ValidFieldData field) {
		ValidResult result = new ValidResult();
		result.setFieldName(field.getFiledName());
		result.setField(field.getField());
		result.setValid(true);
		boolean flag = field.isAllCheck();
		for (IValidation validation : field.getValidations()) {
			String name = validation.name();
			// 获取对应的校验类
			ValidatorBaseAbstract validatorBase = validatorBaseImpl.get(name);
			if (validatorBase == null) {
				String errormsg = String.format("不存在类型【%s】的校验器", name);
				throw new RuntimeException(errormsg);
			}
			boolean isValid = validatorBase.valid(newValidData(field), validation);
			if (!isValid) {
				result.setValid(isValid);
				ValidMsg validMsg = validatorBase.errorMsg(validation);
				String msg = getMsg(validation.name(), validMsg.getMsg()).getMsg();
				String error = String.format(msg, strs2objs(validMsg.getParams()));
				result.addError(error);
				if (!flag) {
					break;
				}
			}
		}
		return result;
	}
	private IValidData newValidData(ValidFieldData field) {
		ValidData validData = new ValidData();
		validData.setSource(field.getSource());
		validData.setValue(field.getValue());
		return validData;
	}


	private List<ValidFieldData> ruleToValidation(Map<String, ?> entity,List<FieldRuleEntity> articles){
		List<ValidFieldData> result = new ArrayList<>();
		articles.forEach(v->{
			ValidFieldData validFieldData = new ValidFieldData();
			validFieldData.setField(v.getColumnCode());
			validFieldData.setFiledName(v.getColumnName());
			validFieldData.setValue(entity.get(v.getColumnCode()));
			validFieldData.setSource(entity);
			validFieldData.setValidations(new ArrayList<Validation>());
			String[] statements = splitStatements(v.getValidateContent());
			Optional.ofNullable(statements).ifPresent(m->{
				for (String statement : m) {
					validFieldData.getValidations().add(Validation.build(statement));
				}
			});
			result.add(validFieldData);
		});
		return result;
	}

	private String[] splitStatements(String statements){
		if (StringUtils.isEmpty(statements)) {
			return null;
		}
		return statements.split(";");
	}

	@SuppressWarnings("unchecked")
	private Map<String,?> convertToMap(Object entity) {
		if (entity == null) {
			throw new NullPointerException("传入校验对象为空");
		}
		if (entity instanceof Map) {
			return (Map<String,?>) entity;
		}
		return entityToMap(entity);

	}

	private Map<String, Object> entityToMap(Object entity) {
		if (entity == null) {
			return null;
		}
		Map<String, Object> result = new HashMap<>();
		try {
			BeanInfo beanInfo = Introspector.getBeanInfo(entity.getClass());
			PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
			for (PropertyDescriptor m : propertyDescriptors) {
				String name = m.getName();
				if (name.equals("callback")||name.equals("callbacks")||name.equals("class")) {
					continue;
				}
				Method method = m.getReadMethod();
				result.put(name, method.invoke(entity));
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return result;
	}


	public ValidResultCtn fireRule(Object entity,List<FieldRuleEntity> articles) {
		Map<String, ?> target = convertToMap(entity);
		List<ValidFieldData> validations = ruleToValidation(target, articles);
		ValidResultCtn validResultCtn = new ValidResultCtn();
		validations.forEach(m->{
			validResultCtn.getResults().add(validField(m));
		});
		return validResultCtn;
	}

	public void fireRuleExcpt(Object entity,List<FieldRuleEntity> articles) {
		ValidResultCtn result = fireRule(entity, articles);
		if (!result.isValid()) {
			throw BusicenException.create(result.getNotValidMessage());
		}
	}

}