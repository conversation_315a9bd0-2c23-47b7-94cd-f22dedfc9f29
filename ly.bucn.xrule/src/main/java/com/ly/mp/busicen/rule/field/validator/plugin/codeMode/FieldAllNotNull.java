package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;



public class FieldAllNotNull extends ValidatorBaseAbstract{

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		String param = validation.strParam();
		if (!StringUtils.isEmpty(param)) {
			String[] mm = param.split(",");
			if (mm.length>0) {
				for (String key : mm) {
					Object value = validData.source().get(key);
					if (value!=null) {
						if (value instanceof String) {
							if (!StringUtils.isEmpty(value)) {
								return true;
							}else {
								continue;
							}
						}
						return true;
					}
				}
				return false;
			}
		}
		return true;
	}

	@Override
	public String validName() {
		return "allnotnull";
	}

}
