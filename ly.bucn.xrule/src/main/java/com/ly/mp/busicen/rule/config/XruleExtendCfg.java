package com.ly.mp.busicen.rule.config;

import java.util.function.Supplier;

import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;

public class XruleExtendCfg {
	
	/**
	 * 获取当前用户的代理方法
	 */
	private static Supplier<? extends UserBusiEntity> user_Supplier;
	
	/**
	 * 是否加载客制化配置
	 */
	private static boolean cust_Mode;
	
	/**
	 * 检索配置是否品牌过滤
	 */
	private static Boolean brandCheck;
	
	/**
	 * 客制化表后缀
	 */
	public final static String CUST_EXT="_cust";
	
	public  void createBucnCustMode() {
		cust_Mode=false;
	}	
	
	public static boolean brandCheck() {
		return brandCheck;
	}

	public XruleExtendCfg brandCheck(boolean brandCheck) {
		XruleExtendCfg.brandCheck = brandCheck;
		return this;
	}

	public void createDefaultCustMode(boolean custMode) {
		cust_Mode=custMode;
	}
	
	public boolean currentCustMode() {
		return cust_Mode;
	}	
	
	public static Supplier<? extends UserBusiEntity> currentUserSupplier(){
		return user_Supplier;
	}
	
	public void createBucnUser() {
		user_Supplier= BusicenContext::getCurrentUserBusiInfo;
	}
	
	public static XruleExtendCfg createEmpty() {
		return new XruleExtendCfg();
	}
	
	public static XruleExtendCfg createBucn() {
		XruleExtendCfg xec = createEmpty();
		xec.createBucnUser();
		xec.createBucnCustMode();
		xec.brandCheck(true);
		return xec;
	}
	
	public static XruleExtendCfg createDefault() {
		return createBucn();
	}
	
	public void createDefaultUser(Supplier<? extends UserBusiEntity> userSupplier) {
		user_Supplier = userSupplier;
	}

}
