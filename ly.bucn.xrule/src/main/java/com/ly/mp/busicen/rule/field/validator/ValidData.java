package com.ly.mp.busicen.rule.field.validator;

import java.util.Map;

public class ValidData implements IValidData {

	private Object value;
	
	private Map<String, ?> source;
	
	@Override
	public Object value() {
		return value;
	}

	@Override
	public Map<String, ?> source() {
		return source;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public void setSource(Map<String, ?> source) {
		this.source = source;
	}	

}
