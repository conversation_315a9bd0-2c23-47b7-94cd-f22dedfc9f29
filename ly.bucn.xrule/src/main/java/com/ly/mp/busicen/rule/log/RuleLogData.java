package com.ly.mp.busicen.rule.log;

import java.io.Serializable;
import java.time.LocalDateTime;

public class RuleLogData implements Serializable{
	
	public static RuleLogData create(String rid,String flow,String name,RuleLogType type,RuleLogState state,String msg,Throwable e,Object data) {
		RuleLogData logData = new RuleLogData();
		logData.setRid(rid);
		logData.setFlow(flow);
		logData.setName(name);
		logData.setType(type);
		logData.setRunState(state);
		logData.setMsg(msg);
		logData.setData(logData);
		logData.setE(e);
		logData.setTime(LocalDateTime.now());		
		return logData;
	}
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String rid;
	
	private String flow;
	
	private String name;
	
	private RuleLogType type;
	
	private RuleLogState runState;
	
	private String msg;	
	
	private LocalDateTime time;
	
	private Throwable e;
	
	private transient Object data;
	
	@Override
	public String toString() {
		String str = String.format("[%s][%s][%s][%s][%s][%s]", rid,flow,name,type,runState,time);
 		return str;
	}
	
	public String getRid() {
		return rid;
	}

	public void setRid(String rid) {
		this.rid = rid;
	}

	public String getFlow() {
		return flow;
	}

	public void setFlow(String flow) {
		this.flow = flow;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public RuleLogType getType() {
		return type;
	}

	public void setType(RuleLogType type) {
		this.type = type;
	}

	public RuleLogState getRunState() {
		return runState;
	}

	public void setRunState(RuleLogState runState) {
		this.runState = runState;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public LocalDateTime getTime() {
		return time;
	}

	public void setTime(LocalDateTime time) {
		this.time = time;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	public Throwable getE() {
		return e;
	}

	public void setE(Throwable e) {
		this.e = e;
	}



	public static enum RuleLogType{
		FLOW,
		ACTION,
		FILTER,
		RULE
	}
	
}
