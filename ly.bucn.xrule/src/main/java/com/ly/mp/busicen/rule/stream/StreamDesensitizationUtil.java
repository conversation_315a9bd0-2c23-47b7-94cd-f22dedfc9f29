package com.ly.mp.busicen.rule.stream;

import org.apache.commons.lang3.StringUtils;

public class StreamDesensitizationUtil {
	
	/**
	 * 只显示第一个汉字，其他隐藏为2个星号<例子：李**>
	 *
	 * @param fullName
	 * @param index    1 为第index位开始脱敏
	 * @return
	 */
	public static String left(String fullName, int index) {
		if (StringUtils.isBlank(fullName)) {
			return "";
		}
		if(StringUtils.length(fullName)<=index) {
			return all(fullName);
		}
		String name = StringUtils.left(fullName, index);
		return StringUtils.rightPad(name, StringUtils.length(fullName), "*");
	}
	
	public static String all(String name) {
		if(StringUtils.isBlank(name)) {
			return "";
		}
		
		return dest(name);// StringUtils.rightPad("", StringUtils.length(name), "*");
	}
	
	public static String dest(String name) {
		if(StringUtils.isBlank(name)) {
			return name;
		}else {
			return dest(StringUtils.length(name));
		}
	}
	
	public static String dest(int length) {
		if(length<=0) {
			return null;
		}else {
			char[] xxx = new char[length];
			for (int a=0;a<length;a++) {
				xxx[a]='*';
			}
			return new String(xxx);
		}
	}
	

	/**
	 * 110****58，前面保留3位明文，后面保留2位明文
	 *
	 * @param name
	 * @param index 3
	 * @param end   2
	 * @return
	 */
	public static String around(String name, int index, int end) {
		if (StringUtils.isBlank(name)) {
			return "";
		}
		if(StringUtils.length(name)<=index+end) {
			return all(name);
		}
		String left = StringUtils.left(name, index);
		String right = StringUtils.right(name, end);
		String xxx = dest(StringUtils.length(name)-index-end); //StringUtils.rightPad("", StringUtils.length(name)-index-end, "*");
		return left.concat(xxx).concat(right);
	}	
	
	/**
	 * 110****58，前面保留3位明文，后面保留2位明文
	 *
	 * @param name
	 * @param index 3
	 * @param end   2
	 * @return
	 */
	public static String email(String name) {
		if (StringUtils.isBlank(name)) {
			return "";
		}
		if(name.contains("@")) {
			String[] sp = name.split("@");
			return StringUtils.rightPad(StringUtils.left(sp[0], 2), StringUtils.length(sp[0]), "*").concat("@").concat(sp[1]);
		}
		return around(name,2,3);
	}

	/**
	 * 后四位，其他隐藏<例子：****1234>
	 *
	 * @param num
	 * @return
	 */
	public static String right(String num, int end) {
		if (StringUtils.isBlank(num)) {
			return "";
		}
		if(StringUtils.length(num)<=end) {
			return all(num);
		}
		return StringUtils.leftPad(StringUtils.right(num, end), StringUtils.length(num), "*");
	}

	/**
	 * 手机号码前三后四脱敏
	 * 
	 * @param mobile
	 * @return
	 */
	public static String mobileEncrypt(String mobile) {
		if (StringUtils.isEmpty(mobile) || (mobile.length() != 11)) {
			return mobile;
		}
		return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
	}

	/**
	 * 身份证前三后四脱敏
	 * 
	 * @param id
	 * @return
	 */
	public static String idEncrypt(String id) {
		if (StringUtils.isEmpty(id) || (id.length() < 8)) {
			return id;
		}
		return id.replaceAll("(?<=\\w{3})\\w(?=\\w{4})", "*");
	}

	/**
	 * 护照前2后3位脱敏，护照一般为8或9位
	 * 
	 * @param id
	 * @return
	 */
	public static String idPassport(String id) {
		if (StringUtils.isEmpty(id) || (id.length() < 8)) {
			return id;
		}
		return id.substring(0, 2) + new String(new char[id.length() - 5]).replace("\0", "*")
				+ id.substring(id.length() - 3);
	}

	/**
	 * 证件后几位脱敏
	 * 
	 * @param id
	 * @param sensitiveSize
	 * @return
	 */
	public static String idPassport(String id, int sensitiveSize) {
		if (StringUtils.isBlank(id)) {
			return "";
		}
		int length = StringUtils.length(id);
		return StringUtils.rightPad(StringUtils.left(id, length - sensitiveSize), length, "*");
	}


}
