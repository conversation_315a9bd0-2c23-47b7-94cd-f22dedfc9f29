package com.ly.mp.busicen.rule.field;

/**
 * 	校验工具类 规则
 */
public class FieldRuleEntity {

	public FieldRuleEntity(String columnCode, String columnName, String validateContent) {
		this.columnCode = columnCode;
		this.columnName = columnName;
		this.validateContent = validateContent;
	}

	/**
	 * 字段中文名
	 */
	private String columnName;
	/**
	 * 字段编码
	 */
	private String columnCode;
	/**
	 * 校验规则
	 */
	private String validateContent;

	@Override
	public String toString() {
		StringBuilder builder = new StringBuilder();
		builder.append("{\"columnName\":\"").append(columnName).append("\",\"columnCode\":\"").append(columnCode).append("\",\"validateContent\":\"").append(validateContent).append("\"}");
		return builder.toString();
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public String getColumnCode() {
		return columnCode;
	}

	public void setColumnCode(String columnCode) {
		this.columnCode = columnCode;
	}

	public String getValidateContent() {
		return validateContent;
	}

	public void setValidateContent(String validateContent) {
		this.validateContent = validateContent;
	}

}