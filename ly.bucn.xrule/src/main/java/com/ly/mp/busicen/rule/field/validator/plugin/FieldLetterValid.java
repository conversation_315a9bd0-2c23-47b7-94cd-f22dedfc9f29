package com.ly.mp.busicen.rule.field.validator.plugin;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidatorBase;

@Component
public class FieldLetterValid extends ValidatorBase {

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)) {
			return RegexUtil.match(RegexUtil.Letter, value.toString());
		}
		return true;
	}

	@Override
	public String validName() {
		return "letter";
	}

}
