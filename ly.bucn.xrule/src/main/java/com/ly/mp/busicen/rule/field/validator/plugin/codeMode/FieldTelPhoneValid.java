package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;



public class FieldTelPhoneValid extends ValidatorBaseAbstract{

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)) {
			return RegexUtil.match(RegexUtil.Phone, value.toString())||RegexUtil.match(RegexUtil.Tel, value.toString());
		}
		return true;
	}

	@Override
	public String validName() {
		return "telphone";
	}
	
}
