package com.ly.mp.busicen.rule.instrumentation.plugin;

import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.event.ContextRefreshedEvent;

import com.ly.mp.busicen.rule.flow.ActionContainer;
import com.ly.mp.busicen.rule.flow.FlowSpelUtil;

public class ApiRealConfiguration implements ApplicationListener<ContextRefreshedEvent> {
	
	@Bean
	public ApiRealHandlerContainer apiRealHandlerContainer() {
		return new ApiRealHandlerContainer();
	}
	
	@Bean
	public IApiRealEventPublish apiRealEventPublish(ApiRealHandlerContainer apiRealHandlerContainer,ActionContainer actionContainer) {
		return new ApiRealEventPublish(apiRealHandlerContainer,actionContainer);
	}

	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		//FlowSpelUtil.exportService(IApiRealEventPublish.class, event.getApplicationContext().getBean(IApiRealEventPublish.class));
	}

}
