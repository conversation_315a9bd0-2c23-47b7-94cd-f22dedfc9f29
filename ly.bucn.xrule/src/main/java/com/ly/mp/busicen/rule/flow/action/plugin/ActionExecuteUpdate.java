package com.ly.mp.busicen.rule.flow.action.plugin;

import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.flow.FlowSpelUtil;
import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.action.ActionExecuteBase;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.engine.db.BusicenSqlMapper;

public class ActionExecuteUpdate extends ActionExecuteBase{

	
	@Override
	public IActionResult execute(IAction action, IDataVolume dataVolume) {
		ActionResult result = ActionResult.create();
		result.signal(Signal.CONTINUE);
		result.action(action.action());
		result.msg(action.msg());
		
		try {
			BusicenSqlMapper busicenSqlMapper = BusicenSqlMapper.create();
			Object data = dataVolume;
			if (!StringUtils.isEmpty(action.extKey())) {
				data = FlowSpelUtil.spelGetData(action, action.extKey());
			}
			data = wrapperUserData(data);
			int count = busicenSqlMapper.update(action.content(), data);
			result.data(count);
			dataVolume.ext().put(action.action(), count);				
			result.nextAction(defaultNextActionCode(action, null));
		} catch (Exception e) {
			result.signal(Signal.EXCPT);
			result.excpt(e);
		}
		return result;
	}
}
