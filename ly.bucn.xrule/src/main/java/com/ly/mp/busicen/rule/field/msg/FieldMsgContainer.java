package com.ly.mp.busicen.rule.field.msg;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import com.ly.mp.busicen.rule.config.XruleConfig;
import com.ly.mp.busicen.rule.field.msg.ext.FieldMsg;
import com.ly.mp.busicen.rule.flow.FlowUserMode;

public class FieldMsgContainer implements IFieldMsgContainer {
	
	@Autowired
	XruleConfig xruleConfig;
	
	Logger log = LoggerFactory.getLogger(FieldMsgContainer.class);
	
	private Map<String, Map<String, IFieldMsg>> errorMsgs = new HashMap<String, Map<String,IFieldMsg>>();
	
	private List<FieldMsg> fieldMsgs = new ArrayList<FieldMsg>();
	private List<FieldMsg> fieldMsgsCust = new ArrayList<FieldMsg>();

	public List<FieldMsg> getFieldMsgs() {
		return fieldMsgs;
	}

	public void setFieldMsgs(List<FieldMsg> fieldMsgs) {
		this.fieldMsgs = fieldMsgs;
	}
	
	public void setFieldMsgsCust(List<FieldMsg> fieldMsgs) {
		this.fieldMsgsCust = fieldMsgs;
	}

	public Map<String, Map<String, IFieldMsg>> getErrorMsgs() {
		return errorMsgs;
	}

	public void setErrorMsgs(Map<String, Map<String, IFieldMsg>> errorMsgs) {
		this.errorMsgs = errorMsgs;
	}
	
	public Map<String, IFieldMsg> getErrors(String name) {
		Map<String, IFieldMsg> errors = errorMsgs.get(name);
		if (errors==null) {
			String err = String.format("规则【%s】的错误信息找不到", name);
			log.error(err);
			throw new RuntimeException(err);
		}
		return errors;
	}

	@Override
	public IFieldMsg getMsg(String name, String label) {
//		UserBusiEntity userBusiEntity = FlowUserMode.currentUser();
		Map<String,Object> ctx = FlowUserMode.currentContext(null);
		
		IFieldMsg fieldMsg = null;
		
		if(xruleConfig.xruleExtendCfg().currentCustMode()) {
			Optional<FieldMsg> opt = fieldMsgsCust.stream().filter(m->
			label.equals(m.getLabel())&&
			ctx.get("brandCode").equals(m.getBrandCode())&&
			ctx.get("oemCode").equals(m.getOemCode())
			).findAny();
			if (opt.isPresent()) {
				fieldMsg = opt.get();
			}
		}
		
		if(fieldMsg!=null) {
			log.info("规则引擎提示语【{}】使用客制化配置",label);
		}else {		
			Optional<FieldMsg> opt = fieldMsgs.stream().filter(m->
					label.equals(m.getLabel())&&
					ctx.get("brandCode").equals(m.getBrandCode())&&
					ctx.get("oemCode").equals(m.getOemCode())
					).findAny();
			if (opt.isPresent()) {
				fieldMsg = opt.get();
			}else {
				String error = String.format("【%s】错误信息找不到", label);
				log.error(error);
				throw new RuntimeException(error);
			}
		}
		return fieldMsg;
	}
	
	
	
//	@Override
//	public IFieldMsg getMsg(String name,String label) {
//		Map<String, IFieldMsg> errors = getErrors(name);
//		IFieldMsg errorMsg = errors.get(label);
//		if (errorMsg==null) {
//			String err = String.format("规则【%s】的【%s】错误信息找不到", name,label);
//			log.error(err);
//			throw new RuntimeException(err);
//		}
//		return errorMsg;
//	}
//	

}
