package com.ly.mp.busicen.rule.flow;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.helper.StringHelper;

public class FlowSpelMethods {
	
	private static Logger log = LoggerFactory.getLogger(FlowSpelMethods.class);	
	
	public static String uuid() {
		return StringHelper.GetGUID();
	}
	
	public static Map<String, Object> mapnew() {
		return new HashMap<String, Object>();
	}
	
	public static Object map(Object info) {
		return JSONObject.toJSON(info);
	}
	
	public static Map<String,Object> mapcp(Map<String, Object> src,Map<String, Object> dist,String... keys) {
		if (src==null||dist==null) {
			return dist;
		}
		if (keys!=null&& keys.length>0) {
			for (String key : keys) {
				dist.put(key, src.get(key));
			}
		}else {
			src.entrySet().stream().forEach(m->{
				if (dist.get(m.getKey())==null) {
					dist.put(m.getKey(),m.getValue());
				}
			});
		}
		return dist;
	}
	
	public static String ordernum(String dlrId, String billTypeId) {
		throw new UnsupportedOperationException();
//		try {
//			Object orderNumOpt = FlowSpelUtil.dynamicInvoke("com.ly.mp.busicen.prc.ibiz.IMdsOrderCodeRuleBiz", "generateOrderCode",
//					new String[] {String.class.getName(),String.class.getName()}, new Object[] {dlrId,billTypeId});
//			@SuppressWarnings("unchecked")
//			Map<String, String> result = (Map<String, String>) orderNumOpt;
//			if (result.get("result").equals("1")) {
//				return result.get("msg");
//			} else {
//				throw BusicenException.create(result.get("msg"));
//			}
//		} catch (Exception e) {
//			log.error("Spel调用单号生成器失败",e);
//			throw e;
//		}
		
	}
	/**
	 * 
	 * @Description: TODO(这里用一句话描述这个类的作用)
	 * <AUTHOR>
	 * @date 2020年1月4日
	 */
	public static String sequence(String sequenceName)
	{
		throw new UnsupportedOperationException();
//		try {
//			String tokenString= BusicenContext.getToken();
//			Object sequObject = FlowSpelUtil.dynamicInvoke("com.ly.mp.busicen.prc.ibiz.ISequenceInfoBiz", "getSequence",
//					new String[] {String.class.getName(),String.class.getName()}, new Object[] {sequenceName,tokenString});
//			String result =  (String)sequObject;
//			return result;
//		} catch (Exception e) {
//			log.error("获取自增序列失败",e);
//			throw e;
//		}
	}

}
