package com.ly.mp.busicen.rule.field.execution;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

public class ValidResult implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String field;
	private String fieldName;
	private boolean isValid;
	private List<String> errors = new ArrayList<String>();
	
	public ValidResult addError(String error) {
		errors.add(error);
		return this;
	}
	
	public String getFieldName() {
		return fieldName;
	}

	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}

	public String getField() {
		return field;
	}
	public void setField(String field) {
		this.field = field;
	}
	public boolean isValid() {
		return isValid;
	}
	public void setValid(boolean isValid) {
		this.isValid = isValid;
	}
	public List<String> getErrors() {
		return errors;
	}
	public void setErrors(List<String> errors) {
		this.errors = errors;
	}
	
	@Override
	public String toString() {
		if (isValid) {
			return String.format("[%s-(校验成功)]", fieldName);
		}else {
			String errstr = StringUtils.join(errors, ",");
			return String.format("[%s-(%s)]", fieldName,errstr);
		}
	}
	
}
