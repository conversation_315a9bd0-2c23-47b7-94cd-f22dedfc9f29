package com.ly.mp.busicen.rule.flow.filter;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.IFlowVolume;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionExecute;

public interface FlowInvocation {
	
	IAction action();
	IDataVolume dataVolume();
	IFlowVolume flowVolume();
	String statement();
	IActionExecute execution();
	FlowFilter invoker();

}
