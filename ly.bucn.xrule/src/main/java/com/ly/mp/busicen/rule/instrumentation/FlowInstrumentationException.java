package com.ly.mp.busicen.rule.instrumentation;

import com.ly.mp.busicen.common.context.BusicenException;

public class FlowInstrumentationException extends RuntimeException{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	public FlowInstrumentationException(String message) {
		super(message);
		initCause(BusicenException.create(message));
		
	}
	
	public FlowInstrumentationException(Throwable e) {
		super(e);
	}
	
	public static FlowInstrumentationException create(String message) {
		return new FlowInstrumentationException(message);
	}

	public static FlowInstrumentationException create(Throwable e) {
		return new FlowInstrumentationException(e);
	}

	
}
