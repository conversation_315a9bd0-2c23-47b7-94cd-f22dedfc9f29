package com.ly.mp.busicen.rule.flow.engine.db;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.function.Consumer;

import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;

public class BusicenResultHandler<T> implements ResultHandler<T> {

	private final static int BATCH_SIZE = 1000;
	private int size;
	private Set<T> tc = new LinkedHashSet<T>(1000);
	private Consumer<Set<T>> consumer;
	
	public BusicenResultHandler(Consumer<Set<T>> consumer) {
		this.consumer=consumer;
	}
	
	public static <T> BusicenResultHandler<T> create(Consumer<Set<T>> consumer) {
		return new BusicenResultHandler<T>(consumer);
	}

	@Override
	public void handleResult(ResultContext<? extends T> resultContext) {
		T t = resultContext.getResultObject();
		tc.add(t);
		size++;
		if (size == BATCH_SIZE) {
			handle();
		}
	}

	private void handle() {
		try {
			consumer.accept(tc);	
		} finally {
			size = 0;
			tc.clear();
		}
	}
	
	public void submit() {
		handle();
	}

}
