package com.ly.mp.busicen.rule.flow.action.plugin;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.action.ActionException;
import com.ly.mp.busicen.rule.flow.action.IAction;

public class ActionFunctionContainer {
	
	Logger log = LoggerFactory.getLogger(ActionFunctionContainer.class);
	
	Map<String, ActionExecuteFunctionBase> funtions = new HashMap<String, ActionExecuteFunctionBase>();
	
	Map<String, IActionDone<IAction,IDataVolume,String[],IFuncResult>> funcMap = new HashMap<>();
	
	public ActionFunctionContainer regist(String funcName,IActionDone<IAction,IDataVolume,String[],IFuncResult> func) {
		if (funcMap.containsKey(funcName)) {
			log.error("注册规则引擎function名称重复【{}】",funcName);
			throw new RuntimeException("注册规则引擎function名称重复："+funcName);
		}
		funcMap.put(funcName, func);
		return this;
	}
	
	IActionDone<IAction,IDataVolume,String[],IFuncResult> func(String funcName){
		IActionDone<IAction,IDataVolume,String[],IFuncResult> functionBase = funcMap.get(funcName);
		if (functionBase==null) {
			String error = String.format("FUNCTION自定义actionfunc【%s】不存在", funcName);
			throw new ActionException(error);
		}
		return functionBase;
	}
	
	ActionExecuteFunctionBase function(String function) {
		ActionExecuteFunctionBase functionBase = funtions.get(function);
		if (functionBase==null) {
			String error = String.format("自定义【%s】节点不存在", function);
			throw new ActionException(error);
		}
		return functionBase;
	}
	

	Map<String, ActionExecuteFunctionBase> getFuntions() {
		return funtions;
	}

	void setFuntions(Map<String, ActionExecuteFunctionBase> funtions) {
		this.funtions = funtions;
	}
	
}
