package com.ly.mp.busicen.rule.flow.action;

public class ActionResult implements IActionResult{
	
	private Signal signal;
	
	private String action;	
	
	private String nextAction;
	
	private Object data;
	
	private Throwable excpt;
	
	private String msg;
	
	private boolean convertMsg=true;;
	
	public static ActionResult create() {
		return new ActionResult();
	}

	@Override
	public Signal signal() {
		return signal;
	}

	@Override
	public String action() {
		return action;
	}

	@Override
	public String nextAction() {
		return nextAction;
	}

	@Override
	public String msg() {
		return msg;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public <T> T data() {
		return (T) data;
	}

	@Override
	public Throwable excpt() {
		return excpt;
	}
	
	@Override
	public boolean convertMsg() {
		return convertMsg;
	}	

	public void signal(Signal signal) {
		this.signal = signal;
	}

	public void action(String action) {
		this.action = action;
	}
	

	public void nextAction(String nextAction) {
		this.nextAction = nextAction;
	}

	public void msg(String msg) {
		this.msg = msg;
	}

	public void data(Object data) {
		this.data = data;
	}

	public void excpt(Throwable excpt) {
		this.excpt = excpt;
	}

	public Signal getSignal() {
		return signal;
	}

	public void setSignal(Signal signal) {
		this.signal = signal;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getNextAction() {
		return nextAction;
	}

	public void setNextAction(String nextAction) {
		this.nextAction = nextAction;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	public Throwable getExcpt() {
		return excpt;
	}

	public void setExcpt(Throwable excpt) {
		this.excpt = excpt;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public boolean isConvertMsg() {
		return convertMsg;
	}

	public void setConvertMsg(boolean convertMsg) {
		this.convertMsg = convertMsg;
	}
			

}
