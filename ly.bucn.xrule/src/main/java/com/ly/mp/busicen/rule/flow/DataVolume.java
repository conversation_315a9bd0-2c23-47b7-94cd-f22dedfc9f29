package com.ly.mp.busicen.rule.flow;

import java.util.HashMap;
import java.util.Map;

import com.ly.mp.busicen.common.constant.UserBusiEntity;

public class DataVolume implements IDataVolume{
	
	private UserBusiEntity user;
	
	private Map<String, Object> data = new HashMap<String, Object>();
	
	private Map<String, Object> ext = new HashMap<String, Object>();
	
	private Map<String, Object> end = new HashMap<String, Object>();

	@Override
	public UserBusiEntity user() {
		return user;
	}

	@Override
	public Map<String, Object> data() {
		return data;
	}

	@Override
	public Map<String, Object> ext() {
		return ext;
	}
	
	@Override
	public Map<String, Object> end() {
		return end;
	}	

	public UserBusiEntity getUser() {
		return user;
	}

	public void setUser(UserBusiEntity user) {
		this.user = user;
	}

	public Map<String, Object> getData() {
		return data;
	}

	public void setData(Map<String, Object> data) {
		this.data = data;
	}

	public Map<String, Object> getExt() {
		return ext;
	}

	public void setExt(Map<String, Object> ext) {
		this.ext = ext;
	}	

	public Map<String, Object> getEnd() {
		return end;
	}

	public void setEnd(Map<String, Object> end) {
		this.end = end;
	}		

}
