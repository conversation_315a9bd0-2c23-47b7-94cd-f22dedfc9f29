package com.ly.mp.busicen.rule.field.validator;

import static com.ly.mp.busicen.rule.XruleStrUtils.splitJKH;
import static com.ly.mp.busicen.rule.XruleStrUtils.splitXKH;
import static com.ly.mp.busicen.rule.XruleStrUtils.splitZKH;

import java.io.Serializable;

public class Validation implements IValidation, Serializable {

	public static Validation build(String statement) {
		Validation validation = new Validation();
		validation.setStatement(statement);
		return validation;
	}

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * <length>(10,20)[COM-V-length]
	 */
	private String statement;

	public String getStatement() {
		return statement;
	}

	public void setStatement(String statement) {
		this.statement = statement;
	}
	
	public static void main(String[] args) {
		Validation ss = new Validation();
		ss.statement = "<spel>((#data[parentCode]<>!=null && #data[parentCode]!='')||#data[varietyType]=='0')[PRC-V-00024]";
		ss.statement = "<spel>()[]";
		ss.statement = "<spel>";
		System.out.println(ss.name());
		System.out.println(ss.strParam());
		System.out.println(ss.strMsg());
	}

	@Override
	public String statement() {
		return getStatement();
	}

	@Override
	public String name() {
		return splitJKH(statement);
	}

	@Override
	public String strParam() {
		return splitXKH(statement);
	}

	@Override
	public String strMsg() {
		return splitZKH(statement);
	}

}
