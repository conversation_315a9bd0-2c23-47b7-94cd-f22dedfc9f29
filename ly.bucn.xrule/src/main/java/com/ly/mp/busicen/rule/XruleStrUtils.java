package com.ly.mp.busicen.rule;

import static java.lang.Character.isWhitespace;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;

public class XruleStrUtils {
	
	public final static String FENHAO_STR=";";
	public final static String DOUHAO_STR=",";
	public static final char LF = '\n';
	public static final char CR = '\r';
	public static final String EMPTY = "";

	public static Map<String, String> splitParam(String str) {
		if (isEmpty(str)) {
			return null;
		}
		List<String> params = splitToList(str,DOUHAO_STR);
		Map<String, String> map = new HashMap<>();
		for (String m : params) {
			StringTokenizer st = new StringTokenizer(m,":");
			while (st.hasMoreTokens()) {
				String token = st.nextToken();
				String last = m.replaceFirst(token+":", "");
				map.put(token.trim(), last.trim());
				break;
			}
		}
		return map;		
	}
	
	
	public static List<String> splitToList(String content,String seprator){
		if (isEmpty(content)||isEmpty(seprator)) {
			return new ArrayList<String>();
		}
		content = chomp(content);
		return Arrays.asList(content.split(seprator));
	}
	
	 public static String chomp(final String str) {
		if (isEmpty(str)) {
	            return str;
        }

        if (str.length() == 1) {
            final char ch = str.charAt(0);
            if (ch == CR || ch == LF) {
                return EMPTY;
            }
            return str;
        }

        int lastIdx = str.length() - 1;
        final char last = str.charAt(lastIdx);

        if (last == LF) {
            if (str.charAt(lastIdx - 1) == CR) {
                lastIdx--;
            }
        } else if (last != CR) {
            lastIdx++;
        }
        return str.substring(0, lastIdx);
	}
	
	public static String[] splitToArray(String content,String seprator){
		if (isEmpty(content)||isEmpty(seprator)) {
			return null;
		}
		return content.split(seprator);
	}

	/**
	 * 拆分尖括号
	 * 
	 * @param content
	 * @return
	 */
	public static String splitJKH(String content) {
		if (isEmpty(content)) {
			return null;
		}
		int start = 0;
		int startFlag = 0;
		int endFlag = 0;
		for (int i = 0; i < content.length(); i++) {
			if (content.charAt(i) == '<') {
				startFlag++;
				if (startFlag == endFlag + 1) {
					start = i;
				}
			} else if (content.charAt(i) == '>') {
				endFlag++;
				if (endFlag == startFlag) {
					return content.substring(start + 1, i);
				}
			}
		}
		return null;
	}

	/**
	 * 拆分小括号
	 * 
	 * @param content
	 * @return
	 */
	public static String splitXKH(String content) {
		if (isEmpty(content)) {
			return null;
		}
		boolean notStart = true;
		int start = 0;
		int end = 0;
		for (int i = 0; i < content.length(); i++) {
			if (notStart && content.charAt(i) == '(') {
				start = i;
				notStart = false;
			}
			if (content.charAt(i) == ')') {
				end = i;
			}
		}
		if (notStart) {
			return null;
		}
		return content.substring(start + 1, end);
	}

	/**
	 * 拆分中括号
	 * 
	 * @param content
	 * @return
	 */
	public static String splitZKH(String content) {
		if (isEmpty(content)) {
			return null;
		}
		int startFlag = 0;
		int endFlag = 0;
		for (int i = content.length()-1; i > -1; i--) {
			if (content.charAt(i) == ']') {
				endFlag = i;
			} else if (content.charAt(i) == '[') {
				startFlag = i;
				return content.substring(startFlag + 1, endFlag);
			}
		}
		return null;
	}
	
	public static boolean isEmpty(final CharSequence str) {
		if (str == null || str.length() == 0) {
			return true;
		}
		for (int i = 0, length = str.length(); i < length; i++) {
			if (!isWhitespace(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}

}
