package com.ly.mp.busicen.rule.flow.filter.plugin;

import static com.ly.mp.busicen.rule.XruleStrUtils.splitXKH;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterBase;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;

/**
 * 数据源切换过滤器，用于切换数据源
 * <AUTHOR>
 *
 */
@Component
public class SwithDbFilter extends FlowFilterBase{
	
	Logger log = LoggerFactory.getLogger(SwithDbFilter.class);

	@Override
	public FlowResult doInvoke(FlowInvocation invocation) {		
		String param = splitXKH(invocation.statement());
		if (StringUtils.isEmpty(param)) {
			log.info("流程【{}】节点【{}】切换数据源为汇总库",invocation.flowVolume().flow(),invocation.action().action());
			return SwitchDbInvoke.invokeTidb(()->invocation.invoker().invoke(invocation));
		}else {
			log.info("流程【{}】节点【{}】切换数据源为{}库",invocation.flowVolume().flow(),invocation.action().action(),param);
			return SwitchDbInvoke.invoke(param, ()->invocation.invoker().invoke(invocation));
		}
	}

	@Override
	public String fileterName() {
		return "swithdb";
	}

}
