package com.ly.mp.busicen.rule.flow;

import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.ly.mp.busicen.rule.config.XruleConfig;
import com.ly.mp.busicen.rule.flow.action.ActionConfig;
import com.ly.mp.busicen.rule.flow.engine.db.MbSqlLogInterceptor;
import com.ly.mp.busicen.rule.instrumentation.FlowInstrumentationConfig;



@Configuration
@Import({FlowActionLoader.class,ActionConfig.class,FlowInstrumentationConfig.class})
@AutoConfigureBefore(FlowActionLoader.class)
public class FlowConfig {
	
	@Bean
	public IFireFlow fireFlow() {
		return new FireFlow();
	}
	
	@Bean
	public IFireFlowFocus fireFlowFocus() {
		return new FireFlowFocus();
	}
	
	@Bean
	public ActionContainer actionContainer() {
		return new ActionContainer();
	}
	
	@Bean
	public IFlowExecute flowExecute() {
		return new FlowExecute();
	}
	
	@ConditionalOnProperty(name = "xrule.debug.enable",havingValue = "true",matchIfMissing = false)
	@Bean
	public MbSqlLogInterceptor mbSqlLogInterceptor() {
		return new MbSqlLogInterceptor();
	}
	
	@Bean
	@ConditionalOnMissingBean
	public XruleConfig xruleConfig() {
		return XruleConfig.createBucn();
	}
	
}
