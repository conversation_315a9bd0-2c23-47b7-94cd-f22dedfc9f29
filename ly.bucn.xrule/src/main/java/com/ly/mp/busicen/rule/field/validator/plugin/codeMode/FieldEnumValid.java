package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;

import java.util.Optional;
import java.util.stream.Stream;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;



public class FieldEnumValid extends ValidatorBaseAbstract{

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)) {
			if (!StringUtils.isEmpty(validation.strParam())) {
				String[] mm = validation.strParam().split(",");
				Optional<String> opt = Stream.of(mm).filter(m->value.equals(m)).findAny();
				return opt.isPresent();				
			}
		}
		return true;
	}
	
	

	@Override
	public String validName() {
		return "enum";
	}
	
	@Override
	public ValidMsg errorMsg(IValidation validation) {		
		ValidMsg validMsg = super.errorMsg(validation);
		String param= validation.strParam();
		String[] params = new String[]{param};
		validMsg.setParams(params);		
		return validMsg;
	}

}
