package com.ly.mp.busicen.rule.field.execution;

import java.util.List;
import java.util.Map;

import com.ly.mp.busicen.rule.field.validator.Validation;

public class ValidFieldData {
	
	private String field;
	
	private String filedName;
	
	private Object value;
	
	private Map<String, ?> source;
	
	private boolean allCheck=false;	
	
	private List<Validation> validations;
	
	public boolean isAllCheck() {
		return allCheck;
	}

	public void setAllCheck(boolean allCheck) {
		this.allCheck = allCheck;
	}	

	public String getFiledName() {
		return filedName;
	}

	public void setFiledName(String filedName) {
		this.filedName = filedName;
	}

	public String getField() {
		return field;
	}

	public void setField(String field) {
		this.field = field;
	}

	public Object getValue() {
		return value;
	}

	public void setValue(Object value) {
		this.value = value;
	}

	public List<Validation> getValidations() {
		return validations;
	}

	public void setValidations(List<Validation> validations) {
		this.validations = validations;
	}

	public Map<String, ?> getSource() {
		return source;
	}

	public void setSource(Map<String, ?> source) {
		this.source = source;
	}	
	
}
