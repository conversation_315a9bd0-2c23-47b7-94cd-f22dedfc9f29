package com.ly.mp.busicen.rule.flow.filter.plugin;

import static com.ly.mp.busicen.rule.XruleStrUtils.splitParam;
import static com.ly.mp.busicen.rule.XruleStrUtils.splitXKH;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.flow.FlowSpelUtil;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterBase;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;
import com.ly.mp.busicen.rule.flow.filter.FlowResultImpl;


@Component
public class CatchFilter extends FlowFilterBase{
	
	Logger log = LoggerFactory.getLogger(CatchFilter.class);

	@Override
	public FlowResult doInvoke(FlowInvocation invocation) {
		String paramStr = splitXKH(invocation.statement());

		FlowResult result = invocation.invoker().invoke(invocation);
		if (result.actionResult().signal()==Signal.BREAK||result.actionResult().signal()==Signal.EXCPT) {
			String catchCondition = null;
			String nextActionZone = null;
			String actionResultZone = null;
			if (!StringUtils.isEmpty(paramStr)) {
				Map<String, String> params = splitParam(paramStr);
				catchCondition = params.get("condition");
				nextActionZone = params.get("nextaction");
				actionResultZone = params.get("result");
				if (StringUtils.isEmpty(nextActionZone)) {
					log.info("流程【{}】节点【{}】异常处理不生效，未指定异常处理节点",invocation.flowVolume().flow(),invocation.action().action());
					return result;
				}
				if (!StringUtils.isEmpty(catchCondition)) {
					
					if ("break".equals(catchCondition)&&result.actionResult().signal()!=Signal.BREAK) {
						log.info("流程【{}】节点【{}】异常处理不生效，结果状态为【{}】",invocation.flowVolume().flow(),invocation.action().action(),result.actionResult().signal());
						return result;
					}
					if ("excpt".equals(catchCondition)&&result.actionResult().signal()!=Signal.EXCPT) {
						log.info("流程【{}】节点【{}】异常处理不生效，结果状态为【{}】",invocation.flowVolume().flow(),invocation.action().action(),result.actionResult().signal());
						return result;
					}
					
				}
				if (!StringUtils.isEmpty(actionResultZone)) {
					log.info("流程【{}】节点【{}】设置异常结果到【{}】",invocation.flowVolume().flow(),invocation.action().action(),actionResultZone);
					FlowSpelUtil.spelSetData(invocation.action(), actionResultZone, result.actionResult());					
				}
				
				String nextAction = FlowSpelUtil.spelGetData(invocation.action(), nextActionZone, String.class);
				log.info("流程【{}】节点【{}】异常处理生效，结果状态为【{}】，下一个节点为【{}】",invocation.flowVolume().flow(),invocation.action().action(),result.actionResult().signal(),nextAction);
				ActionResult actionResult = ActionResult.create();
				actionResult.signal(Signal.CONTINUE);
				actionResult.action(invocation.action().action());
				actionResult.msg(invocation.action().msg());
				actionResult.data(null);
				actionResult.nextAction(nextAction);
				FlowResultImpl flowResultImpl = new FlowResultImpl();
				flowResultImpl.setActionResult(actionResult);
				return flowResultImpl;
				
			}
			else {
				log.info("流程【{}】节点【{}】异常处理不生效，因为参数为空",invocation.flowVolume().flow(),invocation.action().action());
				return result;
			}
		}
		return result;
	}
	
	

	@Override
	public String fileterName() {
		return "catch";
	}

}
