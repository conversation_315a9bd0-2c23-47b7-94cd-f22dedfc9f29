package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;


/**
 * <rang>(10.2,200.4)[]
 * 
 * <AUTHOR>
 *
 */

public class FieldRangeValid extends ValidatorBaseAbstract {

	@Override
	public String validName() {
		return "range";
	}

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value = validData.value();
		if (!StringUtils.isEmpty(value)) {
			String param = validation.strParam();
			if (!StringUtils.isEmpty(param)) {
				String[] mm = param.split(",");
				if (mm.length == 2) {
					Double v = Double.parseDouble(value.toString());
					return v >= min(mm[0]) && v <= max(mm[1]);
				}
			}

		}
		return true;
	}

	private Double min(String min) {
		if (StringUtils.isEmpty(min)) {
			return Double.MIN_VALUE;
		}
		return Double.parseDouble(min);
	}

	private Double max(String max) {
		if (StringUtils.isEmpty(max)) {
			return Double.MAX_VALUE;
		}
		return Double.parseDouble(max);
	}

	@Override
	public ValidMsg errorMsg(IValidation validation) {
		ValidMsg validMsg = super.errorMsg(validation);
		String[] params = validation.strParam().split(",");
		validMsg.setParams(params);
		return validMsg;
	}

}
