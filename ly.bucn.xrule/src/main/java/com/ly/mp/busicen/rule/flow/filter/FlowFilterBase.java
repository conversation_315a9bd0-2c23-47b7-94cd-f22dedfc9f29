package com.ly.mp.busicen.rule.flow.filter;

import org.springframework.beans.factory.annotation.Autowired;

import com.ly.mp.busicen.rule.instrumentation.IFlowInstrumentation;

public abstract class FlowFilterBase implements FlowFilter {

	@Autowired
	IFlowInstrumentation instrumentation;

	@Override
	public FlowResult invoke(FlowInvocation invocation) {
		instrumentation.beginFilter(this, invocation);
		FlowResult result = doInvoke(invocation);
		instrumentation.endFilter(this, invocation, result);
		return result;
	}

	public abstract FlowResult doInvoke(FlowInvocation invocation);
}
