package com.ly.mp.busicen.rule.field;

import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Primary;

import com.ly.mp.busicen.rule.IRuleReload;
import com.ly.mp.busicen.rule.RuleReLoad;
//import com.ly.mp.busicen.rule.RuleReloadService;
import com.ly.mp.busicen.rule.field.article.FieldArticleContainer;
import com.ly.mp.busicen.rule.field.execution.IValidExecution;
import com.ly.mp.busicen.rule.field.execution.ValidExecution;
import com.ly.mp.busicen.rule.field.msg.FieldMsgContainer;
import com.ly.mp.busicen.rule.field.validator.ValidatorContainer;

@Configuration
@Import({ /* RuleReloadService.class, */FiledRuleLoader.class})
@AutoConfigureBefore({ /* RuleReloadService.class, */FiledRuleLoader.class})
public class FieldRuleConfig {
	
	public FieldRuleConfig() {
		
	}	
	
	@Bean
	public ValidatorContainer validatorContainer() {
		return new ValidatorContainer();
	}
	
	@Bean
	public FieldMsgContainer FieldErrorContainer() {
		return new FieldMsgContainer();
	}
	
	@Bean
	public FieldArticleContainer fieldArticleContainer() {
		return new FieldArticleContainer();
	}
	
	@Bean
	public IFireFieldRule fireRule() {
		return new FireFieldRule();
	}
	
	@Bean
	public IValidExecution validExecution() {
		return new ValidExecution();
	}
	
	@Primary
	@Bean
	public IRuleReload ruleReload() {
		return new RuleReLoad();
	}

}
