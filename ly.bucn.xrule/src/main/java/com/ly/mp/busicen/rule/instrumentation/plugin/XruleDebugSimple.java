
package com.ly.mp.busicen.rule.instrumentation.plugin;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.IFlowContext;
import com.ly.mp.busicen.rule.flow.IFlowResult;
import com.ly.mp.busicen.rule.flow.IFlowResultCtn;
import com.ly.mp.busicen.rule.flow.IFlowVolume;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.action.OperationType;
import com.ly.mp.busicen.rule.flow.filter.FlowFilter;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;

@XdebugConf(flow = { "PRC_REMIND_004"})
public class XruleDebugSimple implements IXruleDebug {

	/**
	 * 节点开始执行
	 * @param action
	 * @param context
	 * @param resultCtn
	 */
	@Override
	@XdebugConditon(action = {"BEGIN"},spelCondition = {"ext[PKCHECK]!=null"})
	public void actionInvokePre(IAction action, IFlowContext context, IFlowResultCtn resultCtn) {

	}
	/**
	 * 过滤器开始执行
	 * @param flowFilter
	 * @param invocation
	 */
	@Override
	@XdebugConditon(action = {"a"},filter = {"swithdb"})
	public void beginFilter(FlowFilter flowFilter, FlowInvocation invocation) {

	}
	
	/**
	 * 过滤器执行结束
	 */
	@Override
	@XdebugConditon(action = {"a"},filter = {"swithdb","execution"})
	public void endFilter(FlowFilter flowFilter, FlowInvocation invocation, FlowResult filterResult) {
		
	}
	
	/**
	 * 规则开始执行
	 * @param data
	 * @param context
	 * @param action
	 * @param dataVolume
	 * @param flowVolume
	 */
	@Override
	@XdebugConditon(action = {"UPDATE_D"},spelCondition = {"ext[EXIT]!=null"})
	public void actionExecutePre(Object data, String context, IAction action, IDataVolume dataVolume,
			IFlowVolume flowVolume) {

	}
	/**
	 * 规则执行结束
	 * @param result
	 * @param signal
	 * @param action
	 * @param dataVolume
	 * @param flowVolume
	 * @param actionResult
	 */
	@Override
	@XdebugConditon(action = {""},spelCondition = {"ext[UPDATE]!=null"})
	public void actionExecutePost(Object result, Signal signal, IAction action, IDataVolume dataVolume,
			IFlowVolume flowVolume, IActionResult actionResult) {

	}
	/**
	 * 节点执行结束
	 * @param result
	 * @param signal
	 * @param action
	 * @param context
	 * @param resultCtn
	 * @param flowResult
	 */
	@Override
	@XdebugConditon(action = {"a"},operationType = OperationType.SCRIPT,spelCondition = {"ext[EXIT]!=null"},resultSignal = {Signal.BREAK})
	public void actionInvokePost(Object result, Signal signal,IAction action, IFlowContext context, IFlowResultCtn resultCtn,
			IFlowResult flowResult) {

	}
	/**
	 * 流程结束
	 * @param result
	 */
	@Override
	public void endFlow(IFlowResultCtn result) {
		result.flowContext().flowVolume().flow();
		result.flowContext().dataVolume();
	}
	

}
