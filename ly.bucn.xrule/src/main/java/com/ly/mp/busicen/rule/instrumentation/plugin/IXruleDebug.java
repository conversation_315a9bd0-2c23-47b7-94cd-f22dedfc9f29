package com.ly.mp.busicen.rule.instrumentation.plugin;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.IFlowContext;
import com.ly.mp.busicen.rule.flow.IFlowResult;
import com.ly.mp.busicen.rule.flow.IFlowResultCtn;
import com.ly.mp.busicen.rule.flow.IFlowVolume;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.filter.FlowFilter;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;

public interface IXruleDebug {
	
	/**
	 * 节点开始执行
	 * @param action
	 * @param context
	 * @param resultCtn
	 */
	public void actionInvokePre(IAction action, IFlowContext context, IFlowResultCtn resultCtn);
	/**
	 * 过滤器开始执行
	 * @param flowFilter
	 * @param invocation
	 */
	public void beginFilter(FlowFilter flowFilter, FlowInvocation invocation);
	
	/**
	 * 过滤器結束执行
	 * @param flowFilter
	 * @param invocation
	 */
	public void endFilter(FlowFilter flowFilter, FlowInvocation invocation,FlowResult filterResult);
	/**
	 * 规则开始执行
	 * @param data
	 * @param context
	 * @param action
	 * @param dataVolume
	 * @param flowVolume
	 */
	public void actionExecutePre(Object data, String context, IAction action, IDataVolume dataVolume,
			IFlowVolume flowVolume) ;
	/**
	 * 规则执行结束
	 * @param result
	 * @param signal
	 * @param action
	 * @param dataVolume
	 * @param flowVolume
	 * @param actionResult
	 */
	public void actionExecutePost(Object result, Signal signal, IAction action, IDataVolume dataVolume,
			IFlowVolume flowVolume, IActionResult actionResult);
	/**
	 * 节点执行结束
	 * @param result
	 * @param signal
	 * @param action
	 * @param context
	 * @param resultCtn
	 * @param flowResult
	 */
	public void actionInvokePost(Object result, Signal signal,IAction action, IFlowContext context, IFlowResultCtn resultCtn,
			IFlowResult flowResult);
	/**
	 * 流程结束
	 * @param result
	 */
	public void endFlow(IFlowResultCtn result);


}
