package com.ly.mp.busicen.rule.extention.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

import com.ly.mp.busicen.rule.extention.auto.XruleScannerRegistrar;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(XruleScannerRegistrar.class)
public @interface XruleScan {
	String[] packages() default {};
}
