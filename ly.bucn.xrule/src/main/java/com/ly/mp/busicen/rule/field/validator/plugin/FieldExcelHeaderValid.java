package com.ly.mp.busicen.rule.field.validator.plugin;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;
import com.ly.mp.busicen.rule.field.validator.ValidatorBase;

@Component
public class FieldExcelHeaderValid extends ValidatorBase{

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Map<String, ?> source =  validData.source();
		if (!StringUtils.isEmpty(validation.strParam())) {
			String[] mm = validation.strParam().split(",");
			Optional<String> opt = Stream.of(mm).filter(m->!source.containsKey(m)).findAny();
			return !opt.isPresent();				
		}
		return true;
	}

	@Override
	public String validName() {
		return "excelheader";
	}
	
	@Override
	public ValidMsg errorMsg(IValidation validation) {		
		ValidMsg validMsg = super.errorMsg(validation);
		String param= validation.strParam();
		String[] params = new String[]{param};
		validMsg.setParams(params);		
		return validMsg;
	}

}
