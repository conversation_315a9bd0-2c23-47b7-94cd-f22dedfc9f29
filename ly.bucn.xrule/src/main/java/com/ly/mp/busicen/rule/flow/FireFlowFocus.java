package com.ly.mp.busicen.rule.flow;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

public class FireFlowFocus implements IFireFlowFocus{
	
	ThreadLocal<FlowDataTemp> dt = new ThreadLocal<FireFlowFocus.FlowDataTemp>();
	
	@Autowired
	IFireFlow fireFlow;

	@Override
	public IFireFlowFocus flow(String flow) {
		get().flow=flow;
		return this;
	}

	@Override
	public IFireFlowFocus add(String dataNode, Object obj) {
		get().data.put(dataNode, obj);
		return this;
	}

	@Override
	public IFlowResultCtn fire() {
		FlowDataTemp dtt = get();
		dt.remove();		
		return fireFlow.fire(dtt.flow,dtt.brand, dtt.data);
	}
	
	FlowDataTemp get() {
		FlowDataTemp dtt = dt.get();
		if (dtt==null) {
			dtt = new FlowDataTemp();
			dt.set(dtt);
		}
		return dtt;
	}
	
	class FlowDataTemp{
		String flow;
		String brand;
		Map<String, Object> data=new HashMap<String, Object>();
		
	}

	@Override
	public IFlowResultCtn fireExcpt() {
		FlowDataTemp dtt = get();
		dt.remove();		
		return fireFlow.fireExcpt(dtt.flow,dtt.brand, dtt.data);
	}

	@Override
	public IFireFlowFocus brand(String brand) {
		get().brand=brand;
		return this;
	}

}
