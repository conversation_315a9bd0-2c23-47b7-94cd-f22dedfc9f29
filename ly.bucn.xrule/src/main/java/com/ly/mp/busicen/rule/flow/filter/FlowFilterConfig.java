package com.ly.mp.busicen.rule.flow.filter;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan("com.ly.mp.busicen.rule.flow.filter.plugin")
public class FlowFilterConfig implements ApplicationContextAware{

	private ApplicationContext application;
	
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		application = applicationContext;
	}
	
	@Bean
	public FlowFilterExecuteImpl flowFilterExecuteImpl() {
		return new FlowFilterExecuteImpl();
	}
	
	@Bean
	public FlowFilterContainer flowFilterContainer() {
		return new FlowFilterContainer();
	}
	
	@PostConstruct
	public void init() {
		
		Map<String, FlowFilter> filters = application.getBeansOfType(FlowFilter.class);
		Map<String, FlowFilter> filters2 = new HashMap<String, FlowFilter>();
		filters.forEach((k,v)->{
			filters2.put(v.fileterName(), v);
		});
		flowFilterContainer().setFilters(filters2);
		
	}
	
	

}
