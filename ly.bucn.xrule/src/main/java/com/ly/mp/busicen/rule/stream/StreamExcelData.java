package com.ly.mp.busicen.rule.stream;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

public class StreamExcelData {
	
	private boolean isdes;
	
	private List<StreamExcelDesensitize> desensitize;
	
	private String title;
	
	private String[][] columns;
	
	private Consumer<Set<?>> data;
	
	private Map<String,StreamExcelConvert> converts;

	private Map<String,StreamExcelMerge> mergeCols;
	
	private StreamExcelExtend extend;	
	
	public StreamExcelExtend getExtend() {
		return extend;
	}

	public void setExtend(StreamExcelExtend extend) {
		this.extend = extend;
	}

	public Map<String, StreamExcelMerge> getMergeCols() {
		return mergeCols;
	}

	public void setMergeCols(Map<String, StreamExcelMerge> mergeCols) {
		this.mergeCols = mergeCols;
	}	

	public Map<String, StreamExcelConvert> getConverts() {
		return converts;
	}

	public void setConverts(Map<String, StreamExcelConvert> converts) {
		this.converts = converts;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String[][] getColumns() {
		return columns;
	}

	public void setColumns(String[][] columns) {
		this.columns = columns;
	}

	public Consumer<Set<?>> getData() {
		return data;
	}

	public void setData(Consumer<Set<?>> data) {
		this.data = data;
	}
	
	public boolean isIsdes() {
		return isdes;
	}

	public void setIsdes(boolean isdes) {
		this.isdes = isdes;
	}

	public List<StreamExcelDesensitize> getDesensitize() {
		return desensitize;
	}

	public void setDesensitize(List<StreamExcelDesensitize> desensitize) {
		this.desensitize = desensitize;
	}

}
