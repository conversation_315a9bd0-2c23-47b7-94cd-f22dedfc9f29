package com.ly.mp.busicen.rule.field.validator.plugin;

import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidatorBase;

@Component
public class FieldNumberOrderValid extends ValidatorBase {

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		String param = validation.strParam();
		if (!StringUtils.isEmpty(param)) {
			String[] mm = validation.strParam().split(",");
			if (mm.length>1) {
				Double[] values = new Double[mm.length];
				if (convertValues(validData, mm, values)) {
					for (int i = 0; i < values.length-1; i++) {
						if (values[i]>values[i+1]) {
							return false;
						}
					}
				}
			}			
		}
		return true;
	}
	
	Boolean convertValues(IValidData validData,String[] keys,Double[] values) {
		Map<String, ?> data = validData.source();
		for (int i = 0; i < keys.length; i++) {
			Object value = data.get(keys[i]);
			if (StringUtils.isEmpty(value)) {
				return false;
			}
			Double temp = Double.parseDouble(value.toString());
			values[i]=temp;
		}
		
		return true;
	}
	

	@Override
	public String validName() {
		return "numberorder";
	}

}
