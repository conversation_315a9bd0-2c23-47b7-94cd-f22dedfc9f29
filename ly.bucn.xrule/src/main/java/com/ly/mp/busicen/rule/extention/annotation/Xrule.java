package com.ly.mp.busicen.rule.extention.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.ly.mp.busicen.rule.flow.IFireFlow;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
public @interface Xrule {
	
	Class<? extends IFireFlow> fireFlowEngine() default IFireFlow.class;
	String fireFlowEngineName() default "";

}
