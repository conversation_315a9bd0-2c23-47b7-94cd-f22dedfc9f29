package com.ly.mp.busicen.rule.instrumentation;

import com.ly.mp.busicen.rule.flow.IFlowContext;
import com.ly.mp.busicen.rule.flow.action.IAction;

public class FlowInstruUtils {
	
	public static String getXruleRid(IAction action) {
		return String.valueOf(action.extention().get(IAction.EXTKEY_RID));
	}
	
	public static String getXruleRid(IFlowContext context) {
		return context.flowVolume().rid();
	}

}
