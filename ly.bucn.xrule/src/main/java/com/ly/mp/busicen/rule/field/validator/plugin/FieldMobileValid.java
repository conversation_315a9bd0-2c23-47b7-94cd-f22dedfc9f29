package com.ly.mp.busicen.rule.field.validator.plugin;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;
import com.ly.mp.busicen.rule.field.validator.ValidatorBase;

@Component
public class FieldMobileValid extends ValidatorBase{

	static final String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[5|8|9]))\\d{8}$";
	
	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)) {
			String mobile = value.toString();
		    if (mobile.length() != 11) {
		        return false;
		    } else {
		        Pattern p = Pattern.compile(regex);
		        Matcher m = p.matcher(mobile);
		        return m.matches();
		    }
		}
		return true;
	}

	@Override
	public String validName() {
		return "mobile";
	}
	
	@Override
	public ValidMsg errorMsg(IValidation validation) {
		if (StringUtils.isEmpty(validation.strMsg())) {
			ValidMsg validMsg = new ValidMsg();
			validMsg.setMsg("COM-V-mobile");
			return validMsg;
		}
		return super.errorMsg(validation);
	}

}
