package com.ly.mp.busicen.rule.flow.action;

public enum OperationType{
	SELECTONE("SELECTONE"),
	SELECTLIST("SELECTLIST"),
	UPDATE("UPDATE"),
	DELETE("DELETE"),
	INSERT("INSERT"),
	DATACHECK("DATAC<PERSON>ECK"),
	<PERSON>IELDCHEC<PERSON>("FIELDCHECK"),
	FUNCTION("FUNCTION"),
	SPBEAN("SPBEAN"),
	SCRIPT("SCRIPT"),
	FLOW("FLOW");
	
	private String name;
	 
    private OperationType(String name) {
        this.name = name;
    }
 
    public String getName() {
        return name;
    }		
}
