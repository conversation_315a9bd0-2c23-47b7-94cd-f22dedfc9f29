package com.ly.mp.busicen.rule.flow.action.plugin;

import static com.ly.mp.busicen.rule.XruleStrUtils.splitJKH;
import static com.ly.mp.busicen.rule.XruleStrUtils.splitXKH;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.action.ActionException;
import com.ly.mp.busicen.rule.flow.action.ActionExecuteBase;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;

public class ActionExecuteFunction extends ActionExecuteBase{
	
	Logger log = LoggerFactory.getLogger(ActionExecuteFunction.class);
	
	@Autowired
	ActionFunctionContainer actionFunctionContainer;

	@Override
	public IActionResult execute(IAction action, IDataVolume dataVolume) {
		ActionResult result = ActionResult.create();
		result.action(action.action());
		try {
			String function = functionName(action.content());
			IActionDone<IAction,IDataVolume,String[],IFuncResult> actionFunc = actionFunctionContainer.func(function);
			String[] param = getParam(action.content());
			IFuncResult funcResult = actionFunc.invoke(action, dataVolume,param);
			result.data(funcResult);
			dataVolume.ext().put(action.action(), funcResult);
			if (funcResult!=null) {				
				if (funcResult.success()) {
					result.signal(Signal.CONTINUE);
					result.msg(action.msg());
					result.nextAction(defaultNextActionCode(action, funcResult.nextActionCode()));
				}else {
					log.info("FUNCTION执行结果中断");
					result.signal(Signal.BREAK);
					//特殊处理
					if (StringUtils.isEmpty(funcResult.message())) {
						result.msg(action.msg());
					}else {
						result.msg(funcResult.message());
						result.setConvertMsg(false);
					}
				}				
			}else {
				log.info("FUNCTION执行正常，暂无结果，使用默认下一个节点");
				result.signal(Signal.CONTINUE);
				result.msg(action.msg());
				result.nextAction(defaultNextActionCode(action, null));
			}
		} catch (Exception e) {
			result.signal(Signal.EXCPT);
			result.excpt(e);
		}		
		return result;
	}
	
	private String[] getParam(String content) {
		String param = splitXKH(content);
		if (StringUtils.isEmpty(param)) {
			return null;
		}else {
			return param.split(",");
		}
	}
	
	private String functionName(String content) {
		String function = getName( content);
		if (StringUtils.isEmpty(function)) {
			String error = "未设置自定义函数名称";
			throw new ActionException(error);
		}
		return function;
	}
	
	public String getName(String content) {
		return splitJKH(content);
	}
	
	@Deprecated
	public IActionResult execute2(IAction action, IDataVolume dataVolume) {
		String functionName = functionName(action.content());
		ActionExecuteFunctionBase functionBase = actionFunctionContainer.function(functionName);
		ActionResult result = ActionResult.create();
		result.signal(Signal.CONTINUE);
		result.action(action.action());
		try {
			result = (ActionResult) functionBase.execute(action, dataVolume);
		} catch (Exception e) {
			result.signal(Signal.EXCPT);
			result.excpt(e);
		}
		return result;
	}
	
	

}
