package com.ly.mp.busicen.rule.field.article.ext;

import java.io.Serializable;
import java.time.LocalDateTime;

public class ArticleSence implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程与节点ID
     */
    private Long businessflowSenceId;

    /**
     * 流程编码
     */
    private String businessflowCode;

    /**
     * 流程名称
     */
    private String businessflowName;

    /**
     * 场景节点
     */
    private String businessflowSencename;

    /**
     * 场景节点编码
     */
    private String businessflowSencecode;

    /**
     * 品牌编码
     */
    private String carBrandCode;

    /**
     * 厂商集团编码
     */
    private String oemGroupCode;

    /**
     * 启停状态
     */
    private String isEnable;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    private String modifier;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制字段
     */
    private String updateControlId;

	public Long getBusinessflowSenceId() {
		return businessflowSenceId;
	}

	public void setBusinessflowSenceId(Long businessflowSenceId) {
		this.businessflowSenceId = businessflowSenceId;
	}

	public String getBusinessflowCode() {
		return businessflowCode;
	}

	public void setBusinessflowCode(String businessflowCode) {
		this.businessflowCode = businessflowCode;
	}

	public String getBusinessflowName() {
		return businessflowName;
	}

	public void setBusinessflowName(String businessflowName) {
		this.businessflowName = businessflowName;
	}

	public String getBusinessflowSencename() {
		return businessflowSencename;
	}

	public void setBusinessflowSencename(String businessflowSencename) {
		this.businessflowSencename = businessflowSencename;
	}

	public String getBusinessflowSencecode() {
		return businessflowSencecode;
	}

	public void setBusinessflowSencecode(String businessflowSencecode) {
		this.businessflowSencecode = businessflowSencecode;
	}

	public String getCarBrandCode() {
		return carBrandCode;
	}

	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}

	public String getOemGroupCode() {
		return oemGroupCode;
	}

	public void setOemGroupCode(String oemGroupCode) {
		this.oemGroupCode = oemGroupCode;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}
    
    
	    
}
