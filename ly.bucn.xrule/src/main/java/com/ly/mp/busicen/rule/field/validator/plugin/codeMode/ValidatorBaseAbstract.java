package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;

import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;

/**
 * 	校验工具类 校验抽象类
 */

public abstract class ValidatorBaseAbstract {
	public abstract String validName();
	public abstract boolean valid(IValidData validData, IValidation validation);

	public ValidMsg errorMsg(IValidation validation) {
		ValidMsg validMsg = new ValidMsg();
		if (StringUtils.isEmpty(validation.strMsg())) {
			validMsg.setMsg("COM-V-"+validName());
		}else {
			validMsg.setMsg(validation.strMsg());
		}
		return validMsg;
	}
}