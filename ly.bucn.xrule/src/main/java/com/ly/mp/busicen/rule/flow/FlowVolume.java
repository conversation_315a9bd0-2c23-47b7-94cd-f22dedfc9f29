package com.ly.mp.busicen.rule.flow;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.ly.mp.busicen.rule.flow.action.IAction;

public class FlowVolume implements IFlowVolume{
	
	private String flow;
	
	private String rid;
	
	private List<IAction> actions = new ArrayList<>();

	@Override
	public String flow() {
		return flow;
	}
	
	@Override
	public String rid() {
		return rid;
	}

	@Override
	public List<IAction> actions() {
		return actions.stream().map(m->m).collect(Collectors.toList());
	}	

	public String getFlow() {
		return flow;
	}

	public void setFlow(String flow) {
		this.flow = flow;
	}

	public List<IAction> getActions() {
		return actions;
	}

	public void setActions(List<IAction> actions) {
		this.actions = actions;
	}

	public String getRid() {
		return rid;
	}

	public void setRid(String rid) {
		this.rid = rid;
	}
	
}