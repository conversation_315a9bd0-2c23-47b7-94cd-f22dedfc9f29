package com.ly.mp.busicen.rule.stream;

import java.io.Serializable;
import java.util.Map;

public class StreamExcelConvert implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String convert;
	private String format;
	private Map<String, String> kvs;
	
	public Map<String, String> getKvs() {
		return kvs;
	}
	public void setKvs(Map<String, String> kvs) {
		this.kvs = kvs;
	}
	public String getConvert() {
		return convert;
	}
	public void setConvert(String convert) {
		this.convert = convert;
	}
	public String getFormat() {
		return format;
	}
	public void setFormat(String format) {
		this.format = format;
	}

}
