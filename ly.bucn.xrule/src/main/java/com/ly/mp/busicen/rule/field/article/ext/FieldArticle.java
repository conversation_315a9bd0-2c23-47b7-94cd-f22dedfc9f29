package com.ly.mp.busicen.rule.field.article.ext;

import java.io.Serializable;

import com.ly.mp.busicen.rule.field.article.IFieldArticle;


public class FieldArticle implements IFieldArticle,Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String column;
	
	private String columnName;
	
	private String validContent;
	
	private String columnBrand;
	
	private String columnOem;
	
//	private String sence;
//	
//	private String senceBrand;
//	
//	private String senceOem;
	
	private String function;
	
//	private String functionBrand;
//	
//	private String functionOem;	

	@Override
	public String field() {
		
		return column;
	}

	@Override
	public String fieldName() {
		
		return columnName;
	}

	@Override
	public String articleName() {
		
		return function;
	}

	@Override
	public String statement() {
		
		return validContent;
	}

	public String getColumn() {
		return column;
	}

	public void setColumn(String column) {
		this.column = column;
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public String getValidContent() {
		return validContent;
	}

	public void setValidContent(String validContent) {
		this.validContent = validContent;
	}

	public String getColumnBrand() {
		return columnBrand;
	}

	public void setColumnBrand(String columnBrand) {
		this.columnBrand = columnBrand;
	}

	public String getColumnOem() {
		return columnOem;
	}

	public void setColumnOem(String columnOem) {
		this.columnOem = columnOem;
	}

//	public String getSence() {
//		return sence;
//	}
//
//	public void setSence(String sence) {
//		this.sence = sence;
//	}
//
//	public String getSenceBrand() {
//		return senceBrand;
//	}
//
//	public void setSenceBrand(String senceBrand) {
//		this.senceBrand = senceBrand;
//	}
//
//	public String getSenceOem() {
//		return senceOem;
//	}
//
//	public void setSenceOem(String senceOem) {
//		this.senceOem = senceOem;
//	}

	public String getFunction() {
		return function;
	}

	public void setFunction(String function) {
		this.function = function;
	}

//	public String getFunctionBrand() {
//		return functionBrand;
//	}
//
//	public void setFunctionBrand(String functionBrand) {
//		this.functionBrand = functionBrand;
//	}
//
//	public String getFunctionOem() {
//		return functionOem;
//	}
//
//	public void setFunctionOem(String functionOem) {
//		this.functionOem = functionOem;
//	}
//	
	
	
	

}
