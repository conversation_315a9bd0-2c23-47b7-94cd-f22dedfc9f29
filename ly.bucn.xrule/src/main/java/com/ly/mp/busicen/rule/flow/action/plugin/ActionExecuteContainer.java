package com.ly.mp.busicen.rule.flow.action.plugin;

import java.util.HashMap;
import java.util.Map;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ly.mp.busicen.rule.flow.action.IActionExecute;
import com.ly.mp.busicen.rule.flow.action.IActionExecuteContainer;
import com.ly.mp.busicen.rule.flow.action.OperationType;

@Configuration
public class ActionExecuteContainer implements IActionExecuteContainer {

	Map<OperationType, IActionExecute> actionExecutes = new HashMap<OperationType, IActionExecute>();
	
	
	public Map<OperationType, IActionExecute> getActionExecutes() {
		return actionExecutes;
	}

	public void setActionExecutes(Map<OperationType, IActionExecute> actionExecutes) {
		this.actionExecutes = actionExecutes;
	}
	
	@Bean
	public ActionExecuteDataCheck actionExecuteDataCheck() {
		return new ActionExecuteDataCheck();
	}
	
	@Bean
	public ActionExecuteInsert actionExecuteInsert() {
		return new ActionExecuteInsert();
	}
	
	@Bean
	public ActionExecuteDelete actionExecuteDelete() {
		return new ActionExecuteDelete();
	}
	
	@Bean
	public ActionExecuteUpdate actionExecuteUpdate() {
		return new ActionExecuteUpdate();
	}
	
	@Bean
	public ActionExecuteSelectList actionExecuteSelectList() {
		return new ActionExecuteSelectList();
	}
	
	@Bean
	public ActionExecuteSelectOne actionExecuteSelectOne() {
		return new ActionExecuteSelectOne();
	}	
	
	@Bean
	public ActionExecuteFunction actionExecuteFunction() {
		return new ActionExecuteFunction();
	}
	
	@Bean
	public ActionExecuteFieldCheck actionExecuteFieldCheck() {
		return new ActionExecuteFieldCheck();
	}
	
	@Bean
	public ActionExecuteScript actionExecuteScript() {
		return new ActionExecuteScript();
	}
	
	@Bean
	public ActionExecuteSpBean actionExecuteSpBean() {
		return new ActionExecuteSpBean();
	}
	
	@Bean
	public ActionExecuteFlow actionExecuteFlow() {
		return new ActionExecuteFlow();
	}

	@Override
	public IActionExecute actionExecute(OperationType operationType) {
		return actionExecutes.get(operationType);
	}
	
	
}
