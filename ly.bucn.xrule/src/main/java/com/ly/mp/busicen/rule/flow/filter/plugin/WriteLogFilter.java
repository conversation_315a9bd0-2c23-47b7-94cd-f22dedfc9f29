package com.ly.mp.busicen.rule.flow.filter.plugin;

import static com.ly.mp.busicen.rule.XruleStrUtils.splitXKH;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.flow.FlowSpelUtil;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterBase;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterException;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;

@Component
public class WriteLogFilter extends FlowFilterBase {

	Logger log = LoggerFactory.getLogger(WriteLogFilter.class);
	
	@Override
	public FlowResult doInvoke(FlowInvocation invocation) {
		String paramStr = splitXKH(invocation.statement());
		FlowResult result = invocation.invoker().invoke(invocation);
		if (!StringUtils.isEmpty(paramStr)) {
			List<String> params = Stream.of(paramStr.split(",")).filter(m->!StringUtils.isEmpty(m)).collect(Collectors.toList());
			//IDataVolume data = invocation.dataVolume();
			String msgId = null;
			int indexStart = 1;
			List<Object> list = new ArrayList<>();
			if("false".equals(params.get(0))) {
				//流程失败才写
				msgId = params.get(1);
				indexStart = 2;
				if (!result.actionResult().signal().equals(Signal.BREAK)) {
					return result;
				}
			}else if("true".equals(params.get(0))) {
				//流程成功才写
				msgId = params.get(1);
				indexStart = 2;
				if (!result.actionResult().signal().equals(Signal.CONTINUE)) {
					return result;
				}
			}else {
				//默认流程成功才写
				if (!result.actionResult().signal().equals(Signal.CONTINUE)) {
					return result;
				}
				msgId = params.get(0);
			}
			try {
				msgId = getMsgId(invocation, result, msgId);
			}catch(Exception e) {
				ActionResult actionResult = (ActionResult) result.actionResult();
				actionResult.action(invocation.action().action());
				actionResult.excpt(e);
				actionResult.msg(e.getMessage());
				actionResult.signal(Signal.EXCPT);
				actionResult.data(null);
				actionResult.setConvertMsg(false);
				log.info("流程【{}】节点【{}】结果改为异常，因【{}】",invocation.flowVolume().flow(),invocation.action().action(),actionResult.getMsg());
				return result;
			}
			for(int i = indexStart;i < params.size();i++) {
				String param = params.get(i);
				list.add(FlowSpelUtil.spelGetData(invocation.action(), param));
			}
			//LoggerHelper.writeBLoger(msgId,list.toArray());
			String res = result.actionResult().signal().equals(Signal.CONTINUE)?"成功":"失败";
			//log.info("流程【{}】节点【{}】结果【{}】，日志ID【{}】，存储日志【{}】",invocation.flowVolume().flow(),invocation.action().action(),res,msgId,LoggerHelper.getOrcMsg(msgId, list.toArray()));
			log.info("流程【{}】节点【{}】结果【{}】，日志ID【{}】",invocation.flowVolume().flow(),invocation.action().action(),res,msgId);

		}
		return result;
	}

	public String getMsgId(FlowInvocation invocation,FlowResult result,String msgId) {
		Object obj2 = null;
		try {
			obj2 = FlowSpelUtil.spelGetData(invocation.action(), msgId);
		}catch(Throwable e) {
			//不是动态参数 运行这个有可能会报错
			obj2 = msgId;
		}
		//动态参数获取失败
		if(obj2 == null) {
			throw new FlowFilterException("存储日志时从"+msgId+"获取logId失败!");
		}
		//如果使用的是动态参数 就把msgId换成动态参数的值
		if(!msgId.equals(obj2)) {
			msgId = obj2.toString();
		}
		return msgId;
	}
	
	@Override
	public String fileterName() {
		return "writelog";
	}
}
