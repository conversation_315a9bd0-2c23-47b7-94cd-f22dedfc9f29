package com.ly.mp.busicen.rule.cust;

import static com.ly.mp.busicen.rule.XruleStrUtils.splitParam;

import java.util.Map;

import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.common.util.BusicenUtils;

public class XruleCustUtil {
	
	public static <T> void wrapperOrders(Page<T> pageInfo) {
		Map<String, Object> context = BusicenContext.getContext();
		if (context!=null&&!StringUtils.isEmpty(context.get(BusicenContext.BUSICEN_QUERY_ORDERS))) {
			String busicen_query_orders = String.valueOf(context.get(BusicenContext.BUSICEN_QUERY_ORDERS));
			if (!"#".equals(busicen_query_orders)) {
				Map<String, String> ordersList = splitParam(busicen_query_orders);
				ordersList.forEach((k,v)->{
					if ("asc".equals(v)) {
						pageInfo.addOrder(OrderItem.asc(BusicenUtils.HumpToUnderline(k)));
					}
					if ("desc".equals(v)) {
						pageInfo.addOrder(OrderItem.desc(BusicenUtils.HumpToUnderline(k)));
					}
				});
			}
		}
	}

}
