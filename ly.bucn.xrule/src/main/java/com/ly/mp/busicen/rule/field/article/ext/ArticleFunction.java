package com.ly.mp.busicen.rule.field.article.ext;

import java.io.Serializable;
import java.time.LocalDateTime;

public class ArticleFunction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程与节点ID
     */
    private Long senceFunctionId;

    /**
     * 场景节点编码
     */
    private String businessflowSencecode;

    /**
     * 方法名称
     */
    private String functionName;

    /**
     * 方法编码
     */
    private String functionCode;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    private String modifier;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制字段
     */
    private String updateControlId;

	public Long getSenceFunctionId() {
		return senceFunctionId;
	}

	public void setSenceFunctionId(Long senceFunctionId) {
		this.senceFunctionId = senceFunctionId;
	}

	public String getBusinessflowSencecode() {
		return businessflowSencecode;
	}

	public void setBusinessflowSencecode(String businessflowSencecode) {
		this.businessflowSencecode = businessflowSencecode;
	}

	public String getFunctionName() {
		return functionName;
	}

	public void setFunctionName(String functionName) {
		this.functionName = functionName;
	}

	public String getFunctionCode() {
		return functionCode;
	}

	public void setFunctionCode(String functionCode) {
		this.functionCode = functionCode;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}
    

}
