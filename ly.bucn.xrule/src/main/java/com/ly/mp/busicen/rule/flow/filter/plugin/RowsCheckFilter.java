package com.ly.mp.busicen.rule.flow.filter.plugin;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.action.OperationType;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterBase;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;
import com.ly.mp.component.entities.ListResult;

@Component
public class RowsCheckFilter extends FlowFilterBase {
	
	Logger log = LoggerFactory.getLogger(RowsCheckFilter.class);

	@SuppressWarnings("rawtypes")
	@Override
	public FlowResult doInvoke(FlowInvocation invocation) {
		FlowResult result = invocation.invoker().invoke(invocation);
		if (result.actionResult().signal().equals(Signal.CONTINUE)) {
			if (result.actionResult().data() == null) {
				((ActionResult) result.actionResult()).setSignal(Signal.BREAK);
				log.info("流程【{}】节点【{}】结果改为中断，因结果为null",invocation.flowVolume().flow(),invocation.action().action());
				return result;				
			}
			if (invocation.action().operation().equals(OperationType.INSERT)
					|| invocation.action().operation().equals(OperationType.UPDATE)
					|| invocation.action().operation().equals(OperationType.DELETE)) {
				
				if (result.actionResult().data() instanceof Integer) {
					Integer count = result.actionResult().data();
					if (count == 0) {
						((ActionResult) result.actionResult()).setSignal(Signal.BREAK);
						log.info("流程【{}】节点【{}】结果改为中断，因结果为0",invocation.flowVolume().flow(),invocation.action().action());
					}
				}
				if (result.actionResult().data() instanceof LinkedList) {
					LinkedList<IActionResult> resultList = result.actionResult().data();
					for (IActionResult iActionResult : resultList) {
						if (iActionResult.signal()==Signal.CONTINUE&&iActionResult.data()!=null&&iActionResult.data() instanceof Integer) {
							int count = (Integer)iActionResult.data();
							if (count == 0) {
								((ActionResult) result.actionResult()).setSignal(Signal.BREAK);
								log.info("流程【{}】节点【{}】结果改为中断，因结果结果集合中存在某一结果为0",invocation.flowVolume().flow(),invocation.action().action());								
								break;
							}
						}
					}
				}
				
			}
			if (invocation.action().operation().equals(OperationType.SELECTLIST)
					|| invocation.action().operation().equals(OperationType.SELECTONE)) {
				Object data = result.actionResult().data();
				if (data instanceof List && ((List) data).isEmpty()) {
					((ActionResult) result.actionResult()).setSignal(Signal.BREAK);
					log.info("流程【{}】节点【{}】结果改为中断，因结果集合为空",invocation.flowVolume().flow(),invocation.action().action());
				}
				if (data instanceof ListResult && ((ListResult) data).getRows().isEmpty()) {
					((ActionResult) result.actionResult()).setSignal(Signal.BREAK);
					log.info("流程【{}】节点【{}】结果改为中断，因分页结果为空",invocation.flowVolume().flow(),invocation.action().action());
				}
				if(data instanceof Map && ((Map)data).values().size()==1&& "0".equals(String.valueOf(((Map)data).values().iterator().next()))) {
					((ActionResult) result.actionResult()).setSignal(Signal.BREAK);
					log.info("流程【{}】节点【{}】结果改为中断，因结果为0",invocation.flowVolume().flow(),invocation.action().action());
				}
			}
		}
		return result;
	}

	@Override
	public String fileterName() {
		return "rowscheck";
	}
	

}
