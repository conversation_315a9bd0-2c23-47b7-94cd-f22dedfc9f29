package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;



public class FieldRegexValid extends ValidatorBaseAbstract{

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)&&!StringUtils.isEmpty(validation.strParam())) {			
			return RegexUtil.match(validation.strParam(), value.toString());
		}
		return true;
	}

	@Override
	public String validName() {
		return "regex";
	}

}
