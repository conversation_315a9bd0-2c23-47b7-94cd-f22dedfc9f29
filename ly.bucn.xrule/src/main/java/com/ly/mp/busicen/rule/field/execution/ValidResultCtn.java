package com.ly.mp.busicen.rule.field.execution;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

public class ValidResultCtn {
	
	private List<ValidResult> results = new ArrayList<ValidResult>();

	public List<ValidResult> getNotValidResults() {
		return results.stream().filter(m->!m.isValid()).collect(Collectors.toList());
	}
	
	public String getNotValidMessage() {
		List<String> errorList = getNotValidResults().stream().map(m->m.toString()).collect(Collectors.toList());
		return StringUtils.join(errorList, ",");
	}
	
	public boolean isValid() {
		return getNotValidResults().isEmpty();
	}
	
	public List<ValidResult> getResults() {
		return results;
	}

	public void setResults(List<ValidResult> results) {
		this.results = results;
	}
}
