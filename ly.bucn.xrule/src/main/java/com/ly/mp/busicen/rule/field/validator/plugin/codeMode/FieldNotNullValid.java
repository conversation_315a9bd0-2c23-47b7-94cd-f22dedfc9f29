package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;



public class FieldNotNullValid extends ValidatorBaseAbstract {

	
	@Override
	public String validName() {
		return "notnull";
	}

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (value==null) {
			return false;
		}
		if (value instanceof String) {
			return !StringUtils.isEmpty(value);
		}
		return true;
	}	
}
