package com.ly.mp.busicen.rule.field.validator.plugin;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidatorBase;

@Component
public class FieldShortDateValid extends ValidatorBase{

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)&&value instanceof String) {
			try {
				LocalDateTime.parse(value.toString()+" 00:00:00",
						DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
				return true;
			}catch(Exception e) {
				return false;
			}
			//可以用下方的正则 但我们只需要yyyy-MM-dd HH:mm:ss 所以用上面这种
			//return RegexUtil.match(RegexUtil.ShortDate, value.toString());
		}
		return true;
	}

	@Override
	public String validName() {
		return "shortdate";
	}

}
