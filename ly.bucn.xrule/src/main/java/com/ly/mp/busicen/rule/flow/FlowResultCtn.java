package com.ly.mp.busicen.rule.flow;

import java.util.LinkedList;
import java.util.List;
import java.util.stream.Stream;


public class FlowResultCtn implements IFlowResultCtn{
	
	public static FlowResultCtn create() {
		return new FlowResultCtn();
	}
	
	private String flowName;
	
	private LinkedList<IFlowResult> flowResults = new LinkedList<>();	
	
	private Risultato risultato;
	
	private Throwable excpt;
	
	private IFlowContext flowContext;	

	public void flowContext(IFlowContext flowContext) {
		this.flowContext = flowContext;
	}

	public void risultato(Risultato risultato) {
		this.risultato = risultato;
	}

	public void excpt(Throwable excpt) {
		this.excpt = excpt;
	}

	public void flowName(String flowName) {
		this.flowName = flowName;
	}
	
	public void addResult(IFlowResult actionResult) {
		flowResults.add(actionResult);
	}

	public void flowResults(LinkedList<IFlowResult> flowResults) {
		this.flowResults = flowResults;
	}

	@Override
	public String flowName() {
		return flowName;
	}

	@Override
	public List<IFlowResult> flowResults() {
		return flowResults;
	}

	@Override
	public Risultato risultato() {
		return risultato;
	}

	@Override
	public Throwable excpt() {
		return excpt;
	}

	@Override
	public IFlowContext flowContext() {
		return flowContext;
	}

	@Override
	public IFlowResult breakResult() {
		return flowResults.getLast();
	}

	@Override
	public Boolean isBreak() {		
		return risultato.equals(Risultato.BREAK);
	}

	@Override
	public Stream<IFlowResult> resultsStream() {		
		return flowResults.stream();
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> T exitResult() {		
		return (T) flowContext.dataVolume().ext().get("EXIT");
	}

}
