package com.ly.mp.busicen.rule.instrumentation.plugin;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.action.OperationType;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface XdebugConditon {
	
	public String[] action() default {};
	
	public String[] filter() default {};
	
	public OperationType[] operationType() default {};
	
	public Signal[] resultSignal() default{};
	
	public String[] spelCondition() default {};
	
	public boolean debugAnyway() default false;

}
