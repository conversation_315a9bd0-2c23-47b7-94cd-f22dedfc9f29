package com.ly.mp.busicen.rule.config;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Value;

/**
 * 规则引擎配置
 * <AUTHOR>
 *
 */
public class XruleConfig {
	
	@Value("${xrule.brandcheck.enable:true}")
	private Boolean brandCheckEnable;
	
	@Value("${xrule.custmode.enable:false}")
	private Boolean custModeEnable;
	
	/**
	 * 以配置为准
	 */
	@PostConstruct
	public void init() {
		xruleExtendCfg.brandCheck(brandCheckEnable);
		xruleExtendCfg.createDefaultCustMode(custModeEnable);
	}
	
	public static XruleConfig createEmpty() {		
		XruleConfig xc = new XruleConfig();
		xc.xruleDataCfg = XruleDataCfg.createEmpty();
		xc.xruleDataLoadCfg = XruleDataLoadCfg.createEmpty();
		xc.xruleInstrumentCfg = XruleInstrumentCfg.createEmpty();
		xc.xruleExtendCfg = XruleExtendCfg.createEmpty();
		return xc;
	}
	
	public static XruleConfig createDefault() {		
		return createEmpty()
				.xruleDataCfg(XruleDataCfg.createDefault())
				.xruleDataLoadCfg(XruleDataLoadCfg.createDefault())
				.xruleInstrumentCfg(XruleInstrumentCfg.createDefault())
				.xruleExtendCfg(XruleExtendCfg.createBucn());
	}
	
	public static XruleConfig createBucn() {		
		return createEmpty()
				.xruleDataCfg(XruleDataCfg.createBucn())
				.xruleDataLoadCfg(XruleDataLoadCfg.createBucn())
				.xruleInstrumentCfg(XruleInstrumentCfg.createBucn())
				.xruleExtendCfg(XruleExtendCfg.createDefault());
	}
	
	/**
	 * 配置加载对应表名称，可以用于修改实际配置表名称
	 */
	private XruleDataCfg xruleDataCfg;
	
	/**
	 * 配置加载过程拦截器，可用于修改配置加载方式
	 */
	private XruleDataLoadCfg xruleDataLoadCfg;
	
	/**
	 * 规则引擎探测器，附加功能新增及开关和排序等，当前支持【调试，日志，推送，触发器等等】
	 */
	private XruleInstrumentCfg xruleInstrumentCfg;
	
	/**
	 * 规则引擎额外配置，如是否开启客制化加载，是否品牌过滤等等
	 */
	private XruleExtendCfg xruleExtendCfg;
	
	public XruleExtendCfg xruleExtendCfg() {
		return xruleExtendCfg;
	}

	public XruleConfig xruleExtendCfg(XruleExtendCfg xruleExtendCfg) {
		this.xruleExtendCfg = xruleExtendCfg;
		return this;
	}

	public XruleDataCfg xruleDataCfg() {
		return xruleDataCfg;
	}
	public XruleConfig xruleDataCfg(XruleDataCfg xruleDataCfg) {
		this.xruleDataCfg = xruleDataCfg;
		return this;
	}
	public XruleDataLoadCfg xruleDataLoadCfg() {
		return xruleDataLoadCfg;
	}
	public XruleConfig xruleDataLoadCfg(XruleDataLoadCfg xruleDataLoadCfg) {
		this.xruleDataLoadCfg = xruleDataLoadCfg;
		return this;
	}

	public XruleInstrumentCfg xruleInstrumentCfg() {
		return xruleInstrumentCfg;
	}

	public XruleConfig xruleInstrumentCfg(XruleInstrumentCfg xruleInstrumentCfg) {
		this.xruleInstrumentCfg = xruleInstrumentCfg;
		return this;
	}

}
