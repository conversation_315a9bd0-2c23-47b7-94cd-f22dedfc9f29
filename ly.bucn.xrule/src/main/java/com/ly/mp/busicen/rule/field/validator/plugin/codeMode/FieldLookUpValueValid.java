package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;


/**
 * <lookupvalue>(APPVE9527_07,code)[]
 * <AUTHOR>
 *
 */
public class FieldLookUpValueValid extends ValidatorBaseAbstract{
	
	@Autowired
	NamedParameterJdbcTemplate JdbcTemplate;

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value = validData.value();
		if (!StringUtils.isEmpty(value)) {
			String param = validation.strParam();
			if (!StringUtils.isEmpty(param)) {
				String[] mm = param.split(",");				
				if (mm.length == 2) {
					String typeCode = mm[0];
					String type = mm[1];
					List<LookUpValue> list = getLookUpValues(typeCode);
					if (type.equals("code")) {
						return list.stream().filter(m->m.getCode().equals(value)).findAny().isPresent();
					}else {
						return list.stream().filter(m->m.getName().equals(value)).findAny().isPresent();
					}					
				}
			}
		}
		return true;
	}
	
	
	public List<LookUpValue> getLookUpValues(String typeCode) {
		
		return new ArrayList<FieldLookUpValueValid.LookUpValue>();		
	}

	@Override
	public String validName() {
		return "lookupvalue";
	}
	class LookUpValue{
		private String code;
		
		private String name;

		public String getCode() {
			return code;
		}

		public void setCode(String code) {
			this.code = code;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}
		
		
	}

}
