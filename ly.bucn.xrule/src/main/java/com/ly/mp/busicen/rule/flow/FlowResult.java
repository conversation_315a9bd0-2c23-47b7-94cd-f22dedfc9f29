package com.ly.mp.busicen.rule.flow;

import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;

public class FlowResult implements IFlowResult{
	
	private String action;
	private String actionName;
	private Object data;
	private String message;
	private Signal signal;
	
	public static FlowResult create() {
		FlowResult result = new FlowResult();
		return result;
	}


	@Override
	public String action() {
		return action;
	}

	@Override
	public String actionName() {
		return actionName;
	}

	@SuppressWarnings("unchecked")
	@Override
	public <T> T data() {
		return (T) data;
	}

	@Override
	public String message() {
		return message;
	}

	@Override
	public Signal signal() {
		return signal;
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getActionName() {
		return actionName;
	}

	public void setActionName(String actionName) {
		this.actionName = actionName;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Signal getSignal() {
		return signal;
	}

	public void setSignal(Signal signal) {
		this.signal = signal;
	}
	
	

}
