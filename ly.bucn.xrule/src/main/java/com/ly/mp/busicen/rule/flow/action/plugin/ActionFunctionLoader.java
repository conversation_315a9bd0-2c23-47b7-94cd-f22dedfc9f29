package com.ly.mp.busicen.rule.flow.action.plugin;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ActionFunctionLoader implements ApplicationContextAware{

	ApplicationContext application;
	
	@Autowired
	ActionFunctionContainer actionFunctionContainer;
	
	@PostConstruct
	public void load() {
		//加载beanactionfunctionbase
		Map<String, ActionExecuteFunctionBase> beans = application.getBeansOfType(ActionExecuteFunctionBase.class);
		Map<String, ActionExecuteFunctionBase> funtions = new HashMap<String, ActionExecuteFunctionBase>();
		beans.values().forEach(m -> {
			funtions.put(m.name(), m);
		});
		actionFunctionContainer.setFuntions(funtions);
		
		//加载function
		Map<String, IActionFuncRegister> funcRegisters = application.getBeansOfType(IActionFuncRegister.class);
		funcRegisters.values().forEach(m->m.actionFuncRegist(actionFunctionContainer));
		
	}
	
	
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		application = applicationContext;
	}

}
