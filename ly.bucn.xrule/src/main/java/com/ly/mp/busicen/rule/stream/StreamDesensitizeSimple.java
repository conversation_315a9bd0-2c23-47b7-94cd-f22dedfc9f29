package com.ly.mp.busicen.rule.stream;

import java.util.Map;

public class StreamDesensitizeSimple {
	
	public static String doDesensitize(String mode, Object value, Map<String, Object> context) {
		String valueStr = (String) value;
		switch (mode) {
		case "tel"://电话
			return StreamDesensitizationUtil.right(valueStr, 4);
		case "phone": //手机
			return StreamDesensitizationUtil.mobileEncrypt(valueStr);
		case "telphone"://电话或手机
			return StreamDesensitizationUtil.around(valueStr,3, 3);
		case "name": //姓名
			return StreamDesensitizationUtil.right(valueStr, 1);
		case "idcard": //身份证
			return StreamDesensitizationUtil.idEncrypt(valueStr);
		case "addr": //地址
			return StreamDesensitizationUtil.around(valueStr,3,5);
		case "email": //email
			return StreamDesensitizationUtil.email(valueStr);
		case "standard": //标准 前2后4
			return StreamDesensitizationUtil.around(valueStr,2,4);
		case "all": //全部脱敏
			return StreamDesensitizationUtil.all(valueStr);
		default:
			return valueStr;
		}
	}

}
