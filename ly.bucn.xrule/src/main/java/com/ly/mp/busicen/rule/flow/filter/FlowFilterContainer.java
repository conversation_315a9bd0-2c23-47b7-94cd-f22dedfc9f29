package com.ly.mp.busicen.rule.flow.filter;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FlowFilterContainer {
	
	Logger log = LoggerFactory.getLogger(FlowFilterContainer.class);
	
	private Map<String, FlowFilter> filters = new HashMap<String, FlowFilter>();

	public Map<String, FlowFilter> getFilters() {
		return filters;
	}

	public void setFilters(Map<String, FlowFilter> filters) {
		this.filters = filters;
	}
	
	public FlowFilter filter(String filterName) {
		FlowFilter filter = filters.get(filterName);
		if (filter==null) {
			String message = String.format("规则引擎不存在【%s】过滤器", filterName);
			log.error(message);
			throw new FlowFilterException(message);
		}
		return filter;
	}

}
