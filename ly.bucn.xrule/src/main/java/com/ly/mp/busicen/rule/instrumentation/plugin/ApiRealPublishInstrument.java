package com.ly.mp.busicen.rule.instrumentation.plugin;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//import org.apache.dubbo.rpc.RpcException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.flow.FlowSpelUtil;
import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.IFlowContext;
import com.ly.mp.busicen.rule.flow.IFlowResult;
import com.ly.mp.busicen.rule.flow.IFlowResultCtn;
import com.ly.mp.busicen.rule.flow.IFlowResultCtn.Risultato;
import com.ly.mp.busicen.rule.flow.IFlowVolume;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.filter.FlowFilter;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;
import com.ly.mp.busicen.rule.instrumentation.IFlowInstrumentation;

@Component
public class ApiRealPublishInstrument implements IFlowInstrumentation {
	
	Logger log = LoggerFactory.getLogger(ApiRealPublishInstrument.class);	
	

	@Override
	public void beginFlow(String flow, String brand, Map<String, Object> data, String rid) {
		
	}

	@Override
	public void endBuildContext(IFlowContext context) {
		
	}

	@Override
	public void beginActionInvoke(IAction action, IFlowContext context, IFlowResultCtn result) {
		
	}

	@Override
	public void endActionInvoke(IAction action, IFlowContext context, IFlowResultCtn result, IFlowResult flowResult) {
		
	}

	@Override
	public void beginFilter(FlowFilter flowFilter, FlowInvocation invocation) {
		
	}

	@Override
	public void endFilter(FlowFilter flowFilter, FlowInvocation invocation, FlowResult filterResult) {
		
	}

	@Override
	public void beginActionExecute(IAction action, IDataVolume dataVolume, IFlowVolume flowVolume) {
		
	}

	@Override
	public void endActionExecute(IAction action, IDataVolume dataVolume, IFlowVolume flowVolume,
			IActionResult actionResult) {
		
	}

	@SuppressWarnings("unchecked")
	@Override
	public void endFlow(IFlowResultCtn result) {
		String flow = result.flowContext().flowVolume().flow();
		try {
			if (result.risultato() == Risultato.FINISH) {							
					Object bupk = result.flowContext().dataVolume().end().get("bupk");
					List<Object> bupks = (List<Object>) result.flowContext().dataVolume().end().get("bupks");
					if (StringUtils.isEmpty(bupk)&&(bupks==null||bupks.isEmpty())) {
						log.info("规则引擎实时推送不执行，流程【{}】未配置业务主键",flow);
					}else {
						Map<String, Object> data = new HashMap<String, Object>();
						List<Object> bupksClone = new ArrayList<Object>(bupks);
						data.put("bupks", bupksClone);
						if (!StringUtils.isEmpty(bupk)) {
							bupksClone.add(bupk);
						}
						handle(flow,data);						
					}
			}}
			catch (Exception e) {
				log.error("规则引擎实时推送异常,流程【{}】",flow,e);
			}
	}
	
	
	public void handle(String flow,Map<String, Object> data) {
//		try {
//			IApiRealEventPublish publish = FlowSpelUtil.importService(IApiRealEventPublish.class);
//			publish.pushResult(flow, data);
//		} catch (Exception e) {
//			if (e instanceof RpcException) {
//				if (((RpcException)e).getCode()==4) {
//					log.warn("规则引擎实时推送消费服务不存在,不进行推送:{}",e.getMessage());
//					return ;
//				}
//			}
//			throw e;
//		}
	}

	@Override
	public String label() {
		return "pksync";
	}
	

}
