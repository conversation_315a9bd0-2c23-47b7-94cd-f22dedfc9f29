package com.ly.mp.busicen.rule.field.validator.plugin.codeMode;


import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.validator.IValidData;
import com.ly.mp.busicen.rule.field.validator.IValidation;
import com.ly.mp.busicen.rule.field.validator.ValidMsg;



public class FieldIntegerValid extends ValidatorBaseAbstract {

	@Override
	public boolean valid(IValidData validData, IValidation validation) {
		Object value= validData.value();
		if (!StringUtils.isEmpty(value)) {
			String intStr = value.toString();
			if (!isNumeric(intStr)) {
				return false;
			}
			
			String param = validation.strParam();
			if (!StringUtils.isEmpty(param)) {
				String[] mm = param.split(",");
				Double v = Double.parseDouble(value.toString());
				if(mm.length==1) {
					if(param.startsWith(",")) {						
						return v >= min("") && v <= max(mm[0]);
					}else {
						return v >= min(mm[0]) && v <= max("");
					}
				}
				if (mm.length == 2) {
					return v >= min(mm[0]) && v <= max(mm[1]);
				}
			}
			
		}
		return true;
	}

	private Double min(String min) {
		if (StringUtils.isEmpty(min)) {
			return Double.MIN_VALUE;
		}
		return Double.parseDouble(min);
	}

	private Double max(String max) {
		if (StringUtils.isEmpty(max)) {
			return Double.MAX_VALUE;
		}
		return Double.parseDouble(max);
	}
	
	public static boolean isNumeric(String str){  
		   for(int i=str.length();--i>=0;){  
		      int chr=str.charAt(i);  
		      if(chr<48 || chr>57)  {
		         return false;  
		      }
		   }  
		   return true;  
		}   

	@Override
	public String validName() {
		return "integer";
	}
	
	@Override
	public ValidMsg errorMsg(IValidation validation) {
		ValidMsg validMsg = super.errorMsg(validation);		
		String[] params = validation.strParam().split(",");
		if(params.length==2&&StringUtils.isEmpty(params[0])) {
			validMsg.setMsg("COM-V-"+validName()+"lt");
			String[] paramt = new String[1];
			paramt[0] = params[1];
			validMsg.setParams(paramt);
			return validMsg;
		}
		validMsg.setParams(params);
		if(params.length==1) {
			if(validation.strParam().startsWith(",")) {						
				validMsg.setMsg("COM-V-"+validName()+"lt");
			}else {
				validMsg.setMsg("COM-V-"+validName()+"gt");
			}
		}
		if (!StringUtils.isEmpty(validation.strMsg())) {
			validMsg.setMsg(validation.strMsg());
		}
		return validMsg;
	}

}
