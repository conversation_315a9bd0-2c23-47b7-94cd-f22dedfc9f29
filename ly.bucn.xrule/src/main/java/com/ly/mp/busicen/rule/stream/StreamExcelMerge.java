package com.ly.mp.busicen.rule.stream;

import java.io.Serializable;

public class StreamExcelMerge implements Serializable{
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private int beginColumn;
	
	private int endColumn;
	
	private int beginRow;
	
	private int endRow;


	public int getBeginColumn() {
		return beginColumn;
	}

	public void setBeginColumn(int beginColumn) {
		this.beginColumn = beginColumn;
	}

	public int getEndColumn() {
		return endColumn;
	}

	public void setEndColumn(int endColumn) {
		this.endColumn = endColumn;
	}

	public int getBeginRow() {
		return beginRow;
	}

	public void setBeginRow(int beginRow) {
		this.beginRow = beginRow;
	}

	public int getEndRow() {
		return endRow;
	}

	public void setEndRow(int endRow) {
		this.endRow = endRow;
	}
	
	
	
}
