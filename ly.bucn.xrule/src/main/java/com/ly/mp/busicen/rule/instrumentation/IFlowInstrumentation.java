package com.ly.mp.busicen.rule.instrumentation;

import java.util.Map;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.IFlowContext;
import com.ly.mp.busicen.rule.flow.IFlowResult;
import com.ly.mp.busicen.rule.flow.IFlowResultCtn;
import com.ly.mp.busicen.rule.flow.IFlowVolume;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.filter.FlowFilter;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;

/**
 * <FONT style="FONT-SIZE:24pt; FILTER: shadow(color=#af2dco); WIDTH: 100%; COLOR: blue; LINE-HEIGHT: 100%; FONT-FAMILY:华文行楷  ">规则引擎探测仪</FONT>
 * 
 * <AUTHOR> style="FONT-SIZE:16pt; FONT-FAMILY:隶书  "> ly-liuweisong</FONT>
 *
 */
public interface IFlowInstrumentation extends IInstrumentLabel {
	
	/**
	 *  
	 * 开始执行引擎
	 * @param flow 流程编码
	 * @param brand 品牌
	 * @param data 数据
	 * @param rid 规则执行流水号
	 */
	void beginFlow(String flow,String brand,Map<String, Object> data,String rid);
	
	/**
	 * 总线构造完成
	 * @param context 总线
	 */
	void endBuildContext(IFlowContext context);
	
	/**
	 * 开始调用节点
	 * @param action 节点配置
	 * @param context 总线
	 * @param result 流程结果
	 */
	void beginActionInvoke(IAction action,IFlowContext context,IFlowResultCtn result);
	
	/**
	 * 结束调用节点
	 * @param action
	 * @param context
	 * @param result
	 * @param flowResult
	 */
	void endActionInvoke(IAction action,IFlowContext context,IFlowResultCtn result,IFlowResult flowResult);
	
	/**
	 * 过滤器开始
	 * @param flowFilter
	 * @param invocation
	 */
	void beginFilter(FlowFilter flowFilter,FlowInvocation invocation);
	
	/**
	 * 过滤器开始
	 * @param flowFilter
	 * @param invocation
	 */
	void endFilter(FlowFilter flowFilter,FlowInvocation invocation,FlowResult filterResult);
	
	/**
	 * 开始执行规则
	 * @param action
	 * @param dataVolume
	 * @param flowVolume
	 */
	void beginActionExecute(IAction action,IDataVolume dataVolume,IFlowVolume flowVolume);
	
	/**
	 * 结束执行规则
	 * @param action
	 * @param dataVolume
	 * @param flowVolume
	 * @param actionResult
	 */
	void endActionExecute(IAction action,IDataVolume dataVolume,IFlowVolume flowVolume,IActionResult actionResult);
	
	/**
	 * 结束执行流程
	 * @param result
	 */
	void endFlow(IFlowResultCtn result);	

}
