package com.ly.mp.busicen.rule.config;

import java.util.LinkedList;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 探测器配置
 * <AUTHOR>
 *
 */
public class XruleInstrumentCfg {
	
	private LinkedList<String> instrumentLabels;
	
	public static XruleInstrumentCfg createEmpty() {
		XruleInstrumentCfg cfg = new XruleInstrumentCfg();
		cfg.instrumentLabels=new LinkedList<>();
		return cfg;
	}
	public static XruleInstrumentCfg createDefault() {
		return XruleInstrumentCfg.createEmpty()
				.add("debug")
				.add("metrics");
	}
	public static XruleInstrumentCfg createBucn() {
		return XruleInstrumentCfg.createEmpty()
				.labels("debug", "pkasync", /* "pksync", */"metrics"/* ,"trigger" */);
	}
	
	public LinkedList<String> labels(){
		return instrumentLabels;
	}
	
	public XruleInstrumentCfg labels(String...labels) {
		instrumentLabels = new LinkedList<String>(Stream.of(labels).collect(Collectors.toList()));
		return this;
	}
	
	public XruleInstrumentCfg add(String label) {
		instrumentLabels.add(label);
		return this;
	}
	public XruleInstrumentCfg remove(String label) {
		instrumentLabels.removeIf(m->m.equals(label));
		return this;
	}

}
