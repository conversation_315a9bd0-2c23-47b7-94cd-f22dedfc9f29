package com.ly.mp.busicen.rule.instrumentation;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@ComponentScan("com.ly.mp.busicen.rule.instrumentation.plugin")
public class FlowInstrumentationConfig {
	
	@Bean
	public FlowInstrumentationContainer flowInstrumentationContainer() {
		return new FlowInstrumentationContainer();
	}
	
	@Primary
	@Bean
	public FlowInstrumentationManager flowInstrumentationManager() {
		return new FlowInstrumentationManager();
	}
	
}
