package com.ly.mp.busicen.rule.flow.action.plugin;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.rule.field.IFireFieldRule;
import com.ly.mp.busicen.rule.field.execution.ValidResultCtn;
import com.ly.mp.busicen.rule.flow.FlowException;
import com.ly.mp.busicen.rule.flow.FlowSpelUtil;
import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.action.ActionExecuteBase;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import static com.ly.mp.busicen.rule.XruleStrUtils.*;

public class ActionExecuteFieldCheck extends ActionExecuteBase{
	
	@Autowired
	IFireFieldRule fireFieldRule;

	@Override
	public IActionResult execute(IAction action, IDataVolume dataVolume) {
		ActionResult result = ActionResult.create();
		result.action(action.action());
		result.setConvertMsg(false);
		try {
			Object data = dataVolume;
			if (!StringUtils.isEmpty(action.extKey())) {
				data = FlowSpelUtil.spelGetData(action, action.extKey());
			}
			String[] param = splitXKH(action.content()).split(",");
			if (param.length<2) {
				String errormsg = String.format("[%s]字段校验不正确，应包含流程编码及数据类型", action.action());
				throw new FlowException(errormsg);
			}
			
			ValidResultTemp resultTemp = fieldCheck(data, param[0], param[1],param.length==3?param[2]:"",String.valueOf(action.extention().get(IAction.EXTKEY_BRAND)));
			result.data(resultTemp.result);
			if (resultTemp.valid) {
				result.signal(Signal.CONTINUE);
				result.msg(action.msg());
				result.setConvertMsg(true);
				result.nextAction(defaultNextActionCode(action, ""));
			}else {
				result.signal(Signal.BREAK);
				result.msg(resultTemp.msg);
			}
			
		} catch (Exception e) {
			result.signal(Signal.EXCPT);
			result.excpt(e);
		}
		return result;
	}
	
	private ValidResultTemp fieldCheck(Object data,String flow,String dataType,String checkList,String brand) {
		ValidResultTemp temp = new ValidResultTemp();
		if (data!=null&&data instanceof Collection<?>) {
			if ("notnone".equals(checkList)&&((Collection<?>)data).isEmpty()) {
				throw new NullPointerException("传入集合不能为空");
			}
			List<ValidResultCtn> result = fieldCheckList((Collection<?>)data, flow, dataType,brand);
			temp.result = result;
			StringBuilder sb = new StringBuilder();
			for (int i=0; i<result.size() ; i++) {
				ValidResultCtn resultCtn = result.get(i);
				if (!resultCtn.isValid()) {
					temp.valid=false;
					sb.append(String.format("{第%s行:%s}", i+1,resultCtn.getNotValidMessage()));
				}
			}
			temp.msg = sb.toString();
		}else {
			ValidResultCtn result = fieldCheckOne(data, flow, dataType,brand);
			temp.result=result;
			if (!result.isValid()) {
				temp.valid=false;
				temp.msg=result.getNotValidMessage();
			}
		}
		return temp;
	}
	
	private ValidResultCtn fieldCheckOne(Object data,String flow,String dataType,String brand) {
		return fireFieldRule.fireRule(data, flow, dataType,brand);		
	}
	
	private List<ValidResultCtn> fieldCheckList(Collection<?> data,String flow,String dataType,String brand){
		return data.stream().map(m->fieldCheckOne(m, flow, dataType,brand)).collect(Collectors.toList());
	}
	
	
	
	class ValidResultTemp{
		private boolean valid=true;
		private String msg;
		private Object result;
		public boolean isValid() {
			return valid;
		}
		public void setValid(boolean valid) {
			this.valid = valid;
		}
		public String getMsg() {
			return msg;
		}
		public void setMsg(String msg) {
			this.msg = msg;
		}
		public Object getResult() {
			return result;
		}
		public void setResult(Object result) {
			this.result = result;
		}
		
	}
	
	

}
