package com.ly.mp.busicen.rule.flow.action;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteContainer;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteDataCheck;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteDelete;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteFieldCheck;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteFlow;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteFunction;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteInsert;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteScript;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteSelectList;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteSelectOne;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteSpBean;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteUpdate;

@Configuration
public class ActionExecuteLoader {
	
	
	
	@Autowired
	ActionExecuteContainer actionExecuteContainer;
	
	@Autowired
	ActionExecuteDataCheck actionExecuteDataCheck;
	
	@Autowired
	ActionExecuteInsert actionExecuteInsert;
	
	@Autowired
	ActionExecuteDelete actionExecuteDelete;
	
	@Autowired
	ActionExecuteUpdate actionExecuteUpdate;
	
	@Autowired
	ActionExecuteSelectList actionExecuteSelectList;
	
	@Autowired
	ActionExecuteSelectOne actionExecuteSelectOne;
	
	@Autowired
	ActionExecuteFunction actionExecuteFunction;
	
	@Autowired
	ActionExecuteFieldCheck actionExecuteFieldCheck;
	
	@Autowired
	ActionExecuteScript actionExecuteScript;
	
	@Autowired
	ActionExecuteSpBean actionExecuteSpBeant;
	
	@Autowired
	ActionExecuteFlow actionExecuteFlow;
	
	
	@PostConstruct
	public void init() {
		Map<OperationType, IActionExecute> actionExecutes = new HashMap<OperationType, IActionExecute>();
		actionExecutes.put(OperationType.DATACHECK, actionExecuteDataCheck);
		actionExecutes.put(OperationType.INSERT, actionExecuteInsert);
		actionExecutes.put(OperationType.DELETE, actionExecuteDelete);
		actionExecutes.put(OperationType.UPDATE, actionExecuteUpdate);
		actionExecutes.put(OperationType.SELECTLIST, actionExecuteSelectList);
		actionExecutes.put(OperationType.SELECTONE, actionExecuteSelectOne);
		actionExecutes.put(OperationType.FUNCTION, actionExecuteFunction);	
		actionExecutes.put(OperationType.FIELDCHECK, actionExecuteFieldCheck);
		actionExecutes.put(OperationType.SCRIPT, actionExecuteScript);
		actionExecutes.put(OperationType.SPBEAN, actionExecuteSpBeant);
		actionExecutes.put(OperationType.FLOW, actionExecuteFlow);
		actionExecuteContainer.setActionExecutes(actionExecutes);
	}
	
	

}
