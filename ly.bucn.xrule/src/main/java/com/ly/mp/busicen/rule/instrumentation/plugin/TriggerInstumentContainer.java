package com.ly.mp.busicen.rule.instrumentation.plugin;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class TriggerInstumentContainer {
	
	List<XtriggerConf> triggers = new ArrayList<>();
	
	public TriggerInstumentContainer regist(String flow,boolean sync,ITriggerInstrument trigger) {
		if (StringUtils.isEmpty(flow)||trigger==null) {
			return this;
		}
		XtriggerConf xtc = new XtriggerConf();
		xtc.setFlow(flow);
		xtc.setSync(sync);
		xtc.setTrigger(trigger);
		triggers.add(xtc);		
		return this;
	}
	
	List<XtriggerConf> triggers(String flow){
		if (StringUtils.isEmpty(flow)) {
			return new ArrayList<XtriggerConf>();
		}
		List<XtriggerConf> list = triggers.stream().filter(m->flow.equals(m.getFlow())).collect(Collectors.toList());
		return list==null?new ArrayList<XtriggerConf>():list;
	}
	
	public static class XtriggerConf{
		private String flow;
		private boolean sync;
		private ITriggerInstrument trigger;
		public String getFlow() {
			return flow;
		}
		public void setFlow(String flow) {
			this.flow = flow;
		}
		public boolean isSync() {
			return sync;
		}
		public void setSync(boolean sync) {
			this.sync = sync;
		}
		public ITriggerInstrument getTrigger() {
			return trigger;
		}
		public void setTrigger(ITriggerInstrument trigger) {
			this.trigger = trigger;
		}
		
	}

	
}
