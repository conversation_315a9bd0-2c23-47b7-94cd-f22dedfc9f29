package com.ly.mp.busicen.rule.flow.filter.plugin;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterBase;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;
import com.ly.mp.busicen.rule.flow.filter.FlowResultImpl;
import com.ly.mp.busicen.rule.instrumentation.IFlowInstrumentation;

/**
 * 执行action的过滤器
 * <AUTHOR>
 *
 */
@Component
public class ExecutionFilter extends FlowFilterBase {
	
	@Autowired
	IFlowInstrumentation flowInstrumentation;

	@Override
	public FlowResult doInvoke(FlowInvocation invocation) {
		FlowResultImpl flowResultImpl = new FlowResultImpl();
		
		IActionResult actionResult = null;
		try {
			flowInstrumentation.beginActionExecute(invocation.action(), invocation.dataVolume(),invocation.flowVolume());
			actionResult = invocation.execution().execute(invocation.action(), invocation.dataVolume());
		} finally {
			flowInstrumentation.endActionExecute(invocation.action(), invocation.dataVolume(), invocation.flowVolume(), actionResult);
		}		
		
		flowResultImpl.setActionResult(actionResult);
		return flowResultImpl;
	}

	@Override
	public String fileterName() {
		return "execution";
	}

}
