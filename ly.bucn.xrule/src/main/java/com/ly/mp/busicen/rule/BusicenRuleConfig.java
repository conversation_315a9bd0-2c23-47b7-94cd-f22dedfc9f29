package com.ly.mp.busicen.rule;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.ly.mp.busicen.rule.field.FieldRuleConfig;
import com.ly.mp.busicen.rule.flow.FlowConfig;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterConfig;

@Configuration
public class BusicenRuleConfig {
	
	@Bean
	public FieldRuleConfig fieldRuleConfig() {
		return new FieldRuleConfig();
	}
	
	@Bean
	public FlowConfig flowConfig() {
		return new FlowConfig();
	}
	
	@Bean
	public FlowFilterConfig flowFilterConfig() {
		return new FlowFilterConfig();
	}	
}
