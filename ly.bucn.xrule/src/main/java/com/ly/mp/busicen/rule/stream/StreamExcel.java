package com.ly.mp.busicen.rule.stream;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;

public class StreamExcel implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String actionName;
	
	private String title;
	
	private LinkedHashMap<String, String> columns;
	
	private Map<String,StreamExcelConvert> converts;	

	private Map<String,StreamExcelMerge> mergeCols;
	
	private StreamExcelExtend extend;	
	
	public StreamExcelExtend getExtend() {
		return extend;
	}

	public void setExtend(StreamExcelExtend extend) {
		this.extend = extend;
	}
	
	public Map<String, StreamExcelMerge> getMergeCols() {
		return mergeCols;
	}

	public void setMergeCols(Map<String, StreamExcelMerge> mergeCols) {
		this.mergeCols = mergeCols;
	}

	public Map<String, StreamExcelConvert> getConverts() {
		return converts;
	}

	public void setConverts(Map<String, StreamExcelConvert> converts) {
		this.converts = converts;
	}

	public String getActionName() {
		return actionName;
	}

	public void setActionName(String actionName) {
		this.actionName = actionName;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public LinkedHashMap<String, String> getColumns() {
		return columns;
	}

	public void setColumns(LinkedHashMap<String, String> columns) {
		this.columns = columns;
	}
	
	
}
