package com.ly.mp.busicen.rule.instrumentation.plugin;

import java.util.Map;

import com.ly.mp.busicen.rule.flow.IDataVolume;
import com.ly.mp.busicen.rule.flow.IFlowContext;
import com.ly.mp.busicen.rule.flow.IFlowResult;
import com.ly.mp.busicen.rule.flow.IFlowResultCtn;
import com.ly.mp.busicen.rule.flow.IFlowVolume;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult;
import com.ly.mp.busicen.rule.flow.filter.FlowFilter;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;
import com.ly.mp.busicen.rule.instrumentation.IFlowInstrumentation;

@FunctionalInterface
public interface ITriggerInstrument extends IFlowInstrumentation {

	
	default void beginFlow(String flow,String brand,Map<String, Object> data,String rid) {};
	
	
	default void endBuildContext(IFlowContext context) {};
	
	
	default void beginActionInvoke(IAction action,IFlowContext context,IFlowResultCtn result){};
	

	default void endActionInvoke(IAction action,IFlowContext context,IFlowResultCtn result,IFlowResult flowResult){};
	

	default void beginFilter(FlowFilter flowFilter,FlowInvocation invocation){};
	

	default void endFilter(FlowFilter flowFilter, FlowInvocation invocation, FlowResult filterResult) {}
	

	default void beginActionExecute(IAction action,IDataVolume dataVolume,IFlowVolume flowVolume){};
	

	default void endActionExecute(IAction action,IDataVolume dataVolume,IFlowVolume flowVolume,IActionResult actionResult){};
		

	default String label() { return null; }
		
}
