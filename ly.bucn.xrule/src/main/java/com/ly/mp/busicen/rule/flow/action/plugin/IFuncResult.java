package com.ly.mp.busicen.rule.flow.action.plugin;

public interface IFuncResult {
	
	boolean success();
	
	String nextActionCode();
	
	String message();
	
	Object result();
	
	public static FuncResult defaults() {
		return new FuncResult();
	}
	
	static class FuncResult implements IFuncResult{
		
		private boolean isSuccess = true;
		
		private String nextActionCode;
		
		private String message;
		
		private Object result;

		@Override
		public boolean success() {
			return isSuccess;
		}

		@Override
		public String nextActionCode() {
			return nextActionCode;
		}

		@Override
		public String message() {
			return message;
		}
		
		@Override
		public Object result() {
			return result;
		}
		
		public FuncResult success(boolean success) {
			this.isSuccess = success;
			return this;
		}

		public FuncResult nextActionCode(String nextActionCode) {
			this.nextActionCode = nextActionCode;
			return this;
		}

		public FuncResult message(String message) {
			this.message = message;
			return this;
		}
		
		public FuncResult result(Object result) {
			this.result = result;
			return this;
		}

		public boolean isSuccess() {
			return isSuccess;
		}

		public void setSuccess(boolean isSuccess) {
			this.isSuccess = isSuccess;
		}

		public String getNextActionCode() {
			return nextActionCode;
		}

		public void setNextActionCode(String nextActionCode) {
			this.nextActionCode = nextActionCode;
		}

		public String getMessage() {
			return message;
		}

		public void setMessage(String message) {
			this.message = message;
		}

		public Object getResult() {
			return result;
		}

		public void setResult(Object result) {
			this.result = result;
		}
		
		
		
	}

}
