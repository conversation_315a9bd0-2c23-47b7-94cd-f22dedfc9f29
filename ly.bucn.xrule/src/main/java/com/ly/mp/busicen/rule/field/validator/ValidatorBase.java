package com.ly.mp.busicen.rule.field.validator;

import org.springframework.util.StringUtils;

public abstract class ValidatorBase implements IValidator<IValidData,IValidation>,IValidatorEx {
	
	@Override
	public ValidMsg errorMsg(IValidation validation) {
		ValidMsg validMsg = new ValidMsg();
		if (StringUtils.isEmpty(validation.strMsg())) {
			validMsg.setMsg("COM-V-"+validName());
		}else {
			validMsg.setMsg(validation.strMsg());
		}
		return validMsg;
	}

}
