package com.ly.mp.busicen.rule.stream;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

import org.springframework.util.StringUtils;

//@Component
public class StreamExcelConvertManager {

	private static Map<String, IStreamExcelFieldConvert> converts = new HashMap<>();

	static {
		converts.put("number", (o, c) -> {
			if (StringUtils.isEmpty(c.getFormat())) {
				return String.valueOf(o);
			}
			DecimalFormat df = new DecimalFormat(c.getFormat());
			if (o instanceof BigDecimal || o instanceof Double || o instanceof Integer || o instanceof Long) {
				return df.format(o);
			}
			return String.valueOf(o);
		});
		converts.put("datetime", (o, c) -> {
			if (StringUtils.isEmpty(c.getFormat())) {
				return String.valueOf(o);
			}
			if (o instanceof LocalDateTime) {
				return ((LocalDateTime) o).format(DateTimeFormatter.ofPattern(c.getFormat()));
			} else if (o instanceof Timestamp) {
				DateFormat sdf = new SimpleDateFormat(c.getFormat());
				return sdf.format(o);
			}

			return String.valueOf(o);
		});
		
		converts.put("kvs", (o, c) -> {
			String key = String.valueOf(o);
			if (c.getKvs()==null) {
				return key;				
			}
			String value = c.getKvs().get(key);
			if (value!=null) {
				return value;
			}
			return key;
		});
	}

	public static String convertValue(Object o, StreamExcelConvert conf) {
		if (o == null) {
			return "";
		}
		if (conf == null || StringUtils.isEmpty(conf.getConvert())) {
			return String.valueOf(o);
		}
		IStreamExcelFieldConvert converter = converts.get(conf.getConvert());
		if (converter == null) {
			return String.valueOf(o);
		} else {
			return converter.convert(o, conf);
		}
	}

}
