package com.ly.mp.busicen.rule.flow.action;

import java.util.HashMap;
import java.util.Map;

public class Action implements IAction{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private String action;
	
	private String nextAction;
	
	private String actionName;
	
	private String content;
	
	private String extKey;
	
	private OperationType operation;
	
	private String msg;
	
	private String ruleCode;
	
	private String filter;
	
	private String loopEnable;
	
	private Map<String, Object> extention = new HashMap<String, Object>();

	@Override
	public String action() {
		return action;
	}

	@Override
	public String nextAction() {
		return nextAction;
	}

	@Override
	public String actionName() {
		return actionName;
	}

	@Override
	public String content() {
		return content;
	}

	@Override
	public String extKey() {
		return extKey;
	}

	@Override
	public OperationType operation() {
		return operation;
	}

	@Override
	public String msg() {
		return msg;
	}
	
	@Override
	public String ruleCode() {
		return ruleCode;
	}
	
	@Override
	public boolean loopEnble() {
		return "1".equals(loopEnable);
	}

	public String getAction() {
		return action;
	}

	public void setAction(String action) {
		this.action = action;
	}

	public String getNextAction() {
		return nextAction;
	}

	public void setNextAction(String nextAction) {
		this.nextAction = nextAction;
	}

	public String getActionName() {
		return actionName;
	}

	public void setActionName(String actionName) {
		this.actionName = actionName;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}
	
	public String getExtKey() {
		return extKey;
	}

	public void setExtKey(String extKey) {
		this.extKey = extKey;
	}

	public OperationType getOperation() {
		return operation;
	}

	public void setOperation(OperationType operation) {
		this.operation = operation;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public String getLoopEnable() {
		return loopEnable;
	}

	public void setLoopEnable(String loopEnable) {
		this.loopEnable = loopEnable;
	}

	@Override
	public String filter() {
		return filter;
	}

	public String getFilter() {
		return filter;
	}

	public void setFilter(String filter) {
		this.filter = filter;
	}

	@Override
	public Map<String, Object> extention() {
		return extention;
	}

	public Map<String, Object> getExtention() {
		return extention;
	}

	public void setExtention(Map<String, Object> extention) {
		this.extention = extention;
	}

	public String getRuleCode() {
		return ruleCode;
	}

	public void setRuleCode(String ruleCode) {
		this.ruleCode = ruleCode;
	}

}
