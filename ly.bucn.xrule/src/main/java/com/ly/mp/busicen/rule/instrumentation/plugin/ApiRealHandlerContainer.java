package com.ly.mp.busicen.rule.instrumentation.plugin;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.util.StringUtils;

public class ApiRealHandlerContainer {
	
	List<ApiRealHandlerConf> handlers = new ArrayList<ApiRealHandlerContainer.ApiRealHandlerConf>();
	
	public ApiRealHandlerContainer regist(String flow,boolean sync,IApiRealHandler handler) {
		if (StringUtils.isEmpty(flow)||handler==null) {
			return this;
		}
		ApiRealHandlerConf xtc = new ApiRealHandlerConf();
		xtc.setFlow(flow);
		xtc.setSync(sync);
		xtc.setHandler(handler);
		handlers.add(xtc);		
		return this;
	}
	
	List<ApiRealHandlerConf> handlers(String flow){
		if (StringUtils.isEmpty(flow)) {
			return new ArrayList<ApiRealHandlerConf>();
		}
		List<ApiRealHandlerConf> list = handlers.stream().filter(m->flow.equals(m.getFlow())).collect(Collectors.toList());
		return list==null?new ArrayList<ApiRealHandlerConf>():list;
	}
	
	class ApiRealHandlerConf{
		private String flow;
		private boolean sync;
		private IApiRealHandler handler;
		public String getFlow() {
			return flow;
		}
		public void setFlow(String flow) {
			this.flow = flow;
		}
		public boolean isSync() {
			return sync;
		}
		public void setSync(boolean sync) {
			this.sync = sync;
		}
		public IApiRealHandler getHandler() {
			return handler;
		}
		public void setHandler(IApiRealHandler handler) {
			this.handler = handler;
		}
		
	}

}
