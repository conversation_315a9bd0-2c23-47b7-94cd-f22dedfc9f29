package com.ly.mp.busicen.rule.flow.filter.plugin;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.fastjson.parser.Feature;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.rule.flow.action.ActionResult;
import com.ly.mp.busicen.rule.flow.action.IAction;
import com.ly.mp.busicen.rule.flow.action.IActionResult.Signal;
import com.ly.mp.busicen.rule.flow.action.OperationType;
import com.ly.mp.busicen.rule.flow.engine.db.BusicenResultHandler;
import com.ly.mp.busicen.rule.flow.filter.FlowFilterBase;
import com.ly.mp.busicen.rule.flow.filter.FlowInvocation;
import com.ly.mp.busicen.rule.flow.filter.FlowResult;
import com.ly.mp.busicen.rule.flow.filter.FlowResultImpl;
import com.ly.mp.busicen.rule.stream.StreamExcel;
import com.ly.mp.busicen.rule.stream.StreamExcelData;
import com.ly.mp.busicen.rule.stream.StreamExcelDesensitize;
import com.ly.mp.busicen.rule.stream.StreamExcelExport;

/**
 * 执行action的过滤器
 * <AUTHOR>
 *
 */
@Component
public class StreamExcelFilter extends FlowFilterBase {
	
	Logger log = LoggerFactory.getLogger(StreamExcelFilter.class);
	
	
	
	
	@Autowired(required = false)
	HttpServletResponse response;

	@Override
	public String fileterName() {
		return "streamexcel";
	}

	@Override
	public FlowResult doInvoke(FlowInvocation invocation) {
		
		if(invocation.action().operation()==OperationType.SELECTLIST) {
			if(response!=null) {
				if(BusicenContext.getContext().get(BusicenContext.BUSICEN_EXCEL)!=null) {
					log.info("流程【{}】节点【{}】使用流式导出", invocation.flowVolume().flow(),	invocation.action().action());
					
					String excelJson = BusicenContext.getContext().get(BusicenContext.BUSICEN_EXCEL).toString();
					 
					JSONObject jjj = JSONObject.parseObject(excelJson,Feature.OrderedField);
					
					boolean isdes = jjj.getBoolean("isdes");
					
					JSONArray desensitize = jjj.getJSONArray("desensitize");
					List<StreamExcelDesensitize> sedes = desensitize.toJavaList(StreamExcelDesensitize.class);
					
					JSONObject excel = jjj.getJSONObject("excel");					
					List<StreamExcel> ses = excel.getObject("excels",new TypeReference<List<StreamExcel>>(){});
					StreamExcel ex = ses.get(0);
					
					StreamExcelData sed = new StreamExcelData();
					sed.setTitle(StringUtils.isEmpty(ex.getTitle())?ex.getActionName():ex.getTitle());
					
					String[][] columns = new String[ex.getColumns().size()][2];
					AtomicInteger index=new AtomicInteger(0);
					ex.getColumns().forEach((k,v)->{
						columns[index.get()][0]= k;
						columns[index.get()][1]= v;
						index.addAndGet(1);										
					});
					
					sed.setColumns(columns);
					sed.setConverts(ex.getConverts());
					sed.setMergeCols(ex.getMergeCols());
					sed.setExtend(ex.getExtend());
					sed.setIsdes(isdes);
					sed.setDesensitize(sedes);
					
					StreamExcelExport see = new StreamExcelExport();
					FlowResult result;
					try {
						Consumer<Set<Map<String,Object>>> consumer = see.export(sed, response);
						BusicenResultHandler<Map<String,Object>> handler = BusicenResultHandler.<Map<String,Object>>create(consumer);
						invocation.action().extention().put(IAction.EXTKEY_EXCEL, handler);
						result = invocation.invoker().invoke(invocation);
						handler.submit();
						return result;
					}catch (Exception e) {
						ActionResult actionResult = ActionResult.create();
						actionResult.signal(Signal.EXCPT);
						actionResult.excpt(e);
						actionResult.action(invocation.action().action());
						actionResult.msg(invocation.action().msg());
						actionResult.data(null);
						FlowResultImpl flowResultImpl = new FlowResultImpl();
						flowResultImpl.setActionResult(actionResult);
						flowResultImpl.setException(e);
						return flowResultImpl;
					}
					finally {
						try {
							see.flush();
						} catch (Exception e) {
							ActionResult actionResult = ActionResult.create();
							actionResult.signal(Signal.EXCPT);
							actionResult.excpt(e);
							actionResult.action(invocation.action().action());
							actionResult.msg(invocation.action().msg());
							actionResult.data(null);
							FlowResultImpl flowResultImpl = new FlowResultImpl();
							flowResultImpl.setActionResult(actionResult);
							flowResultImpl.setException(e);
//							return flowResultImpl;
						}
					}
				}
			}else {
				
			}				
						
		}else {
			
		}
		return invocation.invoker().invoke(invocation);
		
	}
	

}
