package com.ly.mp.busicen.rule.stream;

import java.util.List;
import java.util.Map;

public class StreamExcelExtend {
	
	private Integer dropListRowCount=500;
	
	private Map<String,List<String>> dropLists;	
	
	public Integer getDropListRowCount() {
		return dropListRowCount;
	}

	public void setDropListRowCount(Integer dropListRowCount) {
		this.dropListRowCount = dropListRowCount;
	}

	public Map<String, List<String>> getDropLists() {
		return dropLists;
	}

	public void setDropLists(Map<String, List<String>> dropLists) {
		this.dropLists = dropLists;
	}

}
