package com.ly.mp.busicen.rule.extention.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface XruleFlow {
	
	String flow();
	
	ReturnType returnType() default ReturnType.USERDEFINE;
	
	FireMode fireMode() default FireMode.EXCPT;
	
	public static enum FireMode{
		NORMAL,
		EXCPT
	}
	
	public static enum ReturnType{
		/**
		 * 以调用结果类型为准
		 */
		USERDEFINE,
		/**
		 * 分页
		 */
		LISTRESULT,
		/**
		 * 操作
		 */
		OPTRESULT,
		/**
		 * 
		 */
		ENTITYRESULT
	}
	
}
