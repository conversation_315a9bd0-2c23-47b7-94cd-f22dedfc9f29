package com.ly.mp.busicen.rule.flow;

public class FlowStringUtils {

	/**
	 * 获取小括号内容
	 * @param msg
	 * @return
	 */
//	public static String extractParam(String msg) {
//		boolean notStart = true;
//		int start = 0;
//		int end = 0;
//		for (int i = 0; i < msg.length(); i++) {
//			if (notStart && msg.charAt(i) == '(') {
//				start = i;
//				notStart = false;
//			}
//			if (msg.charAt(i) == ')') {
//				end = i;
//			}
//		}
//		if (notStart) {
//			return null;
//		}
//		return msg.substring(start + 1, end);
//	}

}
