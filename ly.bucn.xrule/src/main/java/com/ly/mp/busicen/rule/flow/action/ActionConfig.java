package com.ly.mp.busicen.rule.flow.action;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.ly.mp.busicen.rule.flow.action.plugin.ActionExecuteContainer;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionFunctionContainer;
import com.ly.mp.busicen.rule.flow.action.plugin.ActionFunctionLoader;

@Configuration
@Import({ActionExecuteLoader.class,ActionExecuteContainer.class,ActionFunctionLoader.class})
@AutoConfigureAfter({ActionExecuteLoader.class})
@ComponentScan("com.ly.mp.busicen.rule.flow.action.plugin.function")
public class ActionConfig {
		
	@Bean
	public ActionFunctionContainer actionFunctionContainer() {
		return new ActionFunctionContainer();
	}

}
