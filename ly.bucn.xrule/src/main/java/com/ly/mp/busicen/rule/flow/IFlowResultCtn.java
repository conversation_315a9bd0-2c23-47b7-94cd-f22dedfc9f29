package com.ly.mp.busicen.rule.flow;

import java.util.List;
import java.util.stream.Stream;


public interface IFlowResultCtn {
	
	Boolean isBreak();
	
	Stream<IFlowResult> resultsStream();
	
	Risultato risultato();
	
	IFlowContext flowContext();
	
	Throwable excpt();
	
	String flowName();
	
	List<IFlowResult> flowResults();
	
	IFlowResult breakResult();
	
	<T> T exitResult();
	
	public enum Risultato{		
		FINISH,
		BREAK,
		EXCPT		
	}

}
