package com.ly.mp.busicen.rule.field.article.ext;

import java.io.Serializable;
import java.time.LocalDateTime;

public class ArticleColumn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 流程与节点ID
     */
    private Long senceValidatecolumId;

    /**
     * 场景节点编码
     */
    private String businessflowSencecode;

    /**
     * 表编码
     */
    private String tableCode;

    /**
     * 字段名称
     */
    private String columName;

    /**
     * 字段编码
     */
    private String columCode;

    /**
     * 校验内容
     */
    private String validateContent;

    /**
     * 品牌编码
     */
    private String carBrandCode;

    /**
     * 厂商编码
     */
    private String oemGroupCode;

    /**
     * 启停状态
     */
    private String isEnable;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建人姓名
     */
    private String createdName;

    /**
     * 创建时间
     */
    private LocalDateTime createdDate;

    /**
     * 最后更新人员
     */
    private String modifier;

    /**
     * 最后更新时间
     */
    private LocalDateTime lastUpdatedDate;

    /**
     * 并发控制字段
     */
    private String updateControlId;

	public Long getSenceValidatecolumId() {
		return senceValidatecolumId;
	}

	public void setSenceValidatecolumId(Long senceValidatecolumId) {
		this.senceValidatecolumId = senceValidatecolumId;
	}

	public String getBusinessflowSencecode() {
		return businessflowSencecode;
	}

	public void setBusinessflowSencecode(String businessflowSencecode) {
		this.businessflowSencecode = businessflowSencecode;
	}

	public String getTableCode() {
		return tableCode;
	}

	public void setTableCode(String tableCode) {
		this.tableCode = tableCode;
	}

	public String getColumName() {
		return columName;
	}

	public void setColumName(String columName) {
		this.columName = columName;
	}

	public String getColumCode() {
		return columCode;
	}

	public void setColumCode(String columCode) {
		this.columCode = columCode;
	}

	public String getValidateContent() {
		return validateContent;
	}

	public void setValidateContent(String validateContent) {
		this.validateContent = validateContent;
	}

	public String getCarBrandCode() {
		return carBrandCode;
	}

	public void setCarBrandCode(String carBrandCode) {
		this.carBrandCode = carBrandCode;
	}

	public String getOemGroupCode() {
		return oemGroupCode;
	}

	public void setOemGroupCode(String oemGroupCode) {
		this.oemGroupCode = oemGroupCode;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatedName() {
		return createdName;
	}

	public void setCreatedName(String createdName) {
		this.createdName = createdName;
	}

	public LocalDateTime getCreatedDate() {
		return createdDate;
	}

	public void setCreatedDate(LocalDateTime createdDate) {
		this.createdDate = createdDate;
	}

	public String getModifier() {
		return modifier;
	}

	public void setModifier(String modifier) {
		this.modifier = modifier;
	}

	public LocalDateTime getLastUpdatedDate() {
		return lastUpdatedDate;
	}

	public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
		this.lastUpdatedDate = lastUpdatedDate;
	}

	public String getUpdateControlId() {
		return updateControlId;
	}

	public void setUpdateControlId(String updateControlId) {
		this.updateControlId = updateControlId;
	}

}
