package com.ly.mp.busicen.rule.field;

import java.util.ArrayList;
import java.util.List;

/**
 * 	校验工具类 链式创建规则
 */
public class FieldRuleBuilder {

	private List<FieldRuleEntity> ruleList;

	private FieldRuleBuilder(List<FieldRuleEntity> ruleList) {
		this.ruleList = ruleList;
	}

	public static FieldRuleBuilder builder(String columnCode, String columnName, String validateContent) {
		return builder().add(columnCode, columnName, validateContent);
	}

	public static FieldRuleBuilder builder() {
		return new FieldRuleBuilder(new ArrayList<FieldRuleEntity>());
	}

	public FieldRuleBuilder add(String columnCode, String columnName, String validateContent) {
		ruleList.add(new FieldRuleEntity(columnCode, columnName, validateContent));
		return this;
	}

	public List<FieldRuleEntity> build() {
		return ruleList;
	}

}