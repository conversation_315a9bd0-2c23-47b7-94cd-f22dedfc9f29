package com.ly.mp.busicen.rule.config;

import org.springframework.beans.factory.annotation.Value;

/**
 * 配置加载-表名称
 * <AUTHOR>
 *
 */
public class XruleDataCfg {
	
	/**
	 * 空配置
	 * @return
	 */
	public static XruleDataCfg createEmpty() {
		return new XruleDataCfg();
	}
	
	/**
	 * 默认配置
	 * @return
	 */
	public static XruleDataCfg createDefault() {
		 return createEmpty()
			 .dataRule("t_prc_db_datarule")
			 .flowRule("t_prc_db_sence_datarule")
			 .fieldRule("t_prc_db_sence_validatecolum")
			 .messageRule("t_prc_db_log_model")
			 .pkPublish("t_prc_msg_table_register");		
	}
	
	/**
	 * 业务中台配置
	 * @return
	 */
	public static XruleDataCfg createBucn() {
		return createDefault();
	}
	
	/**
	 * 规则
	 */
	private String dataRule;
	/**
	 * 流程
	 */
	private String flowRule;
	/**
	 * 字段
	 */
	private String fieldRule;
	/**
	 * 消息
	 */
	private String messageRule;
	/**
	 * 主键
	 */
	private String pkPublish;
	
	/**
	 * 配置解密秘钥
	 */
	@Value("${xrule.conf.encrykey:busicen}")
	private String encryKey;
		
	public String encryKey() {
		return encryKey;
	}	

	public String dataRule() {
		return dataRule;
	}
	public String flowRule() {
		return flowRule;
	}
	public String fieldRule() {
		return fieldRule;
	}
	public String messageRule() {
		return messageRule;
	}
	public String pkPublish() {
		return pkPublish;
	}
	public XruleDataCfg dataRule(String dataRule) {
		this.dataRule = dataRule;
		return this;
	}
	public XruleDataCfg flowRule(String flowRule) {
		this.flowRule = flowRule;
		return this;
	}
	public XruleDataCfg fieldRule(String fieldRule) {
		this.fieldRule = fieldRule;
		return this;
	}
	public XruleDataCfg messageRule(String messageRule) {
		this.messageRule = messageRule;
		return this;
	}
	public XruleDataCfg pkPublish(String pkPublish) {
		this.pkPublish = pkPublish;
		return this;
	}
	
	public XruleDataCfg encryKey(String encryKey) {
		this.encryKey = encryKey;
		return this;
	}
	
	
	
}
