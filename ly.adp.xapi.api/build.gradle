apply from: "$rootDir/gradle/docker.gradle"
//apply plugin: "org.cyclonedx.bom"
//apply plugin: 'org.owasp.dependencycheck'

//buildscript {
//    dependencies {
//        classpath "com.cyclonedx:cyclonedx-gradle-plugin:1.5.0"
//        classpath 'org.owasp:dependency-check-gradle:7.0.3'
//    }
//}

ext{
	xapiVersion='0.2.5-SNAPSHOT'
}

dependencies {
	compile project(':ly.adp.common')
    compile fileTree(dir: "$rootDir/extjar/", include: ['*.jar'])
    compile fileTree(dir: "$rootDir/extjar/bucn/xapi/common/", include: ['*.jar'])
    compile fileTree(dir: "$rootDir/extjar/bucn/xapi/log/", include: ['*.jar'])
    compile fileTree(dir: "$rootDir/extjar/bucn/xapi/mc/", include: ['*.jar'])
    compile fileTree(dir: "$rootDir/extjar/bucn/xapi/plugin/", include: ['*.jar'])
    compile fileTree(dir: "$rootDir/extjar/bucn/mybatis/", include: ['*.jar'])
    compile fileTree(dir: "$rootDir/extjar/bucn/valid/", include: ['*.jar'])
//	compile ("com.szlanyou.bucn:ly.bucn.component.xapi:${xapiVersion}"){transitive=false}
//	compile ("com.szlanyou.bucn:ly.bucn.component.xapi.log.db:${xapiVersion}"){transitive=false}
//	compile ("com.szlanyou.bucn:ly.bucn.component.xapi.mc:${xapiVersion}"){transitive=false}
//	compile ("com.szlanyou.bucn:ly.bucn.component.xapi.plugin:${xapiVersion}"){transitive=false}
//	compile ("com.szlanyou.bucn:ly.bucn.component.mybatis:${xapiVersion}"){transitive=false}
//	compile ("com.szlanyou.bucn:ly.bucn.component.valid:${xapiVersion}"){transitive=false}
    compile libs.swagger2Core
    compile ("org.springframework.boot:spring-boot-starter-data-ldap:$springBootVersion")
}
if (!project.hasProperty('upload')){
    apply from: "$rootDir/gradle/springboot.gradle"
}
