<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.tda.mapper.TdaReceive002Mapper">
    <select id="findOnecustInfo" resultType="java.util.Map">
        select CUST_ID,
               EXTEND_JSON
        from csc.t_sac_onecust_info
        where CUST_ID = #{custId}
    </select>


    <update id="updateOnecustInfo">
        update
        csc.t_sac_onecust_info
        set
        MODIFIER='TDA_R_002',
        MODIFY_NAME='TDA_R_002',
        LAST_UPDATED_DATE=NOW(),

        <if test="param.custName != null and param.custName !='' ">
            CUST_NAME = #{param.custName},
        </if>
        <if test="param.phoneStandby != null and param.phoneStandby !='' ">
            PHONE_STANDBY = #{param.phoneStandby},
        </if>
        <if test="param.genderCode != null and param.genderCode !='' ">
            GENDER_CODE = #{param.genderCode},
        </if>
        <if test="param.genderName != null and param.genderName !='' ">
            GENDER_NAME = #{param.genderName},
        </if>
        <if test="param.nickName != null and param.nickName !='' ">
            NICK_NAME = #{param.nickName},
        </if>
        <if test="param.email != null and param.email !='' ">
            EMAIL = #{param.email},
        </if>
        <if test="param.wechat != null and param.wechat !='' ">
            WECHAT = #{param.wechat},
        </if>
        <if test="param.idCard != null and param.idCard !='' ">
            ID_CARD = #{param.idCard},
        </if>
        <if test="param.driverCard != null and param.driverCard !='' ">
            DRIVER_CARD = #{param.driverCard},
        </if>
        <if test="param.drivingLicense != null and param.drivingLicense !='' ">
            DRIVING_LICENSE = #{param.drivingLicense},
        </if>
        <if test="param.passport != null and param.passport !='' ">
            PASSPORT = #{param.passport},
        </if>
        <if test="param.source5 != null and param.source5 !='' ">
            source5 = #{param.source5},
        </if>
        <if test="param.source6 != null and param.source6 !='' ">
            source6 = #{param.source6},
        </if>
        <if test="param.attr83 != null and param.attr83 !='' ">
            attr83 = #{param.attr83},
        </if>
        <if test="param.complaintsLabel != null and param.complaintsLabel !='' ">
            COMPLAINTS_LABEL = #{param.complaintsLabel},
        </if>
        <if test="param.freeLabel !=null and param.freeLabel !=''">
            FREE_LABEL=#{param.freeLabel},
        </if>
        <if test="param.userHobbiesCode !=null and param.userHobbiesCode !=''">
            USER_HOBBIES_CODE=#{param.userHobbiesCode},
        </if>
        <if test="param.userHobbiesName !=null and param.userHobbiesName !=''">
            USER_HOBBIES_NAME=#{param.userHobbiesName},
        </if>
        <if test="param.characteristicsCode !=null and param.characteristicsCode !=''">
            CHARACTERISTICS_CODE=#{param.characteristicsCode},
        </if>
        <if test="param.characteristicsName !=null and param.characteristicsName !=''">
            CHARACTERISTICS_NAME=#{param.characteristicsName},
        </if>
        <if test="param.oneCustSource != null and param.oneCustSource !='' ">
            ONE_CUST_SOURCE = #{param.oneCustSource},
        </if>
        <if test="param.extendsJson != null and param.extendsJson !='' ">
            EXTEND_JSON = #{param.extendsJson},
        </if>
        <if test="param.column1 != null and param.column1 !='' ">
            COLUMN1 = #{param.column1},
        </if>
        <if test="param.column2 != null and param.column2 !='' ">
            COLUMN2 = #{param.column2},
        </if>
        <if test="param.column3 != null and param.column3 !='' ">
            COLUMN3 = #{param.column3},
        </if>
        <if test="param.column4 != null and param.column4 !='' ">
            COLUMN4 = #{param.column4},
        </if>
        <if test="param.column5 != null and param.column5 !='' ">
            COLUMN5 = #{param.column5},
        </if>
        <if test="param.column6 != null and param.column6 !='' ">
            COLUMN6 = #{param.column6},
        </if>
        <if test="param.column7 != null and param.column7 !='' ">
            COLUMN7 = #{param.column7},
        </if>
        <if test="param.column8 != null and param.column8 !='' ">
            COLUMN8 = #{param.column8},
        </if>
        <if test="param.column9 != null and param.column9 !='' ">
            COLUMN9 = #{param.column9},
        </if>
        <if test="param.column10 != null and param.column10 !='' ">
            COLUMN10 = #{param.column10},
        </if>
        UPDATE_CONTROL_ID=uuid()
        where
       CUST_ID = #{param.custId}
    </update>
</mapper>