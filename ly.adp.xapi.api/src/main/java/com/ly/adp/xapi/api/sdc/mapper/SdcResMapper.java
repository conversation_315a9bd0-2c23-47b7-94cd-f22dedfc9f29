package com.ly.adp.xapi.api.sdc.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface SdcResMapper extends BaseMapper {

    int queryDissociateApply(Map<String, Object> map);

    int insertOrderDissociate(Map<String, Object> paramMap);

    int insertOrderDissociateFiles(Map<String, Object> fileMap);

    List<Map<String, Object>> queryLookUpValue(@Param("param") Map<String, Object> lookupMap);

    int orderDissociateApply(@Param("param") Map<String, Object> mapParam);

    int dissociatefileSave(@Param("param") Map<String, Object> fileMap);
}
