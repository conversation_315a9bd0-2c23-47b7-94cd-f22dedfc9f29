package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 设备信息
 * <AUTHOR>
 * @Date 2024/7/3
 * @Version 1.0.0
 **/
public class DevInfo implements Serializable {

    private static final long serialVersionUID = -2961311133951459711L;

    /**
     * 联网记录
     */
    @JsonProperty("wifi_history")
    private List<WifiHistory> wifiHistory;

    /**
     * 文件上传历史
     */
    @JsonProperty("upload_history")
    private List<UploadHistory> uploadHistory;

    /**
     * 本地文件信息
     */
    @JsonProperty("local_files")
    private List<LocalFile> localFiles;

    /**
     * 操作历史
     */
    @JsonProperty("oper_history")
    private OperHistory operHistory;

    /**
     * OTA升级信息
     */
    @JsonProperty("ota_info")
    private List<OtaInfo> otaInfo;

    /**
     * 蓝牙操作历史
     */
    @JsonProperty("ble_history")
    private List<BleHistory> bleHistory;

    public List<WifiHistory> getWifiHistory() {
        return wifiHistory;
    }

    public void setWifiHistory(List<WifiHistory> wifiHistory) {
        this.wifiHistory = wifiHistory;
    }

    public List<UploadHistory> getUploadHistory() {
        return uploadHistory;
    }

    public void setUploadHistory(List<UploadHistory> uploadHistory) {
        this.uploadHistory = uploadHistory;
    }

    public List<LocalFile> getLocalFiles() {
        return localFiles;
    }

    public void setLocalFiles(List<LocalFile> localFiles) {
        this.localFiles = localFiles;
    }

    public OperHistory getOperHistory() {
        return operHistory;
    }

    public void setOperHistory(OperHistory operHistory) {
        this.operHistory = operHistory;
    }

    public List<OtaInfo> getOtaInfo() {
        return otaInfo;
    }

    public void setOtaInfo(List<OtaInfo> otaInfo) {
        this.otaInfo = otaInfo;
    }

    public List<BleHistory> getBleHistory() {
        return bleHistory;
    }

    public void setBleHistory(List<BleHistory> bleHistory) {
        this.bleHistory = bleHistory;
    }
}
