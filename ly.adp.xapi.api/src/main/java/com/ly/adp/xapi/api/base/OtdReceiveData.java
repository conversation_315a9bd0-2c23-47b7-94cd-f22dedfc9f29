package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.ms.AdpMsRunner;
import com.ly.bucn.component.xapi.entity.XapiField;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.error.ErrorSender;
import com.ly.bucn.component.xapi.kernal.model.*;
import com.ly.bucn.component.xapi.kernal.model.XapiResult.XapiResultData;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import com.ly.bucn.component.xapi.service.receive.ReceiveService;
import com.ly.bucn.component.xapi.util.XapiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;

@ReceiveService("otd_adp")
public class OtdReceiveData extends ReceiveDataBase<Object> {
    private static final Logger log = LoggerFactory.getLogger(OtdReceiveData.class);
    @Resource
    IXapiLogBiz xapiLogBiz;
    @Autowired
    PlatformTransactionManager transactionManager;
    @Autowired
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public boolean handle(XapiInstance instance, Object data, Consumer<String> consumer) {

        XapiMap<String, Object> context = new XapiMap<>();
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(instance.getLabel());
        loginfo.setObjectType(instance.getObjectType());
        loginfo.setLogTime(new Date());
        loginfo.setLogStatus("0");
        context.put("loginfo", loginfo);

        XapiReceiveResponse receiveResponse = XapiReceiveResponse.creat();

        XapiResult<Object> insertResult = XapiResult.creat();


        try {
            init(instance, context);
            if (!checkConfig(instance, context)) {
                log.error(context.get("checkMessage").toString());
                log.error("停止接收任务:{}", instance.getLabel());
                throw new Exception(context.<String>getValue("checkMessage"));
            }
            XapiData xdata = convertData(instance, context, data);
            XapiData ximprodata = improveColumn(instance, context, xdata);
            insertResult = this.insertData(instance, context, ximprodata);
//            return true;

        } catch (Exception e) {
            receiveResponse.setCode("-1");
            receiveResponse.setMsg(e.getMessage());
            loginfo.setLogMessage(e.getMessage());
            loginfo.setLogStatus("-1");

            log.error("接收调用异常", e);
        } finally {
            Map<String, Object> resultData = new HashMap<>();
            List<Map<String, Object>> mess = new ArrayList<>();

            insertResult.getErrorData().forEach(m -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", m.getId());
                map.put("isSuccess", m.isSuccess() ? "Y" : "N");
                map.put("message", m.isSuccess() ? "" : m.getMessage());
                mess.add(map);
            });
            resultData.put("data", mess);
            String jsonresp = JSONObject.toJSONString(resultData);
            consumer.accept(jsonresp);
            try {
                loginfo.setOutJson(jsonresp);
                loginfo.setInJson(data.toString());
                xapiLogBiz.add(loginfo);
            } catch (Exception e2) {
            }
        }

        return true;
    }

    public XapiData convertData(XapiInstance instance, XapiMap<String, Object> context, Object data) {
        String json = (String) data;
        List<XapiTable> childTables = (List) context.getValue("childTables");
        XapiData xData = XapiData.creat();
        //通过mq获取的报文
        if (AdpMsRunner.isMqPublish()) {
            Map<String, Object> resp = JSONObject.parseObject(json, Map.class);
            XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
            dr.setMainData(resp);
            childTables.forEach((n) -> {
                String firstFlag = n.getSubFlag();
                if ("otd_r_if70".equals(instance.getLabel())) {
                    List<String> urlList = (List) resp.get(firstFlag);
                    List<Map<String, Object>> childdata = new ArrayList<>();
                    urlList.forEach((v) -> {
                        Map<String, Object> map = new HashMap<>();
                        map.put("roNo", resp.get("roNo"));
                        map.put("url", v);
                        childdata.add(map);
                    });
                    dr.addChild(n, childdata);
                } else {
                    List<Map<String, Object>> childdata = (List) resp.get(firstFlag);
                    dr.addChild(n, childdata);
                }
                resp.remove(firstFlag);
            });
            xData.add(dr);

        } else {
            XapiReceiveRequest requestObj = (XapiReceiveRequest) JSONObject.parseObject(json, XapiReceiveRequest.class);
            if (requestObj.getHead().containsKey("serialnum")) {
                String serialnum = String.valueOf(requestObj.getHead().get("serialnum"));
                XapiLog loginfo = (XapiLog) context.getValue("loginfo");
                loginfo.setSerialNum(serialnum);
            }

            List<Map<String, Object>> body = requestObj.getBody();
            body.forEach((m) -> {
                XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                dr.setMainData(m);
                childTables.forEach((n) -> {
                    String firstFlag = n.getSubFlag();
                    List<Map<String, Object>> childdata = (List) m.get(firstFlag);
                    dr.addChild(n, childdata);
                    m.remove(firstFlag);
                });
                xData.add(dr);
            });
        }

        return xData;
    }

  /*  @Override
    public XapiResult<Object>    insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {

        XapiResult<Object> xapiResult = new XapiResult<>();

        data.getData().forEach(d->{
            XapiResultData<Object> resultData = Insert(instance, context, d);
            xapiResult.addErrorData(resultData);
        });

        return xapiResult;
    }*/


    public XapiResult<Object> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<Object> xapiResult = XapiResult.creat();
        XapiUtil.dataCount(data.getData().size());
        data.getData().forEach((d) -> {
            XapiUtil.dataCountDown();
            XapiResult.XapiResultData<Object> resultData = Insert(instance, context, d);
            // if (!resultData.isSuccess()) {
            xapiResult.addErrorData(resultData);
            // }

        });
        return xapiResult;
    }
}
