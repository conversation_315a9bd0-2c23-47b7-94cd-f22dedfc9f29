package com.ly.adp.xapi.api.tda.receive.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ly.adp.xapi.api.tda.entity.TestDrivelonLat;
import com.ly.adp.xapi.api.tda.receive.TdaReceive001;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/7
 */
@Service
public class TdaReceive001Impl implements TdaReceive001 {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tdaReceiveModel(XapiInstance instance, XapiMap<String, Object> context, Map<String, Object> mainData, XapiResult.XapiResultData<Object> resultData) {
        TestDrivelonLat testDrivelonLat = JSONObject.parseObject(JSONObject.toJSONString(mainData), TestDrivelonLat.class);

        resultData.setSuccess(true);
        HashMap<String, Object> responseMsg = Maps.newHashMap();
        responseMsg.put("code", "1");
        // responseMsg.put("requestCode", map.get("requestCode"));
        responseMsg.put("message", "CHENGG");

        resultData.setMessage(JSONObject.toJSONString(responseMsg));

    }
}
