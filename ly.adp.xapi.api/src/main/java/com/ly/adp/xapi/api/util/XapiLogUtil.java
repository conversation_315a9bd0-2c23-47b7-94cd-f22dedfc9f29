package com.ly.adp.xapi.api.util;

import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;

/**
 * xapi 日志工具类
 * <AUTHOR>
 * @Date 2024/5/31
 * @Version 1.0.0
 **/
public class XapiLogUtil {

    /**
     * xapi 成功日志记录
     * @param xapiLogBiz
     * @param apiLabel api名
     * @param inJson 入参
     * @param outJson 出参
     */
    public static void addSuccessXapiLog(IXapiLogBiz xapiLogBiz, String apiLabel, Object inJson, Object outJson) {
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(apiLabel);
        loginfo.setLogStatus("0");
        loginfo.setInJson(JSONObject.toJSONString(inJson));
        loginfo.setOutJson(JSONObject.toJSONString(outJson));
        xapiLogBiz.add(loginfo);
    }

    /**
     * xapi 成功日志记录
     * @param xapiLogBiz
     * @param apiLabel api名
     * @param inJson 入参
     * @param outJson 出参
     */
    public static void addSuccessXapiLog(IXapiLogBiz xapiLogBiz, String apiLabel, String inJson, String outJson) {
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(apiLabel);
        loginfo.setLogStatus("0");
        loginfo.setInJson(inJson);
        loginfo.setOutJson(outJson);
        xapiLogBiz.add(loginfo);
    }
}
