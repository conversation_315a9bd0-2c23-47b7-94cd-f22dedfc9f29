package com.ly.adp.xapi.api.base.entities;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 邮件发送请求实体
 * <AUTHOR>
 * @Date 2025/1/9
 * @Version 1.0.0
 **/
public class MailSendDTO {
    /**
     * 附件列表
     * 非必填
     */
    private List<MailAttachmentDTO> attachment;

    /**
     * 邮件内容
     * 必填
     */
    private MailContentDTO mailContent;

    /**
     * 邮件标题
     * 必填
     */
    private String mailTitle;

    /**
     * 唯一标识
     * 非必填
     */
    private String outBizNo;

    /**
     * 收件人邮箱
     * 必填
     */
    private List<String> receiveMails;

    /**
     * 来源系统
     * 必填
     */
    private String sourceSystem;

    /**
     * 模板编码
     * 必填
     */
    private String templateCode;

    public List<MailAttachmentDTO> getAttachment() {
        return attachment;
    }

    public void setAttachment(List<MailAttachmentDTO> attachment) {
        this.attachment = attachment;
    }

    public MailContentDTO getMailContent() {
        return mailContent;
    }

    public void setMailContent(MailContentDTO mailContent) {
        this.mailContent = mailContent;
    }

    public String getMailTitle() {
        return mailTitle;
    }

    public void setMailTitle(String mailTitle) {
        this.mailTitle = mailTitle;
    }

    public String getOutBizNo() {
        return outBizNo;
    }

    public void setOutBizNo(String outBizNo) {
        this.outBizNo = outBizNo;
    }

    public List<String> getReceiveMails() {
        return receiveMails;
    }

    public void setReceiveMails(List<String> receiveMails) {
        this.receiveMails = receiveMails;
    }

    public String getSourceSystem() {
        return sourceSystem;
    }

    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    /**
     * 便捷构造方法，用于创建TEXT类型的邮件发送请求
     *
     * @param mailTitle 邮件标题
     * @param content 邮件内容
     * @param emails 收件人邮箱(逗号分隔的字符串)
     * @param sourceSystem 来源系统
     * @param templateCode 模板编码
     * @param outBizNo 唯一标识（可选）
     * @return MailSendDTO实例
     */
    public static MailSendDTO createTextMail(
            String mailTitle,
            String content,
            String emails,
            String sourceSystem,
            String templateCode,
            String outBizNo
    ) {
        MailSendDTO dto = new MailSendDTO();

        // 设置邮件内容
        MailContentDTO mailContent = new MailContentDTO();
        mailContent.setContent(content);
        mailContent.setContentType(MailContentTypeEnum.TEXT.getCode());

        // 设置基本信息
        dto.setMailContent(mailContent);
        dto.setMailTitle(mailTitle);
        dto.setReceiveMails(parseEmails(emails));
        dto.setSourceSystem(sourceSystem);
        dto.setTemplateCode(templateCode);
        dto.setOutBizNo(outBizNo);
        dto.setAttachment(new ArrayList<>());  // 初始化空的附件列表

        return dto;
    }


    /**
     * 重载方法，不需要outBizNo的版本
     */
    public static MailSendDTO createTextMail(
            String mailTitle,
            String content,
            String emails,
            String sourceSystem,
            String templateCode
    ) {
        return createTextMail(mailTitle, content, emails, sourceSystem, templateCode, null);
    }

    /**
     * 解析邮箱地址
     * 支持以下格式：
     * 1. List<String>
     * 2. 逗号分隔的字符串
     * 3. 单个邮箱地址
     *
     * @param emails 邮箱地址（支持多种格式）
     * @return 邮箱地址列表
     */
    private static List<String> parseEmails(Object emails) {
        if (emails == null) {
            return new ArrayList<>();
        }

        if (emails instanceof List) {
            return new ArrayList<>((List<String>) emails);
        }

        if (emails instanceof String) {
            String emailStr = (String) emails;
            if (emailStr.contains(",")) {
                return Arrays.stream(emailStr.split(","))
                        .map(String::trim)
                        .filter(s -> !s.isEmpty())
                        .collect(Collectors.toList());
            }
            return Collections.singletonList(emailStr.trim());
        }

        throw new IllegalArgumentException("不支持的邮箱格式");
    }

    @Override
    public String toString() {
        return "MailSendDTO{" +
                "attachment=" + attachment +
                ", mailContent=" + mailContent +
                ", mailTitle='" + mailTitle + '\'' +
                ", outBizNo='" + outBizNo + '\'' +
                ", receiveMails=" + receiveMails +
                ", sourceSystem='" + sourceSystem + '\'' +
                ", templateCode='" + templateCode + '\'' +
                '}';
    }
}
