package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@ApiModel("获取培训人员信息请求参数")
public class UserNumRequestEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /*公司id*/
    private String  corpCode;
    /*公司编号*/
    private List<Map<String,String>> organizePositionList;
    /*APPKEY*/
    private String  appKey_;
    /*数字签名*/
    private String sign_;
    /*系统时间*/
    private Long timestamp_;

    public String getCorpCode() {
        return corpCode;
    }

    public void setCorpCode(String corpCode) {
        this.corpCode = corpCode;
    }

    public List<Map<String, String>> getOrganizePositionList() {
        return organizePositionList;
    }

    public void setOrganizePositionList(List<Map<String, String>> organizePositionList) {
        this.organizePositionList = organizePositionList;
    }

    public String getAppKey_() {
        return appKey_;
    }

    public void setAppKey_(String appKey_) {
        this.appKey_ = appKey_;
    }

    public String getSign_() {
        return sign_;
    }

    public void setSign_(String sign_) {
        this.sign_ = sign_;
    }

    public Long getTimestamp_() {
        return timestamp_;
    }

    public void setTimestamp_(Long timestamp_) {
        this.timestamp_ = timestamp_;
    }
}
