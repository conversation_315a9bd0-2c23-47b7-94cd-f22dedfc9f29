package com.ly.adp.xapi.api.baseEntity;

/**
 * <AUTHOR>
 * @description: OTD 现车类型
 * @date 2024/11/19
 */
public enum CarVehicleTypeEnum {

    CAR_VEHICLE_TYPE_STOCK("0", "现车"),

    CAR_VEHICLE_TYPE_HOSTLING("1", "整备车辆"),

    CAR_VEHICLE_TYPE_SHOW("2", "展车"),

    CAR_VEHICLE_TYPE_SPECIAL("3", "特殊"),

    CAR_VEHICLE_TYPE_WHOLESALE("4", "批售现车");

    private String code;

    private String desc;
    ;

    CarVehicleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
