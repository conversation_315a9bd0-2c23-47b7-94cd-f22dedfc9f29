package com.ly.adp.xapi.api.tda.receive.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ly.adp.common.entity.Result;
import com.ly.adp.common.entity.enums.CommonErrEnum;
import com.ly.adp.common.entity.enums.DataSourceEnum;
import com.ly.adp.xapi.api.tda.entity.IdBargeInfo;
import com.ly.adp.xapi.api.tda.entity.idbarge.Common;
import com.ly.adp.xapi.api.tda.entity.idbarge.IdBargeInfoDto;
import com.ly.adp.xapi.api.tda.feign.TdaFeign;
import com.ly.adp.xapi.api.tda.feign.TdaResponse;
import com.ly.adp.xapi.api.tda.mapper.IdBargeMapper;
import com.ly.adp.xapi.api.tda.receive.ITdaNormalService;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.component.helper.StringHelper;
import io.seata.common.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * tda 工牌信息接收
 * <AUTHOR>
 * @Date 2024/7/3
 * @Version 1.0.0
 **/
@Service
public class TdaNormalService implements ITdaNormalService {

    private static final Logger log = LoggerFactory.getLogger(TdaNormalService.class);

    @Value("${refer.tda.token}")
    private String referTdaToken;

    @Value("${refer.tda.menu}")
    private String tdaUserMenu;

    @Value("${refer.tda.page}")
    private int tdaPage;

    @Value("${refer.tda.offset}")
    private int tdaOffset;

    @Autowired
    private Executor asyncTaskExecutor;

    @Resource
    private IdBargeMapper idBargeMapper;

    @Resource
    private TdaFeign tdaFeign;

    /**
     * 校验公共信息
     * @param common
     * @param result
     * @return
     */
    private Common validateCommonInfo(Common common, Result result) {
        Optional.ofNullable(common.getSn())
                .orElseGet(() -> {
                    result.setSuccess(false);
                    result.setCode(CommonErrEnum.BIZ_ERR.getCode());
                    result.setMsg("缺失工牌SN号");
                    return null;
                });
        return common;
    }

    /**
     * 更新工牌信息
     *
     * @param idBargeInfoDto
     * @param idBargeInfo
     */
    private void updateIdBarge(IdBargeInfoDto idBargeInfoDto, IdBargeInfo idBargeInfo) {
        idBargeInfo.setDataForUpdate(idBargeInfoDto);
        SwitchDbInvoke.invoke(DataSourceEnum.base.getJdbcName(), () -> idBargeMapper.updateById(idBargeInfo));
    }

    /**
     * 插入工牌信息
     * @param idBargeInfoDto
     */
    private void insertIdBarge(IdBargeInfoDto idBargeInfoDto) {
        IdBargeInfo idBargeInfo = new IdBargeInfo();
        idBargeInfo.setDataForInsert(idBargeInfoDto);
        SwitchDbInvoke.invoke(DataSourceEnum.base.getJdbcName(), () -> idBargeMapper.insert(idBargeInfo));
    }

    /**
     * 通过工牌sn号查询工牌信息
     * @param sn
     */
    private IdBargeInfo queryIdBargeBySn(String sn) {
        LambdaQueryWrapper<IdBargeInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IdBargeInfo::getSn, sn);
        return SwitchDbInvoke.invoke(DataSourceEnum.base.getJdbcName(), () -> idBargeMapper.selectOne(queryWrapper));
    }

    @Override
    public Result idbargeInfo(IdBargeInfoDto idBargeInfoDto) {
        Result result = Result.Builder.success();
        // 非空校验
        Optional.ofNullable(idBargeInfoDto)
                .map(IdBargeInfoDto::getCommon)
                .map(common -> validateCommonInfo(common, result))
                .orElseGet(() -> {
                    result.setSuccess(false);
                    result.setCode(CommonErrEnum.BIZ_ERR.getCode());
                    result.setMsg("缺失公共信息");
                    return null;
                });
        // 如果resultData不是成功的，则直接返回
        if (!result.isSuccess()) {
            return result;
        }

        // 工牌号
        String sn = idBargeInfoDto.getCommon().getSn();

        // 通过sn 获取 userid
        log.info("tda通过工牌获取用户信息接口工牌号：{}", sn);
        TdaResponse tdaResponse = tdaFeign.userInfo(referTdaToken, tdaUserMenu, tdaOffset, tdaPage, sn);
        log.info("tda通过工牌获取用户信息接口结果：{}", JSON.toJSONString(tdaResponse));
        if (tdaResponse.getCode() != 0) {
            result.setSuccess(false);
            result.setCode(CommonErrEnum.BIZ_ERR.getCode());
            result.setMsg(String.format("工牌:%s获取用户信息失败，失败原因：%s", sn, tdaResponse.getMsg()));
            return result;
        }
        if (CollectionUtils.isEmpty(tdaResponse.getData().getUsers())) {
            result.setSuccess(false);
            result.setCode(CommonErrEnum.BIZ_ERR.getCode());
            result.setMsg(String.format("工牌:%s查询不到对应用户信息", sn));
            return result;
        }
        idBargeInfoDto.getCommon().setUserId(tdaResponse.getData().getUsers().get(0).getUser_id());


        // 接收数据并存入数据库
        // 使用线程池执行数据库操作，避免阻塞和资源浪费
        CompletableFuture.runAsync(() ->  {
            // 判断当前工牌信息是否存在，存在即更新，不存在即插入
            IdBargeInfo idBargeInfo = queryIdBargeBySn(sn);
            if (StringHelper.IsEmptyOrNull(idBargeInfo)) {
                // 新增
                insertIdBarge(idBargeInfoDto);
            } else {
                // 更新
                updateIdBarge(idBargeInfoDto, idBargeInfo);
            }
        }, asyncTaskExecutor).exceptionally(ex -> {
            // 记录异常日志
            log.error("处理工牌信息异常", ex);
            result.setSuccess(false);
            result.setCode(CommonErrEnum.BIZ_ERR.getCode());
            result.setMsg(String.format("处理工牌信息异常:%s", ex.getMessage()));
            return null;
        });
        return result;
    }
}
