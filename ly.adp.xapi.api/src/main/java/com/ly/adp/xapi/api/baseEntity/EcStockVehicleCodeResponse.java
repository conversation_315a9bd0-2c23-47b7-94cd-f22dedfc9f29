package com.ly.adp.xapi.api.baseEntity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/11
 */
public class EcStockVehicleCodeResponse implements Serializable {

    private static final long serialVersionUID = -9200426347504492526L;


    @ApiModelProperty("VIN码")
    private String vin;
    @ApiModelProperty("销售状态（0:可售,1:售卖中,2:不可售）")
    private Integer salesStatus;
    @TableField("pno18")
    @ApiModelProperty("PNO18")
    private String pno18;
    @ApiModelProperty("车辆类型")
    private Integer vehicleType;
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;
    @ApiModelProperty("售价")
    private BigDecimal retailPrice;
    @ApiModelProperty("是否叠加现车权益(0:否,1:是)")
    private Integer isOverlay;

    @ApiModelProperty("是否批售(0:否,1:是)")
    private Integer isFleet;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司编码")
    private String companyCode;

    @ApiModelProperty("纳税人识别号")
    private String taxNum;

    @ApiModelProperty("地址")
    private String addr;

    @ApiModelProperty("电话")
    private String phone;

    @ApiModelProperty("银行")
    private String bank;

    @ApiModelProperty("银行账号")
    private String bankNum;

    @ApiModelProperty("邮箱")
    private String mail;

    @Override
    public String toString() {
        return "EcStockVehicleCodeResponse{" +
                "vin='" + vin + '\'' +
                ", salesStatus=" + salesStatus +
                ", pno18='" + pno18 + '\'' +
                ", vehicleType=" + vehicleType +
                ", discountAmount=" + discountAmount +
                ", retailPrice=" + retailPrice +
                ", isOverlay=" + isOverlay +
                ", isFleet=" + isFleet +
                ", companyName='" + companyName + '\'' +
                ", companyCode='" + companyCode + '\'' +
                ", taxNum='" + taxNum + '\'' +
                ", addr='" + addr + '\'' +
                ", phone='" + phone + '\'' +
                ", bank='" + bank + '\'' +
                ", bankNum='" + bankNum + '\'' +
                ", mail='" + mail + '\'' +
                '}';
    }

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Integer getSalesStatus() {
        return salesStatus;
    }

    public void setSalesStatus(Integer salesStatus) {
        this.salesStatus = salesStatus;
    }

    public String getPno18() {
        return pno18;
    }

    public void setPno18(String pno18) {
        this.pno18 = pno18;
    }

    public Integer getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(Integer vehicleType) {
        this.vehicleType = vehicleType;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public Integer getIsOverlay() {
        return isOverlay;
    }

    public void setIsOverlay(Integer isOverlay) {
        this.isOverlay = isOverlay;
    }

    public Integer getIsFleet() {
        return isFleet;
    }

    public void setIsFleet(Integer isFleet) {
        this.isFleet = isFleet;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getTaxNum() {
        return taxNum;
    }

    public void setTaxNum(String taxNum) {
        this.taxNum = taxNum;
    }

    public String getAddr() {
        return addr;
    }

    public void setAddr(String addr) {
        this.addr = addr;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getBank() {
        return bank;
    }

    public void setBank(String bank) {
        this.bank = bank;
    }

    public String getBankNum() {
        return bankNum;
    }

    public void setBankNum(String bankNum) {
        this.bankNum = bankNum;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

}
