package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.OtdWebUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.bucn.component.xapi.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;

@SendService("otd_adp")
public class OtdSendData extends SendDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(OtdSendData.class);

    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) throws Exception {
        //String json = String.valueOf(o);
        String result = OtdWebUtil.postData(xapiInstance.getSystem().getXapiKv("provide_path").getValueTee() + xapiInstance.getRemark(), o);
        return result;
    }

    @Override
    public XapiResult<String> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) {
        XapiResult<String> result = XapiResult.creat();
        try {
            String respJson = String.valueOf(o);
            if ("httperror".equals(respJson)) {
                throw new RuntimeException("httperror");
            }
            Map<String, Object> respMap = JSONObject.parseObject(respJson, Map.class);
            if (respMap.containsKey("resultCode")) {
                if ("200".equals(respMap.get("resultCode").toString())) {
                    if (respMap.containsKey("data") && respMap.get("data") != null) {
                        List<Map<String, String>> message = (List<Map<String, String>>) respMap.get("data");
                        for (Map<String, String> messageMap : message) {
                            if ("N".equals(messageMap.get("isSuccess"))) {
                                result.addErrorData(new XapiResult.XapiResultData<String>(messageMap.get("id"), StringUtil.subStr(messageMap.get("message"), 200)));
                            }
                        }
                    }
                } else {
                    result.setStatus(1);
                    if (respMap.containsKey("errMsg")) {
                        result.setMessage(StringUtil.subStr(String.valueOf(respMap.get("errMsg")), 200));
                    } else {
                        result.setMessage("对方未返回错误信息");
                    }
                }
            } else {
                result.setStatus(1);
                result.setMessage("对方未返回响应码");
            }
        } catch (Exception e) {
            log.error("调用异常", e);
            result.setStatus(1);
            result.setMessage(StringUtil.subStr(e.getMessage(), 200));
        }
        return result;
    }
}
