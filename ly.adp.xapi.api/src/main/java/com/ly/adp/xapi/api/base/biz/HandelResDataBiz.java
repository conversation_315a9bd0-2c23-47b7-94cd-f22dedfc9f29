package com.ly.adp.xapi.api.base.biz;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.mapper.BasePcmsResMapper;
import com.ly.adp.xapi.api.baseEntity.ExteriorsResponseEntity;
import com.ly.adp.xapi.api.baseEntity.InteriorResponseEntitiy;
import com.ly.adp.xapi.api.baseEntity.OptionResponseEntity;
import com.ly.adp.xapi.api.baseEntity.Pno18ResponseEntity;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.mp.busicen.common.helper.StringHelper;

@Service
public class HandelResDataBiz {
	@Autowired	
	BasePcmsResMapper basePcmsResMapper;
	private static final Logger log = LoggerFactory.getLogger(HandelResDataBiz.class);

	
	public void handelPno18Resp(Pno18ResponseEntity pno18ResponseEntity){
		try {
			//String jsonresp = String.valueOf(res);
			//Map<String, Object> resp = JSONObject.parseObject(jsonresp,Map.class);
			//if (resp.containsKey("code")) {
				if ("200".equals(String.valueOf(pno18ResponseEntity.getCode()))) {
					if (pno18ResponseEntity.getData()!=null) {						
						List<Map<String, Object>> listData = (List<Map<String, Object>>) pno18ResponseEntity.getData();		
							String batchNo = UUID.randomUUID().toString().replace("-", "").toLowerCase();
							for(Map<String, Object> pno18Map:listData){
								pno18Map.put("xapibatchno", batchNo);
								pno18Map.put("id", UUID.randomUUID().toString());
								basePcmsResMapper.insertPno18(pno18Map);
								List<Map<String, Object>> optionalList=(List<Map<String, Object>>) pno18Map.get("options");
								if(optionalList!=null&&optionalList.size()>0){
									for(Map<String, Object> optionalMap:optionalList){
										optionalMap.put("xapibatchno", batchNo);
										optionalMap.put("id", UUID.randomUUID().toString());
										basePcmsResMapper.insertOptional(optionalMap);
									}
								}
							}
					}
				}
			//}
		} catch (Exception e) {
			log.error("handelPno18Resp处理数据失败",e);
		}
		
	}
	
	public void handelExteriorsResp(ExteriorsResponseEntity exteriorsResponseEntity){
		try {			
			//String jsonresp = String.valueOf(res);
			//Map<String, Object> resp = JSONObject.parseObject(jsonresp,Map.class);
			//if (resp.containsKey("code")) {
				if ("200".equals(String.valueOf(exteriorsResponseEntity.getCode()))) {
					if (exteriorsResponseEntity.getData()!=null) {						
						List<Map<String, Object>> listData = exteriorsResponseEntity.getData();	
							for(Map<String, Object> exteriorsMap:listData){
								exteriorsMap.put("id", UUID.randomUUID().toString());
								basePcmsResMapper.insertExteriors(exteriorsMap);
							}
					}
				}
			//}
		} catch (Exception e) {
			log.error("handelExteriorsResp处理数据失败",e);
		}
		
	}
	
	public void handelInteriorResp( InteriorResponseEntitiy interiorResponseEntitiy){
		try {			
			//String jsonresp = String.valueOf(res);
			//Map<String, Object> resp = JSONObject.parseObject(jsonresp,Map.class);
			//if (resp.containsKey("code")) {
				if ("200".equals(String.valueOf(interiorResponseEntitiy.getCode()))) {
					if (interiorResponseEntitiy.getData()!=null) {				
						List<Map<String, Object>> listData = interiorResponseEntitiy.getData();	
							for(Map<String, Object> interiorsMap:listData){
								interiorsMap.put("id", UUID.randomUUID().toString());
								basePcmsResMapper.insertInteriors(interiorsMap);
							}
					}
				}
			//}
		} catch (Exception e) {
			log.error("handelInteriorResp处理数据失败",e);
		}
		
	}
	
	public void handelOptionalResp(OptionResponseEntity optionResponseEntity){
		try {			
			//String jsonresp = String.valueOf(res);
			//Map<String, Object> resp = JSONObject.parseObject(jsonresp,Map.class);
			//if (resp.containsKey("code")) {
				if ("200".equals(String.valueOf(optionResponseEntity.getCode()))) {
					if (optionResponseEntity.getData()!=null) {						
						List<Map<String, Object>> listData =optionResponseEntity.getData();	
							for(Map<String, Object> optionalMap:listData){
								optionalMap.put("id", UUID.randomUUID().toString());
								basePcmsResMapper.insertOptionalM(optionalMap);
							}
					}
				}
			//}
		} catch (Exception e) {
			log.error("handelOptionalResp处理数据失败",e);
		}
		
	}


}
