package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.CdpBiz;
import com.ly.adp.xapi.api.base.biz.CdpWebUtil;
import com.ly.bucn.component.xapi.entity.XapiField;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.pull.PullDataBase;
import com.ly.bucn.component.xapi.service.pull.PullService;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.stream.Collectors;

@PullService("cdp_adp")
public class CdpPullService extends PullDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(CdpPullService.class);

    private final Base64.Encoder encoder = Base64.getEncoder();

    @Override
    public Object serializeParam(XapiInstance xapiInstance, XapiMap<String, Object> context, XapiMap<String, Object> param) {
        List<XapiField> xapiFieldList = ((XapiTable)context.getValue("paramTable")).getFields().stream().filter((m) -> {
            //return "sortBegin".equals(m.getDbType());
            return String.valueOf(m.getDbType()).startsWith("sortBegin");
        }).collect(Collectors.toList());

        if(xapiFieldList.size() > 0){
            XapiField xapiField = xapiFieldList.get(0);
            xapiField.setIsBatchno(xapiField.getDbType().split("#")[1]);
            if(context.containsKey(xapiField.getViewField())){
                param.put(xapiField.getViewField(), context.get(xapiField.getViewField()));
            }
            context.put("xapiFieldParam", xapiField);
        }

        return param;
    }

    @Override
    public Object pullData(XapiInstance xapiInstance, XapiMap<String, Object> context, Object param) throws Exception {
        //获取token
        String token = "";
        if(!context.containsKey("access_token")) {
            token = CdpBiz.getToken(xapiInstance);
            context.put("access_token", token);
        }else{
            token = String.valueOf(context.get("access_token"));
        }

        //获取数据
        Map<String,String> headG =  new HashMap<>();
        headG.put("Authorization", "Bearer " + token);

        return CdpWebUtil.getData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getService(), headG, (Map<String, String>) param);
    }

    @Override
    public XapiData deserializeData(XapiInstance xapiInstance, XapiMap<String, Object> context, Object data) {
        context.put("stillFlag", false);
        String jsonresp = String.valueOf(data);
        Map<String, Object> resp = JSONObject.parseObject(jsonresp,Map.class);
        if(resp.containsKey("error")){
            throw new RuntimeException("返回值异常:" + jsonresp);
        }
        XapiData xapiData = XapiData.creat();
        if(resp.containsKey("data")){
            List<Map<String, Object>> listData = (List<Map<String, Object>>) resp.get("data");
            if(listData.size() > 0){
                listData.forEach( m ->{
                    XapiData.XapiDataRow xapiDataRow = XapiData.XapiDataRow.creat();
                    xapiDataRow.setMainData(m);
                    xapiData.add(xapiDataRow);
                });

                //继续取数
                if(context.containsKey("xapiFieldParam")){
                    XapiField xapiField = (XapiField)context.get("xapiFieldParam");
                    context.put(xapiField.getViewField(), String.valueOf(listData.get(listData.size() - 1).get(xapiField.getIsBatchno())));
                    context.put("stillFlag", true);
                }
            }
            return xapiData;
        }else{
            XapiTable xapiTable = (XapiTable)context.get("mainTable");
            List<XapiTable> childTables = (List)context.getValue("childTables");

            Map<String, Object> mainData = new HashMap<>();
            if(StringHelper.IsEmptyOrNull(xapiTable.getSubFlag())){
                mainData = resp;
            }else{
                //mainData = (Map<String, Object>) resp.get(xapiTable.getSubFlag());
                Object obj = resp.get(xapiTable.getSubFlag());
                if(obj instanceof List){
                    List<Map<String, Object>> childData = (List)obj;
                    childData.forEach((m) -> {
                        XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                        dr.setMainData(m);
                        childTables.forEach((n) -> {
                            String firstFlag = n.getSubFlag();
                            List<Map<String, Object>> childdata = (List)m.get(firstFlag);
                            dr.addChild(n, childdata);
                            m.remove(firstFlag);
                        });
                        xapiData.add(dr);
                    });
                    return xapiData;
                }else if(obj instanceof Map){
                    mainData = (Map)obj;
                }
            }
            if(mainData.size() > 0){
                XapiData.XapiDataRow xapiDataRow = XapiData.XapiDataRow.creat();
                xapiDataRow.setMainData(mainData);
                for (XapiTable childTable: childTables) {
                    String firstFlag = childTable.getSubFlag();
                    List<Map<String, Object>> childdata = (List)mainData.get(firstFlag);
                    xapiDataRow.addChild(childTable, childdata);
                    mainData.remove(firstFlag);
                }
                xapiData.add(xapiDataRow);
            }
            return xapiData;
        }
    }

    @Override
    public void endPull(XapiInstance instance, XapiMap<String, Object> context, XapiResult<String> result) {
        if(context.containsKey("stillFlag") && (Boolean) context.get("stillFlag")){
            still();
        }
        super.endPull(instance, context, result);
    }

//    public static void main(String[] args) {
//        try{
//            Base64.Encoder encoder = Base64.getEncoder();
//            String text = "cl03dc17f44c3e44e:65eeb46bd1e1b0c699c673e204870d72cc3b2270";
//            byte[] textByte = text.getBytes("UTF-8");
//            String encodedText = encoder.encodeToString(textByte);
//            System.out.println(encodedText);
//            Map<String,String> head =  new HashMap<>();
//            head.put("Authorization", "Basic " + encodedText);
//
//            MultiValueMap<String,String> body =  new LinkedMultiValueMap<>();
//            body.add("grant_type", "client_credentials");
//            String tokenJ = CdpWebUtil.postData("https://cdp-api-dev.smart.cn/v2/oauth2/token", MediaType.APPLICATION_FORM_URLENCODED, head, body);
//            System.out.println(tokenJ);
//            Map<String,Object> tokenM = (Map<String,Object>) JSONObject.parse(tokenJ);
//
//            Map<String,String> headG =  new HashMap<>();
//            headG.put("Authorization", "Bearer " + tokenM.get("access_token"));
//
//            Map<String,String> param =  new HashMap<>();
//            param.put("lastUpdated[ge]", "2022-01-29T09:16:17Z");
//            param.put("lastUpdated[le]", "2022-01-29T09:18:17Z");
//
//            System.out.println(CdpWebUtil.getData("https://cdp-api-dev.smart.cn/v2/customers", headG, param));
//
//        }catch (Exception e){
//            System.out.println(e);
//        }
//
//    }

}
