package com.ly.adp.xapi.api.otd;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ly.adp.xapi.api.base.CdpNormalService;
import com.ly.adp.xapi.api.base.biz.BpmWebUtil;
import com.ly.adp.xapi.api.baseEntity.CarVehicleTypeEnum;
import com.ly.adp.xapi.api.baseEntity.EcPolicyStrRequest;
import com.ly.adp.xapi.api.baseEntity.SalesStatusEnum;
import com.ly.adp.xapi.api.otd.mapper.OtdResMapper;
import com.ly.adp.xapi.api.util.MapToObjectConverterUtil;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.helper.JsonUtils;
import com.ly.mp.component.helper.StringHelper;
import io.swagger.annotations.ApiModelProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OtdAdpStockCarService {
    private static final Logger log = LoggerFactory.getLogger(CdpNormalService.class);

    @Autowired
    OtdResMapper otdResMapper;

    @Autowired
    IXapiLogBiz xapiLogBiz;

    @Transactional
    public Map<String, Object> otdAdpStockCar(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        String batchNo = StringHelper.GetGUID();
        log.info("{}-otd现车推送入：{}", batchNo, JSONObject.toJSONString(map));
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("otd_adp_stock_car");
        loginfo.setInJson(JSONObject.toJSONString(map));
        HttpHeaders httpHeaders = new HttpHeaders();
        int i = 1;
        String ecUrl = otdResMapper.findecUrl();
        try {
            List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("data");
            TransactionAspectSupport.currentTransactionStatus().isNewTransaction();

            //     List<String> nationwideDlr = otdResMapper.findNationwideDlr();
            for (Map<String, Object> paramMap : list) {

                if (StringHelper.IsEmptyOrNull(paramMap.get("PON18"))) {

                    throw new BusicenException("PNO18不能为空");

                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("VIN"))) {

                    throw new BusicenException("VIN不能为空");

                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("IS_SALES"))) {

                    throw new BusicenException("IS_SALES不能为空");

                }
                //otd入参IS_SALES 0不可售 1可售转化为adp值列表VE1033
                if ("0".equals(paramMap.get("IS_SALES"))) {

                    paramMap.put("IS_SALES", "2");
                } else if ("1".equals(paramMap.get("IS_SALES"))) {

                    paramMap.put("IS_SALES", "0");
                } else if ("2".equals(paramMap.get("IS_SALES"))) {

                    paramMap.put("IS_SALES", "1");
                }


                if (StringHelper.IsEmptyOrNull(paramMap.get("STORE_HOUSE"))) {

                    throw new BusicenException("STORE_HOUSE不能为空");

                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("CAR_TYPE"))) {

                    throw new BusicenException("CAR_TYPE不能为空");

                }

                if (StringHelper.IsEmptyOrNull(paramMap.get("IN_STORE_TIME"))) {

                    throw new BusicenException("IN_STORE_TIME不能为空");

                }

                //查重，判断是否新增（现车表：t_orc_stock_vehicle）,售卖中的数据不可修改
                Map<String, Object> mapCount = new HashMap<>();
                mapCount.put("vin", paramMap.get("VIN"));
                List<Map<String, Object>> stockCarCount = otdResMapper.selectStockCarCount(mapCount);

                String stockCarId = StringHelper.GetGUID();
                Map<String, Object> stockCarMap = new HashMap<>();
                stockCarMap.put("pno18", paramMap.get("PON18"));
                stockCarMap.put("stockDealerCode", paramMap.get("STORE_HOUSE"));
                stockCarMap.put("vin", paramMap.get("VIN"));
                stockCarMap.put("salesStatus", paramMap.get("IS_SALES"));
                stockCarMap.put("stockDate", paramMap.get("IN_STORE_TIME"));
                stockCarMap.put("vehicleType", paramMap.get("CAR_TYPE"));
                stockCarMap.put("retailNo", paramMap.get("retailNo"));
                stockCarMap.put("businessType", paramMap.get("businessType"));
                stockCarMap.put("stockStatus", paramMap.get("STOCK_STATUS"));
                stockCarMap.put("stockCarId", stockCarId);
                if (stockCarCount.size() > 0) {
                    // 修改
                    stockCarMap.put("stockCarId", stockCarCount.get(0).get("stockCarId"));
                    //如果是批售展车现车，并且推送状态是不可售，需要把批售展车batchSaleShelfStatus改为下架
                    if (CarVehicleTypeEnum.CAR_VEHICLE_TYPE_WHOLESALE.getCode().equals(paramMap.get("CAR_TYPE").toString())) {
                        if (SalesStatusEnum.NO_SALES.getCode().toString().equals(paramMap.get("IS_SALES"))
                                || SalesStatusEnum.SALESED.getCode().toString().equals(paramMap.get("IS_SALES"))) {
                            stockCarMap.put("batchSaleShelfStatus", 0);
                        }
                    }
                    otdResMapper.updateStockCar(stockCarMap);

                } else {

                    if ("1".equals(paramMap.get("businessType"))) {
                        otdResMapper.indertStockCar(stockCarMap);
                        continue;
                    }
                    boolean isSetSaleRange = false;
                    try {
                        // 批售展车类型4 不需要从EC拿权益,其他类型需要
                        if (!CarVehicleTypeEnum.CAR_VEHICLE_TYPE_WHOLESALE.getCode().equals(paramMap.get("CAR_TYPE").toString())) {
                            String jsonString = JSONObject.toJSONString(stockCarMap);
                            log.info("{}-OTD现车数据接收-调用ec-入参：{}", batchNo, jsonString);
                            String post = BpmWebUtil.post(ecUrl, jsonString, httpHeaders);
                            log.info("{}-OTD现车数据接收-调用ec-结果：{}", batchNo, post);
                            Rsp rsp = JSONObject.parseObject(post, Rsp.class);
                            if ("0".equals(rsp.getCode())) {
                                Rsp.Data data = rsp.getData();
                                if (!StringHelper.IsEmptyOrNull(data)) {
                                    stockCarMap.put("carDiscountPrice", data.getCarDiscountPrice());
                                    stockCarMap.put("isOverlay", data.getIsOverlay());
                                    stockCarMap.put("policyName", data.getPolicyName());
                                    stockCarMap.put("policyCode", data.getPolicyCode());
                                    stockCarMap.put("storeHouse", data.getStoreHouse());
                                    Integer policyStatus = data.getPolicyStatus();
                                    stockCarMap.put("policyStatus", policyStatus);
                                    isSetSaleRange = 1 == policyStatus;
                                    //额外的ec字段
                                    EcPolicyStrRequest ecPolicyStrRequest = new EcPolicyStrRequest();
                                    BeanUtil.copyProperties(data,ecPolicyStrRequest);
                                    stockCarMap.put("ecPolicyStr", JSONUtil.toJsonStr(ecPolicyStrRequest));
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("{}-OTD现车数据接收-调用ec-异常：{}", batchNo, e.getMessage(), e);
                    }
                    otdResMapper.indertStockCar(stockCarMap);
                    // 设置售卖门店范围
                    if (isSetSaleRange) {
                        setSalesRangeForOtd(stockCarMap, batchNo);
                    }
                }
                i++;
            }

//                //新增现车售卖范围表(存在则删除再新增：t_orc_stock_vehicle_scope)
//                Map<String, Object> scopeMap = new HashMap<>();
//                scopeMap.put("stockCarId", stockCarMap.get("stockCarId"));
//                scopeMap.put("vin", stockCarMap.get("vin"));
//                //scopeMap.put("dealerCode",stockCarMap.get("stockDealerCode"));
//                int count = otdResMapper.selectDetailCount(scopeMap);
//                if (count > 0) {
//                    otdResMapper.deleteDetail(scopeMap);
//                }
//
//                Map<String, String> salesRange = Maps.newHashMap();
      //      salesRange = otdResMapper.findSalesRange(paramMap);
//
//                if (!"VDC".equals(stockCarMap.get("stockDealerCode")) && StringHelper.IsEmptyOrNull(salesRange)) {
//
//                    salesRange = otdResMapper.findDCRange(paramMap);
//
//                }
//                if (StringHelper.IsEmptyOrNull(salesRange)) {
//
//                    if (!"VDC".equals(stockCarMap.get("stockDealerCode"))) {
//                        List<String> dlrCodeList = otdResMapper.selectDlrCodeList(stockCarMap);
//                        for (String dlrCode : dlrCodeList) {
//                            scopeMap.put("dealerCode", dlrCode);
//                            otdResMapper.insertDetail(scopeMap);
//                        }
//                    } else {
//                        scopeMap.put("dealerCode", stockCarMap.get("stockDealerCode"));
//                        otdResMapper.insertDetail(scopeMap);
//                    }
//                    i++;
//                    continue;
//
//                }
//                if ("全国".equals(salesRange.get("salesRange"))) {
//
//                    int insert = otdResMapper.insertNationwideDetail(scopeMap, nationwideDlr);
//                    i++;
//                    continue;
//
//                }
//                if ("大区".equals(salesRange.get("salesRange"))) {
//
//                    String region = otdResMapper.findRegion(paramMap);
//                    List<String> regionDlr = otdResMapper.findRegionDlr(region);
//                    if (!StringHelper.IsEmptyOrNull(regionDlr)) {
//                        int insert = otdResMapper.insertNationwideDetail(scopeMap, regionDlr);
//                    }
//                    i++;
//                    continue;
//                }
//                if ("代理商".equals(salesRange.get("salesRange"))) {
//                    List<String> agentDlr = otdResMapper.findAgent(paramMap);
//                    if (!StringHelper.IsEmptyOrNull(agentDlr)) {
//                        int insert = otdResMapper.insertNationwideDetail(scopeMap, agentDlr);
//                    }
//                    i++;
//                    continue;
//                }
//                if ("城市公司".equals(salesRange.get("salesRange"))) {
//                    List<String> dlrCodeList = otdResMapper.selectDlrCodeList(stockCarMap);
//                    if (!StringHelper.IsEmptyOrNull(dlrCodeList)) {
//                        int insert = otdResMapper.insertNationwideDetail(scopeMap, dlrCodeList);
//                    }
//                    i++;
//                    continue;
//                }
//
//                int insert = otdResMapper.insertDlrDetail(scopeMap, salesRange.get("salesRange"));
//                i++;
//            }

        } catch (Exception e) {
            log.error("{}-接收现车数据失败：{}", batchNo, e.getMessage(), e);
            result.put("success", false);
            result.put("errMsg", "第" + i + "行数据传输失败," + e.getMessage());

            loginfo.setLogStatus("-1");
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            throw new BusicenException("第" + i + "行数据传输失败," + e.getMessage());
        }

        result.put("success", true);
        result.put("errMsg", "操作成功");
        loginfo.setLogStatus("0");
        loginfo.setOutJson(JSONObject.toJSONString(result));
        xapiLogBiz.add(loginfo);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> smartAmount(Map<String, Object> map) {

        Map<String, Object> result = new HashMap<>(3);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("tradein_adp_smartAmount");
        loginfo.setLogTime(new Date());
        //loginfo.setLogStatus(logStatus);
        // loginfo.setLogMessage(msg);
        //  loginfo.setInJson(inJson);
        // loginfo.setOutJson(outJson);
        xapiLogBiz.add(loginfo);
        if (StringHelper.IsEmptyOrNull(map)) {
            result.put("EXTERNALREFERENCEID", "");
            result.put("RESULT", "E");
            result.put("MESSAGE", "内容为空");
            loginfo.setLogStatus("-1");
            loginfo.setLogMessage("内容为空");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        return result;
    }


    public Map<String, Object> cdpAdpPhoneUpdate(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        log.info("cdp下发电话更新记录入参：" + map);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("cdp_adp_phone_update");

        try {
            List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("data");
            if (CollectionUtils.isEmpty(list)) {
                throw new BusicenException("数据为空!");
            }
            otdResMapper.insertCdpAdpPhoneUpdate(list);

        } catch (Exception e) {
            log.info("cdpAdpPhoneUpdate error: {}", e);
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            loginfo.setLogStatus("-1");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            throw new BusicenException(e.getMessage());
        }

        result.put("success", true);
        result.put("errMsg", "操作成功");
        loginfo.setLogStatus("0");
        loginfo.setInJson(JSONObject.toJSONString(map));
        loginfo.setOutJson(JSONObject.toJSONString(result));
        xapiLogBiz.add(loginfo);
        return result;
    }

    public Map<String, Object> sdcAdpOrderRevocation(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        log.info("订单特殊流程申请撤回接口入参：" + map);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("sdc_adp_order_revocation");

        try {
            if (StringHelper.IsEmptyOrNull(map.get("dissociateCode"))) {
                throw new BusicenException("解配申请单号不能为空!");
            }
            if (StringHelper.IsEmptyOrNull(map.get("saleOrderCode"))) {
                throw new BusicenException("订单号不能为空!");
            }
            //校验解配单是否满足条件
            Map<String, Object> order_map = otdResMapper.selectOrderIsExists(map);
            if (CollectionUtils.isEmpty(order_map)) {
                throw new BusicenException("此订单不存在!");
            }
            if (!"6".equals(order_map.get("applyType")) && !"7".equals(order_map.get("applyType"))) {
                throw new BusicenException("只有逾期未付款和逾期未预约的订单可以撤回!");
            }
            if ("1".equals(order_map.get("column1"))) {
                throw new BusicenException("此订单已发送otd解绑!");
            }
            if ("6".equals(order_map.get("applyStatus"))) {
                throw new BusicenException("此订单已撤回!");
            }
            int flag = otdResMapper.updateOrderByRevocation(map);

        } catch (Exception e) {
            log.info("sdcAdpOrderRevocation error: {}", e);
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            loginfo.setLogStatus("-1");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            throw new BusicenException(e.getMessage());
        }

        result.put("success", true);
        result.put("errMsg", "操作成功");
        loginfo.setLogStatus("0");
        loginfo.setInJson(JSONObject.toJSONString(map));
        loginfo.setOutJson(JSONObject.toJSONString(result));
        xapiLogBiz.add(loginfo);
        return result;
    }

    public Map<String, Object> tradeinRightStatus(Map<String, Object> map) {

        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("tradein_adp_rightStatus");
        Map<String, Object> result = new HashMap<>();
        if (StringHelper.IsEmptyOrNull(map.get("replaceOrderNum"))) {
            result.put("code", 0);
            result.put("success", false);
            result.put("msg", "置换单号不能为空");
            loginfo.setLogStatus("-1");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        if (StringHelper.IsEmptyOrNull(map.get("saleOrderCode"))) {
            result.put("code", 0);
            result.put("success", false);
            result.put("msg", "订单号不能为空");
            loginfo.setLogStatus("-1");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }
        if (StringHelper.IsEmptyOrNull(map.get("rightStatus"))) {
            result.put("code", 0);
            result.put("success", false);
            result.put("msg", "置换权益申请单状态不能为空");
            loginfo.setLogStatus("-1");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }
        if ("30".equals(map.get("rightStatus")) || "40".equals(map.get("rightStatus")) || "60".equals(map.get("rightStatus"))) {
            map.put("rejectReason", map.get("rejectReason"));
        } else {
            map.put("rejectReason", "");
        }
        int update = otdResMapper.updateOrderToTradeinright(map);
        if (update != 1) {
            result.put("code", 0);
            result.put("success", false);
            result.put("msg", "未找到对应的置换权益申请单");
            loginfo.setLogStatus("-1");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        result.put("success", true);
        result.put("code", 1);
        result.put("msg", "操作成功");
        loginfo.setLogStatus("0");
        loginfo.setInJson(JSONObject.toJSONString(map));
        loginfo.setOutJson(JSONObject.toJSONString(result));
        xapiLogBiz.add(loginfo);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> ecAdpPolicyDiscount(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>(3);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("ec_adp_policyDiscount");

        if (StringHelper.IsEmptyOrNull(map.get("vin"))) {
            throw new BusicenException("vin码不能为空!");
        }

        String vin = String.valueOf(map.get("vin"));
        if (!(otdResMapper.queryVinExists(vin) > 0)) {
            loginfo.setLogStatus("0");
            result.put("success", true);
            result.put("code", "1");
            result.put("msg", String.format("操作成功:当前VIN:%s的现车信息OTD还未同步，不处理ec政策推送", vin));
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        Object policyStatus = map.get("policyStatus");
        if (StringHelper.IsEmptyOrNull(policyStatus)) {

            throw new BusicenException("政策状态不能为空!");
        }
        //处理政策新增字段
        EcPolicyStrRequest ecPolicyStrRequest = MapToObjectConverterUtil.convert(map);
        map.put("ecPolicyStr", JSONUtil.toJsonStr(ecPolicyStrRequest));
        if ("0".equals(policyStatus.toString())) {
            int update = otdResMapper.updateStockVehicle(map);
            loginfo.setLogStatus("0");

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }
        if (StringHelper.IsEmptyOrNull(map.get("policyName"))) {

            throw new BusicenException("现车政策名称不能为空!");
        }

        if (StringHelper.IsEmptyOrNull(map.get("isOverlay"))) {

            throw new BusicenException("是否叠加现行权益不能为空!");
        }

        if (StringHelper.IsEmptyOrNull(map.get("carDiscountPrice"))) {

            throw new BusicenException("优惠金额不能为空!");
        }

        if (StringHelper.IsEmptyOrNull(map.get("storeHouse"))) {

            throw new BusicenException("库存位置不能为空!");
        }

        int update = otdResMapper.updateStockVehicle(map);

        // 设置现车销售门店
        Map<String, Object> manualSetting = otdResMapper.findStockVehicle(map);
        if ("1".equals(manualSetting.get("manualSetting"))) {

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setLogStatus("0");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }
        Map<String, Object> scopeMap = new HashMap<>();
        scopeMap.put("stockCarId", manualSetting.get("stockCarId"));
        scopeMap.put("vin", map.get("vin"));
        //scopeMap.put("dealerCode",stockCarMap.get("stockDealerCode"));

        otdResMapper.deleteDetail(scopeMap);

        Map<String, String> salesRange = Maps.newHashMap();
        salesRange = otdResMapper.findRange(manualSetting, map.get("storeHouse"));
        if (!"VDC".equals(map.get("storeHouse")) && StringHelper.IsEmptyOrNull(salesRange)) {
            salesRange = otdResMapper.findEcDCRange(map,manualSetting.get("pno18"),manualSetting.get("vehicleType"));
        }
        if (StringHelper.IsEmptyOrNull(salesRange)) {
            log.info("无预设写入scope:{}", JSONObject.toJSONString(map));
            String storeHouse = String.valueOf(map.get("storeHouse"));
            if (!"VDC".equals(storeHouse)) {
//                List<String> dlrCodeList = otdResMapper.selecDlrCodeList(map);
                // DC库所在代理商下所有同城市的且已上线的POS/BC进行自动设置 2024-06-04
                List<String> dlrCodeList = otdResMapper.queryAgentCityDlr(storeHouse);
                for (String dlrCode : dlrCodeList) {
                    scopeMap.put("dealerCode", dlrCode);
                    otdResMapper.insertDetail(scopeMap);
                }
            } else {
                scopeMap.put("dealerCode", storeHouse);
                otdResMapper.insertDetail(scopeMap);
            }

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setLogStatus("0");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }


        if ("全国".equals(salesRange.get("salesRange"))) {
            List<String> nationwideDlr = otdResMapper.findNationwideDlr();
            log.info("全国门店写入scope:{}", JSONObject.toJSONString(map));
            int insert = otdResMapper.insertNationwideDetail(scopeMap, nationwideDlr);

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setLogStatus("0");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        if ("大区".equals(salesRange.get("salesRange"))) {

            String region = otdResMapper.findEcRegion(map);
            List<String> regionDlr = otdResMapper.findRegionDlr(region);
            if (!StringHelper.IsEmptyOrNull(regionDlr)) {
                log.info("大区门店写入scope:{}", JSONObject.toJSONString(map));
                int insert = otdResMapper.insertNationwideDetail(scopeMap, regionDlr);
            }

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setLogStatus("0");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        if ("代理商".equals(salesRange.get("salesRange"))) {
            List<String> agentDlr = otdResMapper.findecAgent(map);
            if (!StringHelper.IsEmptyOrNull(agentDlr)) {
                log.info("代理商门店写入scope:{}", JSONObject.toJSONString(map));
                int insert = otdResMapper.insertNationwideDetail(scopeMap, agentDlr);
            }

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setLogStatus("0");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        if ("城市公司".equals(salesRange.get("salesRange"))) {

            List<String> dlrCodeList = otdResMapper.selecDlrCodeList(map);
            if (!StringHelper.IsEmptyOrNull(dlrCodeList)) {
                log.info("城市公司门店写入scope:{}", JSONObject.toJSONString(map));
                int insert = otdResMapper.insertNationwideDetail(scopeMap, dlrCodeList);
            }

            result.put("success", true);
            result.put("code", "1");
            result.put("msg", "操作成功");
            loginfo.setLogStatus("0");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }

        log.info("自定义门店写入scope:{}", JSONObject.toJSONString(map));
        int insert = otdResMapper.insertDlrDetail(scopeMap, salesRange.get("salesRange"));

        result.put("success", true);
        result.put("code", "1");
        result.put("msg", "操作成功");
        loginfo.setLogStatus("0");
        loginfo.setInJson(JSONObject.toJSONString(map));
        loginfo.setOutJson(JSONObject.toJSONString(result));
        xapiLogBiz.add(loginfo);
        return result;
    }






    /**
     * OTD现车数据接收设置门店售卖范围
     * @param map
     * @param batchNo
     * @return
     */
    public void setSalesRangeForOtd(Map<String, Object> map, String batchNo) {
        try {
            String vin = String.valueOf(map.get("vin"));
            Map<String, Object> manualSetting = otdResMapper.findStockVehicle(map);
            if ("1".equals(manualSetting.get("manualSetting"))) {
                return;
            }

            Map<String, Object> scopeMap = new HashMap<>();
            scopeMap.put("stockCarId", manualSetting.get("stockCarId"));
            scopeMap.put("vin", vin);

            otdResMapper.deleteDetail(scopeMap);
            String storeHouse = String.valueOf(map.get("storeHouse"));
            Map<String, String> salesRange = otdResMapper.findRange(manualSetting, storeHouse);
            if (CollectionUtils.isEmpty(salesRange) && !"VDC".equals(storeHouse)) {
                salesRange = otdResMapper.findEcDCRange(map, manualSetting.get("pno18"), manualSetting.get("vehicleType"));
            }
            if (CollectionUtils.isEmpty(salesRange)) {
                log.info("无预设写入scope:{}", JSONObject.toJSONString(map));
                handleNoPresetRange(storeHouse, scopeMap);
                return;
            }

            String range = salesRange.get("salesRange");
            if ("全国".equals(range)) {
                List<String> nationwideDlr = otdResMapper.findNationwideDlr();
                log.info("全国门店写入scope:{}", JSONObject.toJSONString(map));
                otdResMapper.insertNationwideDetail(scopeMap, nationwideDlr);
            } else if ("大区".equals(range)) {
                String region = otdResMapper.findEcRegion(map);
                List<String> regionDlr = otdResMapper.findRegionDlr(region);
                if (!StringHelper.IsEmptyOrNull(regionDlr)) {
                    log.info("大区门店写入scope:{}", JSONObject.toJSONString(map));
                    otdResMapper.insertNationwideDetail(scopeMap, regionDlr);
                }
            } else if ("代理商".equals(range)) {
                List<String> agentDlr = otdResMapper.findecAgent(map);
                if (!StringHelper.IsEmptyOrNull(agentDlr)) {
                    log.info("代理商门店写入scope:{}", JSONObject.toJSONString(map));
                    otdResMapper.insertNationwideDetail(scopeMap, agentDlr);
                }
            } else if ("城市公司".equals(range)) {
                List<String> dlrCodeList = otdResMapper.selecDlrCodeList(map);
                if (!StringHelper.IsEmptyOrNull(dlrCodeList)) {
                    log.info("城市公司门店写入scope:{}", JSONObject.toJSONString(map));
                    otdResMapper.insertNationwideDetail(scopeMap, dlrCodeList);
                }
            } else {
                log.info("自定义门店写入scope:{}", JSONObject.toJSONString(map));
                otdResMapper.insertDlrDetail(scopeMap, range);
            }
        } catch (Exception e) {
            log.error("{}-OTD现车数据接收-设置售卖门店-异常：{}", batchNo, e.getMessage(), e);
        }
    }

    private void handleNoPresetRange(String storeHouse, Map<String, Object> scopeMap) {
        if (!"VDC".equals(storeHouse)) {
            List<String> dlrCodeList = otdResMapper.queryAgentCityDlr(storeHouse);
            for (String dlrCode : dlrCodeList) {
                scopeMap.put("dealerCode", dlrCode);
                otdResMapper.insertDetail(scopeMap);
            }
        } else {
            scopeMap.put("dealerCode", storeHouse);
            otdResMapper.insertDetail(scopeMap);
        }
    }

    public static class Rsp {
        String code;
        String msg;
        String status;
        Data data;

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public Data getData() {
            return data;
        }

        public void setData(Data data) {
            this.data = data;
        }

        public static class Data {
            String policyName;
            String policyCode;
            Integer isOverlay;
            BigDecimal carDiscountPrice;
            Integer policyStatus;
            String storeHouse;

            @ApiModelProperty("个人批售金额")
            private String personalFleetPrice;

            @ApiModelProperty("企业批售金额")
            private String enterpriseFleetPrice;

            @ApiModelProperty("企业msrp价格")
            private String companyMsrp;

            @ApiModelProperty("个人msrp价格")
            private String personMsrp;

//            @ApiModelProperty(value = "现车优惠金额")
//            private String carDiscountPrice;

            @ApiModelProperty("大定权益优惠")
            private String userRightsDiscount;

            @ApiModelProperty("企业sap优惠折扣")
            private String companySapDiscount;

            @ApiModelProperty("个人sap优惠折扣")
            private String personSapDiscount;

            @ApiModelProperty("企业国家补贴")
            private String companyStateSubsidies;

            @ApiModelProperty("个人国家补贴")
            private String personStateSubsidies;

            @ApiModelProperty("企业优惠总额")
            private String companyDiscountTotalPrice;

            @ApiModelProperty("个人优惠总额")
            private String personDiscountTotalPrice;

            @ApiModelProperty("企业车辆总价/企业实际车价")
            private String companyVehicleAmount;

            @ApiModelProperty("个人车辆总价/个人实际车价")
            private String personVehicleAmount;


            public String getPolicyCode() {
                return policyCode;
            }

            public void setPolicyCode(String policyCode) {
                this.policyCode = policyCode;
            }

            public String getPolicyName() {
                return policyName;
            }

            public void setPolicyName(String policyName) {
                this.policyName = policyName;
            }

            public Integer getIsOverlay() {
                return isOverlay;
            }

            public void setIsOverlay(Integer isOverlay) {
                this.isOverlay = isOverlay;
            }

            public BigDecimal getCarDiscountPrice() {
                return carDiscountPrice;
            }

            public void setCarDiscountPrice(BigDecimal carDiscountPrice) {
                this.carDiscountPrice = carDiscountPrice;
            }

            public Integer getPolicyStatus() {
                return policyStatus;
            }

            public void setPolicyStatus(Integer policyStatus) {
                this.policyStatus = policyStatus;
            }

            public String getStoreHouse() {
                return storeHouse;
            }

            public void setStoreHouse(String storeHouse) {
                this.storeHouse = storeHouse;
            }

            public String getPersonalFleetPrice() {
                return personalFleetPrice;
            }

            public void setPersonalFleetPrice(String personalFleetPrice) {
                this.personalFleetPrice = personalFleetPrice;
            }

            public String getEnterpriseFleetPrice() {
                return enterpriseFleetPrice;
            }

            public void setEnterpriseFleetPrice(String enterpriseFleetPrice) {
                this.enterpriseFleetPrice = enterpriseFleetPrice;
            }

            public String getCompanyMsrp() {
                return companyMsrp;
            }

            public void setCompanyMsrp(String companyMsrp) {
                this.companyMsrp = companyMsrp;
            }

            public String getPersonMsrp() {
                return personMsrp;
            }

            public void setPersonMsrp(String personMsrp) {
                this.personMsrp = personMsrp;
            }

            public String getUserRightsDiscount() {
                return userRightsDiscount;
            }

            public void setUserRightsDiscount(String userRightsDiscount) {
                this.userRightsDiscount = userRightsDiscount;
            }

            public String getCompanySapDiscount() {
                return companySapDiscount;
            }

            public void setCompanySapDiscount(String companySapDiscount) {
                this.companySapDiscount = companySapDiscount;
            }

            public String getPersonSapDiscount() {
                return personSapDiscount;
            }

            public void setPersonSapDiscount(String personSapDiscount) {
                this.personSapDiscount = personSapDiscount;
            }

            public String getCompanyStateSubsidies() {
                return companyStateSubsidies;
            }

            public void setCompanyStateSubsidies(String companyStateSubsidies) {
                this.companyStateSubsidies = companyStateSubsidies;
            }

            public String getPersonStateSubsidies() {
                return personStateSubsidies;
            }

            public void setPersonStateSubsidies(String personStateSubsidies) {
                this.personStateSubsidies = personStateSubsidies;
            }

            public String getCompanyDiscountTotalPrice() {
                return companyDiscountTotalPrice;
            }

            public void setCompanyDiscountTotalPrice(String companyDiscountTotalPrice) {
                this.companyDiscountTotalPrice = companyDiscountTotalPrice;
            }

            public String getPersonDiscountTotalPrice() {
                return personDiscountTotalPrice;
            }

            public void setPersonDiscountTotalPrice(String personDiscountTotalPrice) {
                this.personDiscountTotalPrice = personDiscountTotalPrice;
            }

            public String getCompanyVehicleAmount() {
                return companyVehicleAmount;
            }

            public void setCompanyVehicleAmount(String companyVehicleAmount) {
                this.companyVehicleAmount = companyVehicleAmount;
            }

            public String getPersonVehicleAmount() {
                return personVehicleAmount;
            }

            public void setPersonVehicleAmount(String personVehicleAmount) {
                this.personVehicleAmount = personVehicleAmount;
            }
        }
    }
}


