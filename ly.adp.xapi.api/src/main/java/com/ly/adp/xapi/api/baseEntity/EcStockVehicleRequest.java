package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:ec/otd现车接口查询
 * @date 2023/4/3
 */
@ApiModel("ec/otd请求数据")
public class EcStockVehicleRequest implements Serializable {
    private static final long serialVersionUID = 7645223514161632459L;

    @ApiModelProperty("EC查询入参VIN码")
    private String vin;
    @ApiModelProperty("OTD查询入参VIN码")
    private List<String> vins;

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public List<String> getVins() {
        return vins;
    }

    public void setVins(List<String> vins) {
        this.vins = vins;
    }
}
