package com.ly.adp.xapi.api.clue.feign;

import com.fasterxml.jackson.annotation.JsonFormat;
import groovy.transform.EqualsAndHashCode;
import groovy.transform.ToString;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description: 线索用户旅程返回VO
 * @Author: rik.ren
 * @Date: 2025/3/15 18:11
 **/
@ToString
@EqualsAndHashCode
public class ClueEventFlowRsp implements Serializable {
    public ClueEventFlowRsp(LocalDateTime triggerTime, String eventName, Integer eventType, String custId) {
        this.triggerTime = triggerTime;
        this.eventName = eventName;
        this.eventType = eventType;
        this.custId = custId;
    }

    public ClueEventFlowRsp() {
    }

    /**
     * 触发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime triggerTime;
    /**
     * 事件名称
     */
    private String eventName;
    /**
     * "事件类型，1了解，2到店，3试驾，4下定，5交车，6战败
     */
    private Integer eventType;

    /**
     * 线索id
     */
    private String custId;

    public LocalDateTime getTriggerTime() {
        return triggerTime;
    }

    public void setTriggerTime(LocalDateTime triggerTime) {
        this.triggerTime = triggerTime;
    }

    public String getEventName() {
        return eventName;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }
}
