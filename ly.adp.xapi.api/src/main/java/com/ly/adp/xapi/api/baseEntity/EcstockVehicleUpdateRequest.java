package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:ec更新现车可售状态
 * @date 2023/4/3
 */
public class EcstockVehicleUpdateRequest implements Serializable {
    private static final long serialVersionUID = 5177533517426143641L;

    @ApiModelProperty("EC入参VIN码")
    private String vin;
    @ApiModelProperty("订单编号")
    private String saleOrder;
    @ApiModelProperty("订单节点（0，生成订单；1，取消订单；2，退款;3,支付成功）")
    private String saleNode;

    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public String getSaleOrder() {
        return saleOrder;
    }

    public void setSaleOrder(String saleOrder) {
        this.saleOrder = saleOrder;
    }

    public String getSaleNode() {
        return saleNode;
    }

    public void setSaleNode(String saleNode) {
        this.saleNode = saleNode;
    }
}
