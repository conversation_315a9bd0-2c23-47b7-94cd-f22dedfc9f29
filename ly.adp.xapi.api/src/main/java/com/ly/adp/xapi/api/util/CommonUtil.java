package com.ly.adp.xapi.api.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class CommonUtil {

    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);

    /**
     * 实体list信息转为 json 字符串
     * @param list
     * @return
     */
    public static <E> String listToJsonString(List<E> list) {
        // 将 List 转换为 JSONArray
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(list));
        // 将 JSONArray 转换为 JSON 字符串
        return jsonArray.toJSONString();
    }

    /**
     * 实体信息转为 json 字符串
     * @param t
     * @return
     */
    public static <T> String objToJsonString(T t) {
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = "";
        try {
            // 将对象转换为JSON字符串
            jsonString = objectMapper.writeValueAsString(t);
        } catch (JsonProcessingException e) {
            log.error("实体转换json失败");
        }
        return jsonString;
    }
}
