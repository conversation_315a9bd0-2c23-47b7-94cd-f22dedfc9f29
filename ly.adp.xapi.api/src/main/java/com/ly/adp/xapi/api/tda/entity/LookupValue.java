package com.ly.adp.xapi.api.tda.entity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/11/15
 */
public class LookupValue implements Serializable {


    private static final long serialVersionUID = 8387773793813374989L;

    String lookupValueCode;

    String lookupValueName;

    public String getLookupValueCode() {
        return lookupValueCode;
    }

    public void setLookupValueCode(String lookupValueCode) {
        this.lookupValueCode = lookupValueCode;
    }

    public String getLookupValueName() {
        return lookupValueName;
    }

    public void setLookupValueName(String lookupValueName) {
        this.lookupValueName = lookupValueName;
    }

    @Override
    public String toString() {
        return "LookupValue{" +
                "lookupValueCode='" + lookupValueCode + '\'' +
                ", lookupValueName='" + lookupValueName + '\'' +
                '}';
    }
}
