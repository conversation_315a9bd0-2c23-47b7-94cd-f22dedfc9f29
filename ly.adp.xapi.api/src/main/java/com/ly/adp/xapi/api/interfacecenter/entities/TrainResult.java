package com.ly.adp.xapi.api.interfacecenter.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * 培训结果接收接口
 *
 * @TableName t_ifr_base_train_result
 */
@TableName("t_ifr_base_train_result")
public class TrainResult {

    /**
     * 主键ID
     */
    @TableId("LOGS_ID")
    private String logsId;

    /**
     * 岗位名称
     */
    @TableField("POSITION_NAME")
    private String positionName;

    /**
     * 部门名称
     */
    @TableField("ORGANIZE_NAME")
    private String organizeName;

    /**
     * 部门code
     */
    @TableField("ORGANIZE_CODE")
    private String organizeCode;

    /**
     * 岗位code
     */
    @TableField("POSITION_CODE")
    private String positionCode;

    /**
     * 通过培训人数
     */
    @TableField("PASSTRAIN_NUM")
    private String passTrainNum;

    /**
     * 编制人数
     */
    @TableField("FORMATION_NUM")
    private String formationNum;

    /**
     * 通过培训的工号集合
     */
    @TableField("EMPLOYEE_CODE_LIST")
    private String employeeCodeList;

    /**
     * 通过爱学在线工号集合
     */
    @TableField("PASS_MAP_USER_LIST")
    private String passMapUserList;

    /**
     * 通过岗位认证工号集合
     */
    @TableField("PASS_TMS_USER_LIST")
    private String passTmsUserList;

    /**
     * 缺口人数（编制人数-实际通过培训人数）
     */
    @TableField("LACK_NUM")
    private String lackNum;

    /**
     * 当前部门岗位下的人数
     */
    @TableField("POST_PERSON_NUM")
    private String postPersonNum;

    /**
     * 插入日期
     */
    @TableField(value = "INSERT_DATE")
    private LocalDateTime insertDate;

    /**
     * 接收状态
     */
    @TableField(value = "RECEIVE_FLAG")
    private String receiveFlag;

    /**
     * 错误日志
     */
    @TableField("ERR_LOG")
    private String errLog;

    /**
     * 接收日期
     */
    @TableField(value = "RECEIVE_DATE")
    private LocalDateTime receiveDate;

    public String getLogsId() {
        return logsId;
    }

    public void setLogsId(String logsId) {
        this.logsId = logsId;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getOrganizeName() {
        return organizeName;
    }

    public void setOrganizeName(String organizeName) {
        this.organizeName = organizeName;
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getPassTrainNum() {
        return passTrainNum;
    }

    public void setPassTrainNum(String passTrainNum) {
        this.passTrainNum = passTrainNum;
    }

    public String getFormationNum() {
        return formationNum;
    }

    public void setFormationNum(String formationNum) {
        this.formationNum = formationNum;
    }

    public String getEmployeeCodeList() {
        return employeeCodeList;
    }

    public void setEmployeeCodeList(String employeeCodeList) {
        this.employeeCodeList = employeeCodeList;
    }

    public String getPassMapUserList() {
        return passMapUserList;
    }

    public void setPassMapUserList(String passMapUserList) {
        this.passMapUserList = passMapUserList;
    }

    public String getPassTmsUserList() {
        return passTmsUserList;
    }

    public void setPassTmsUserList(String passTmsUserList) {
        this.passTmsUserList = passTmsUserList;
    }

    public String getLackNum() {
        return lackNum;
    }

    public void setLackNum(String lackNum) {
        this.lackNum = lackNum;
    }

    public String getPostPersonNum() {
        return postPersonNum;
    }

    public void setPostPersonNum(String postPersonNum) {
        this.postPersonNum = postPersonNum;
    }

    public LocalDateTime getInsertDate() {
        return insertDate;
    }

    public void setInsertDate(LocalDateTime insertDate) {
        this.insertDate = insertDate;
    }

    public String getReceiveFlag() {
        return receiveFlag;
    }

    public void setReceiveFlag(String receiveFlag) {
        this.receiveFlag = receiveFlag;
    }

    public String getErrLog() {
        return errLog;
    }

    public void setErrLog(String errLog) {
        this.errLog = errLog;
    }

    public LocalDateTime getReceiveDate() {
        return receiveDate;
    }

    public void setReceiveDate(LocalDateTime receiveDate) {
        this.receiveDate = receiveDate;
    }

    @Override
    public String toString() {
        return "TrainResult{" +
                "logsId='" + logsId + '\'' +
                ", positionName='" + positionName + '\'' +
                ", organizeName='" + organizeName + '\'' +
                ", organizeCode='" + organizeCode + '\'' +
                ", positionCode='" + positionCode + '\'' +
                ", passTrainNum='" + passTrainNum + '\'' +
                ", formationNum='" + formationNum + '\'' +
                ", employeeCodeList='" + employeeCodeList + '\'' +
                ", passMapUserList='" + passMapUserList + '\'' +
                ", passTmsUserList='" + passTmsUserList + '\'' +
                ", lackNum='" + lackNum + '\'' +
                ", postPersonNum='" + postPersonNum + '\'' +
                ", insertDate=" + insertDate +
                ", receiveFlag='" + receiveFlag + '\'' +
                ", errLog='" + errLog + '\'' +
                ", receiveDate=" + receiveDate +
                '}';
    }
}