package com.ly.adp.xapi.api.acc;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.ReactivePlusDataBase;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.ReactivePlusService;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.XapiReactiveRequestPage;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.XapiReactiveResponsePage;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;

import java.util.Map;

@ReactivePlusService("activity_adp")
public class ActivityReactiveService extends ReactivePlusDataBase {

    @Override
    public XapiReactiveRequestPage<Map<String, Object>> wrapParam(XapiInstance arg0, XapiMap<String, Object> arg1,
                                                                  XapiReactiveRequestPage<Map<String, Object>> arg2) {
        // TODO Auto-generated method stub
        return arg2;
    }

    @Override
    public XapiReactiveResponsePage<Object> wrapResult(XapiInstance arg0, XapiMap<String, Object> arg1,
                                                       XapiReactiveResponsePage<Object> arg2) {
        // TODO Auto-generated method stub
        return arg2;
    }

    @Override
    public XapiReactiveResponsePage<Object> queryData(XapiInstance instance, XapiMap<String, Object> context,
                                                      XapiReactiveRequestPage<Map<String, Object>> param) {

        return SwitchDbInvoke.invoke("base", () -> {

            return super.queryData(instance, context, param);
        });


    }


}
