package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 通用信息
 * <AUTHOR>
 * @Date 2024/7/3
 * @Version 1.0.0
 **/
public class Common implements Serializable {

    private static final long serialVersionUID = 4664557268428124263L;

    /**
     * 工牌SN号
     */
    @JsonProperty("sn")
    private String sn;

    /**
     * 用户账号
     */
    @JsonProperty("user_id")
    private String userId;

    /**
     * 绑定的用户姓名
     */
    @JsonProperty("user_name")
    private String userName;

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }
}
