package com.ly.adp.xapi.api.base.biz;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.ly.adp.common.ms.RabbitMQPool;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.Consumer;
import com.rabbitmq.client.DefaultConsumer;
import com.rabbitmq.client.Envelope;

@Component
public class demoEC implements ApplicationRunner{

	
	@Autowired
	RabbitMQPool rabbitMQPool;
	
	 @Resource
	    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage passiveServiceManage;

    @Resource
    WhiteListInterceptor whiteListInterceptor;
	
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
		
    
	public void getMQ() {
		rabbitMQPool.consumerTemplate(channel -> {
			
			String mq = "smart.queue.adp.demo.to.csp"; //mq为监听队列
			Consumer consumer = new DefaultConsumer(channel) {
				@Override
				public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
						byte[] body) throws IOException {
					String data = new String(body, "UTF-8");
					// 解析data即可 
					System.out.println("监听消息"+data);
					 data ="{\r\n" + 
							"    \"action\": \"A\", //action表示动作：A-新增，U-修改，D-删除，Q-查询\r\n" + 
							"    \"taskId\": \"1567890\", //保证唯一性，表示这一次交易的编号\r\n" + 
							"    \"content\":[{\r\n" + 
							"       \"retailNo\": \"1\",\r\n" + 
							"		\"smartId\": \"1\",\r\n" + 
							"		\"pno18\": \"1\",\r\n" + 
							"		\"buyerType\": \"1\",\r\n" + 
							"		\"buyerName\": \"1\",\r\n" + 
							"		\"buyerMobileNo\": \"1\",\r\n" + 
							"		\"ownerName\": \"1\",\r\n" + 
							"		\"idType\": \"1\",\r\n" + 
							"		\"idCardNo\": \"1\",\r\n" + 
							"		\"purchaseType\": \"1\",\r\n" + 
							"		\"expectedLicensedCityCode\": \"1\",\r\n" + 
							"		\"salesArea\": \"1\",\r\n" + 
							"		\"posCode\": \"1\",\r\n" + 
							"		\"salesName\": \"1\",\r\n" + 
							"		\"salesPhone\": \"1\",\r\n" + 
							"		\"expectedDeliveryCityCode\": \"1\",\r\n" + 
							"		\"deliveryDealer\": \"1\",\r\n" + 
							"		\"orderTime\": \"1\",\r\n" + 
							"		\"orderStatus\": \"1\",\r\n" + 
							"		\"orderModifiedTime\": \"1\",\r\n" + 
							"		\"orderSource\": \"1\",\r\n" + 
							"		\"orderChannel\": \"1\",\r\n" + 
							"		\"invoiceStatus\": \"1\",\r\n" + 
							"		\"invoiceTime\": \"1\",\r\n" + 
							"		\"vin\": \"1\",\r\n" + 
							"		\"msrp\": \"1\",\r\n" + 
							"		\"stateSubsidies\": \"1\",\r\n" + 
							"		\"discount\": \"1\",\r\n" + 
							"		\"applicableTaxRate\": \"1\",\r\n" + 
							"		\"orderAmount\": \"1\",\r\n" + 
							"		\"firstOrderType\": \"1\",\r\n" + 
							"		\"isTransformOrder\": \"1\",\r\n" + 
							"		\"transformOrderAmount\": \"1\",\r\n" + 
							"		\"intentionAmount\": \"1\",\r\n" + 
							"		\"depositAmount\": \"1\",\r\n" + 
							"		\"financePlan\": \"1\",\r\n" + 
							"		\"financePlanStatus\": \"1\",\r\n" + 
							"		\"deliveryName\": \"1\",\r\n" + 
							"		\"deliveryEmployee\": \"1\",\r\n" + 
							"		\"deliveryPhone\": \"1\",\r\n" + 
							"		\"paied_records\": [{\r\n" + 
							"			\"paiedStage\": \"1\",\r\n" + 
							"			\"paiedStatus\": \"1\",\r\n" + 
							"			\"paiedTime\": \"1\",\r\n" + 
							"			\"paiedMethod\": \"1\",\r\n" + 
							"			\"tradeNo\": \"1\",\r\n" + 
							"			\"chnlTradeNo\": \"1\",\r\n" + 
							"			\"paiedAmount\": \"1\"\r\n" + 
							"		}],\r\n" + 
							"		\"refund_records\": [{\r\n" + 
							"			\"refundStatus\": \"1\",\r\n" + 
							"			\"refundTime\": \"1\",\r\n" + 
							"			\"refundAmount\": \"1\",\r\n" + 
							"			\"chnlTradeNo\": \"1\",\r\n" + 
							"			\"srcChnlTradeNo\": \"1\"\r\n" + 
							"		}],\r\n" + 
							"		\"deliveryAllocateTime\": \"1\"}\r\n" + 
							"    ]\r\n" + 
							"}";
					data=data.replaceFirst("content", "body");	
					Map<String,Object> result = new HashMap<>();
					List<Map<String,Object>> dataList=new ArrayList<>();
			        result.put("message","");
			        result.put("isSuccess","N");
					String service = "ec_ifr_sale_order";
				    XapiInstance instance = xServiceHolder.getInstance(service);
				    // 判断是否有对应的配置
		            if(instance!=null){
		                AtomicReference<String> respStr = new AtomicReference<>();
		                passiveServiceManage.handle(instance, data, m -> respStr.set((String) m));
		                result = JSON.parseObject(respStr.get(),Map.class);
		            }else {
		                String msg = String.format("接口%s不存在",service);
		                logger.error(msg);
		                result.put("isSuccess","N");
		                result.put("message",msg);
		                dataList.add(result);
		            }
				}
			};
			
            
			channel.basicConsume(mq, true, consumer);
			
			
			
		});
		
		
	}

	@Override
	public void run(ApplicationArguments args) throws Exception {
		
		//getMQ();
		
	}

}
