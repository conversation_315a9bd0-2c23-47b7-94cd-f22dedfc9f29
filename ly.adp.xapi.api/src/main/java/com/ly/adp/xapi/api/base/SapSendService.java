package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ly.adp.xapi.api.base.biz.SapWebUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.bucn.component.xapi.util.StringUtil;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@SendService("sap_adp")
public class SapSendService extends SendDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(SapSendService.class);

    public Object convertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiTable mainTable = (XapiTable) context.getValue("mainTable");
        if (!StringHelper.IsEmptyOrNull(mainTable.getSubFlag())) {
            Map<String, Object> item = new HashMap<>();
            item.put("item", data.getDataEx());

            List<String> subFlag = (List) Arrays.asList(mainTable.getSubFlag().split("\\|"));
            for (String str : subFlag) {
                Map<String, Object> m = new HashMap<>();
                m.put(str, item);
                item = m;
            }
            return JSONObject.toJSONString(item);
        }
        if ("sap_adp_s_001".equals(instance.getLabel())) {
            HashMap<String, Object> urn = Maps.newHashMap();
            HashMap<String, Object> input = Maps.newHashMap();
            HashMap<String, Object> hashMap = Maps.newHashMap();
            List<Map<String, Object>> dataExList = data.getDataEx();
            for (Map<String, Object> dataEx : dataExList) {
                HashMap<Object, Object> newHashMap = Maps.newHashMap();
                newHashMap.put("item", dataEx.get("item"));
                dataEx.put("ITEM", newHashMap);
                dataEx.remove("item");
            }
            input.put("item", dataExList);
            urn.put("INPUT", input);
            hashMap.put("urn:ZIF_ADP_142", urn);
            return JSONObject.toJSONString(hashMap);
        }
        return JSONObject.toJSONString(data.getDataEx());
    }

    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object param) throws Exception {
        return SapWebUtil.postData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getService(), xapiInstance.getSystem().getProvideAccount(), xapiInstance.getSystem().getProvidePassword(), param);
    }

    @Override
    public XapiResult<String> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object data) {
        XapiResult<String> result = XapiResult.creat();
        try {
            String respJson = String.valueOf(data);
            @SuppressWarnings("unchecked")
            Map<String, Object> resp = JSONObject.parseObject(respJson, Map.class);
            if ("sap_adp_s_001".equals(xapiInstance.getLabel())) {
                if (resp.containsKey("RETURN")) {
                    Map<String, Object> resp1 = (Map<String, Object>) resp.get("RETURN");
                    if (resp1.containsKey("item")) {
                        List<Map<String, Object>> list = (List<Map<String, Object>>) resp1.get("item");
                        if ("S".equals(String.valueOf(list.get(0).get("RESULT")))) {
                            return result;
                        } else {
                            result.setMessage(String.valueOf(list.get(0).get("MESSAGE")));
                        }
                    }
                }

            }
            if (resp.containsKey("E_RETURN")) {
                Map<String, Object> resp1 = (Map<String, Object>) resp.get("E_RETURN");
                if (resp1.containsKey("item")) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>) resp1.get("item");
                    if ("S".equals(String.valueOf(list.get(0).get("RESULT")))) {
                        return result;
                    } else {
                        result.setMessage(String.valueOf(list.get(0).get("MESSAGE")));
                    }
                }
            }
            result.setStatus(1);
        } catch (Exception e) {
            log.error("sapSendService解析反馈报文：", e);
            result.setStatus(1);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
