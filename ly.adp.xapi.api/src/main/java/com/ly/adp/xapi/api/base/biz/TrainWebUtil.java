package com.ly.adp.xapi.api.base.biz;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.mp.component.helper.StringHelper;

import net.sf.json.JSONSerializer;

import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/12/14 14:54
 */
public class TrainWebUtil {
    private static final Logger log = LoggerFactory.getLogger(TrainWebUtil.class);

    public static String getData(String url, Map<String, String> param) {
        String endUrl = null;
        if (!StringHelper.IsEmptyOrNull(param)) {
            endUrl = url + "?" + formatKv(param, true);
        }
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(endUrl);
            HttpHeaders httpHeaders = new HttpHeaders();

            httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
            // 请根据需要，对应选择response泛型
            responseEntity = getRestTemplate().exchange(uri, HttpMethod.POST, entity, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("获取培训系统人员信息接口异常", e);
            throw new RuntimeException(e);
        }
        return responseEntity.getBody();
    }

    public static String pullData(String url, Map<String, String> head, Map<String, Object> param) {

        JSONObject json = new JSONObject();
        JSONObject result = new JSONObject();
        List<Map<String, Object>> LIST = (ArrayList<Map<String, Object>>) param.get("organizePositionList");

        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(LIST));
        json.put("corpCode", param.get("corpCode"));
        json.put("organizePositionList", jsonArray);
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);

            HttpHeaders httpHeaders = new HttpHeaders();
            //httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Object> entity = new HttpEntity<>(json, httpHeaders);

            // 请根据需要，对应选择response泛型
            //responseEntity = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class);
            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, entity, String.class);

        } catch (URISyntaxException e) {
            // TODO Auto-generated catch block
            log.error("获取岗位编制人数失败", e);
            throw new RuntimeException(e);
        } catch (RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("获取岗位编制人数失败", e);
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();

    }

    public static String postData(String url, Object param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);

            HttpHeaders httpHeaders = new HttpHeaders();
            //httpHeaders.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Object> entity = new HttpEntity<>(param, httpHeaders);

            // 请根据需要，对应选择response泛型
            //responseEntity = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class);
            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, entity, String.class);

        } catch (URISyntaxException e) {
            // TODO Auto-generated catch block
            log.error("获取岗位编制人数失败", e);
            throw new RuntimeException(e);
        } catch (RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("获取岗位编制人数失败", e);
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }

    public static String formatKv(Map<String, String> messageData, boolean urlEncode) {
        return formatKv(messageData, "&", "=", false, "UTF-8", false);
    }

    public static String formatKv(Map<String, String> messageData, String joiner, String linker,
                                  boolean urlEncode, String encoding, boolean onlyValue) {

        Map<String, String> messageDataSort = new TreeMap<>(messageData);

        StringBuilder message = new StringBuilder();
        for (Map.Entry<String, String> entry : messageDataSort.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            log.debug("add kv data {}:{}", name, value);

            if (urlEncode) {
                try {
                    name = URLEncoder.encode(entry.getKey(), encoding);
                    value = URLEncoder.encode(entry.getValue(), encoding);
                } catch (UnsupportedEncodingException e) {
                    throw new IllegalArgumentException("url encode failed, encoding is unsupported");
                }
                log.debug("add encoded kv data {}:{}", name, value);
            }

            if (onlyValue) {
                message.append(value).append(joiner);
            } else {

                message.append(name).append(linker).append(value).append(joiner);
            }

        }

        if (message.length() > 0 && joiner.length() > 0) {
            message.delete(message.length() - joiner.length(), message.length());
        }

        return message.toString();
    }


}
