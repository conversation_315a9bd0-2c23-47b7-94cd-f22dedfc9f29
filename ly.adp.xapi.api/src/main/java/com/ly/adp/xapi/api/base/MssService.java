package com.ly.adp.xapi.api.base;

import com.ly.adp.xapi.api.base.biz.MssBiz;
import com.ly.bucn.component.xapi.service.normal.NormalDataGenericBase;
import com.ly.bucn.component.xapi.service.normal.NormalFunctionGeneric;
import com.ly.bucn.component.xapi.service.normal.NormalService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * 中台接口对接 Normal
 */
@NormalService("MSS_ADP")
public class MssService extends NormalDataGenericBase{

	@Autowired
	private MssBiz mssBiz;
	
	 public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
	        handlers.put("sendVerificationCode", mssBiz::sendVerificationCode);
	}
}
