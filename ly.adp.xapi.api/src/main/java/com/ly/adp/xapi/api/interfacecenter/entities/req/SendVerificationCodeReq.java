package com.ly.adp.xapi.api.interfacecenter.entities.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/1/7
 * @Version 1.0.0
 **/
@ApiModel("通过邮箱发送验证码入参-req")
public class SendVerificationCodeReq implements Serializable {

    private static final long serialVersionUID = 2307559701205826005L;

    @ApiModelProperty(value = "邮箱", notes = "通过aes加密后传入", required = true)
    private String email;

    @ApiModelProperty(value = "用户名", required = true)
    private String userName;

    public SendVerificationCodeReq() {
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return "SendVerificationCodeReq{" +
                "email='" + email + '\'' +
                ", userName='" + userName + '\'' +
                '}';
    }
}
