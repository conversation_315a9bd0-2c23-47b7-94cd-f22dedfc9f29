package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/10
 */
public class EcStockVehicleCodeRequest implements Serializable {

    private static final long serialVersionUID = -4903536289529926304L;

    @ApiModelProperty("二维码CODE")
    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String toString() {
        return "EcStockVehicleCodeRequest{" +
                "code='" + code + '\'' +
                '}';
    }
}
