package com.ly.adp.xapi.api.base;

import com.ly.bucn.component.xapi.service.normal.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;


@NormalService("zt_adp")
public class ZtService extends NormalDataGenericBase {
    private static final Logger log = LoggerFactory.getLogger(ZtService.class);

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> map) {

    }
}
