package com.ly.adp.xapi.api.interfacecenter.services.impl;

import com.ly.adp.common.entity.Result;
import com.ly.adp.xapi.api.interfacecenter.entities.dto.TrainResultDto;
import com.ly.adp.xapi.api.interfacecenter.mapper.TrainResultMapper;
import com.ly.adp.xapi.api.interfacecenter.services.ITrainNormalService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
public class TrainNormalService implements ITrainNormalService {

    private static final Logger log = LoggerFactory.getLogger(TrainNormalService.class);

    @Resource
    private TrainResultMapper trainResultMapper;

    @Override
    public Result insertTrainResult(TrainResultDto trainResultDto) {
        Result validateResult = trainResultDto.validate();
        if (!validateResult.isSuccess()) {
            return validateResult;
        }
        trainResultMapper.insert(trainResultDto.convertToEntity());
        return Result.Builder.success();
    }
}
