package com.ly.adp.xapi.api.baseEntity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;
@ApiModel("外饰主数据接口返回")
public class ExteriorsResponseEntity implements Serializable {

    /*响应码*/
    private int code;
    /*响应信息*/
    private String msg;
    /*响应数据*/
    private List<Map<String, Object>> data;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<Map<String, Object>> getData() {
        return data;
    }

    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }
}
