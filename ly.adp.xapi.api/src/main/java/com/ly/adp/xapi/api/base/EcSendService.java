package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.EcWebUtil;
import com.ly.adp.xapi.api.base.biz.SapWebUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.bucn.component.xapi.util.StringUtil;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.HashMap;
import java.util.Map;

@SendService("ec_adp")
public class EcSendService extends SendDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(EcSendService.class);

    @Override
    public Object convertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        if ("M".equals(instance.getMoudle())) {
            return JSONObject.toJSONString(data.getDataEx().get(0));
        } else {
            return JSONObject.toJSONString(data.getDataEx());
        }
    }

    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> context, Object data) throws Exception {

        String token = "";
        //获取token
        if (!context.containsKey("access_token")) {
            MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
            params.add("client_id", xapiInstance.getSystem().getProvideAccount());
            params.add("client_secret", xapiInstance.getSystem().getProvidePassword());
            params.add("grant_type", "client_credentials");
            String tokenJ = EcWebUtil.postData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getSystem().getXapiKv("token_url").getValueTee(), MediaType.APPLICATION_FORM_URLENCODED, null, params);

            Map<String, Object> tokenM = (Map<String, Object>) JSONObject.parse(tokenJ);
            if (!tokenM.containsKey("access_token") || StringHelper.IsEmptyOrNull(tokenM.get("access_token"))) {
                throw new Exception("账号密码错误，获取token失败");
            }
            token = String.valueOf(tokenM.get("access_token"));
            context.put("access_token", token);
        } else {
            token = String.valueOf(context.get("access_token"));
        }

        String json = String.valueOf(data);
        //发送数据
        Map<String, String> hear = new HashMap<>();
        hear.put("Authorization", "Bearer " + token);
        hear.put("X-Channel-Id", xapiInstance.getSystem().getXapiKv("X-Channel-Id").getValueTee());
        return EcWebUtil.postData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getService(), MediaType.APPLICATION_JSON, hear, json);
    }

    @Override
    public XapiResult<String> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object data) {
        XapiResult<String> result = XapiResult.creat();
        try {
            String resp = String.valueOf(data);
            @SuppressWarnings("unchecked")
            Map<String, Object> respJson = JSONObject.parseObject(resp, Map.class);
            if (respJson.containsKey("code") && "0".equals(String.valueOf(respJson.get("code")))) {
                return result;
            } else if (respJson.containsKey("msg")) {
                result.setStatus(1);
                result.setMessage(StringUtil.subStr(String.valueOf(respJson.get("msg")), 200));
            } else {
                result.setStatus(1);
                result.setMessage("no resp");
            }
        } catch (Exception e) {
            log.error("EcSendService解析反馈报文：", e);
            result.setStatus(1);
            result.setMessage(e.getMessage());
        }
        return result;
    }

//    public static void main(String[] args) {
////        MultiValueMap<String, String> params= new LinkedMultiValueMap<String, String>();
////        params.add("client_id", "ADP");
////        params.add("client_secret", "0cc836e2-3811-4e25-821b-ae25f1c3bb56");
////        params.add("grant_type", "client_credentials");
////        System.out.println(EcWebUtil.postData("https://eco-sit-api.smartchina.com.cn/auth/token", MediaType.APPLICATION_FORM_URLENCODED,null, params));
//
//        String str = "[{\"saleOrderCode\":\"1\",\"saleName\":\"1\",\"saleDlrId\":\"1\",\"salePhone\":\"1\",\"saleCityCode\":\"2\",\"saleDlrShortName\":\"1\"}]";
//        System.out.println(str.replaceFirst("\\[", ""));
//        System.out.println(str.replaceFirst("\\[", "").substring(0, str.length() - 2));
//    }
}
