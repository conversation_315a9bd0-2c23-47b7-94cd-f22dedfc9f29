package com.ly.adp.xapi.api.acc;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.netflix.client.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.SocketException;
import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/11/20
 */
public class RequestClient {

    private static final Logger logger = LoggerFactory.getLogger(RequestClient.class);
    @SuppressWarnings("unchecked")
    private static List<Class<? extends Throwable>> connectionRelated =
            Lists.<Class<? extends Throwable>>newArrayList(SocketException.class, SocketTimeoutException.class);
    private static int MAX_RETRY_COUNT = 2;
    private static RestTemplate restTemplate;

    static {
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
        requestFactory.setConnectTimeout(10 * 1000);
        requestFactory.setReadTimeout(20 * 1000);
        restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
    }

    /**
     * get content
     *
     * @param url
     * @param headers
     * @return
     */
    public static RspEntity get(String url, HttpHeaders headers) {
        return get(url, null, headers);
    }

    /**
     * get content
     *
     * @param url
     * @param params
     * @param headers
     * @return
     */
    public static RspEntity get(String url, Map<String, Object> params, HttpHeaders headers) {
        return get(url, params, headers, 0);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    private static RspEntity get(String url, Map<String, Object> params, HttpHeaders headers, int retryCount) {
        HttpEntity entity = new HttpEntity(headers);
        String tmpUrl = url;
        if (params != null && !params.isEmpty()) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            params.forEach(builder::queryParam);
            url = builder.build().encode().toUriString();
        }

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            return new RspEntity(response.getBody());
        } catch (ErrorResponseException e) {
            logger.warn("Failed to post data, url:{}", url, e);
            throw e;
        } catch (Exception e) {
            // 超时重试逻辑
            if (Utils.isPresentAsCause(e, connectionRelated) && retryCount++ < MAX_RETRY_COUNT) {
                logger.warn("time out retry, url:{}", url, e);
                return get(tmpUrl, params, headers, retryCount);
            }
            logger.warn("Failed to get content, url:{}", url, e);
            throw e;
        }
    }

    /**
     * post content
     *
     * @param url
     * @param params
     * @param headers
     * @return
     */
    public static RspEntity post(String url, Map params, HttpHeaders headers) {
        try {
            HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(params), headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            return new RspEntity(response.getBody());
        } catch (ErrorResponseException e) {
            logger.warn("Failed to post data, url:{}", url, e);
            throw e;
        } catch (Exception e) {
            // params里面可能包含密码，因此不打印params的log
            logger.warn("Failed to get content, url:{}", url, e);
            throw e;
        }
    }

    /**
     * 提交form表单
     *
     * @param url
     * @param params
     * @param headers
     * @return
     */
    public static RspEntity postForm(String url, MultiValueMap<String, Object> params, HttpHeaders headers) {
        try {
            HttpEntity<MultiValueMap<String, Object>> r = new HttpEntity<>(params, headers);
            return new RspEntity(restTemplate.postForObject(url, r, String.class));
        } catch (ErrorResponseException e) {
            logger.warn("Failed to post data, url:{}", url, e);
            throw e;
        } catch (Exception e) {
            // params里面可能包含密码，因此不打印params的log
            logger.warn("Failed to get content, url:{}", url, e);
            throw e;
        }
    }

    /**
     * post plain text
     *
     * @param url
     * @param content
     * @param headers
     * @return
     */
    public static RspEntity post(String url, String content, HttpHeaders headers) {
        try {

            HttpEntity<String> entity = new HttpEntity<>(content, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            return new RspEntity(response.getBody());
        } catch (ErrorResponseException e) {
            logger.warn("Failed to post data, url:{}", url, e);
            throw e;
        } catch (Exception e) {
            // params里面可能包含密码，因此不打印params的log
            logger.warn("Failed to get content, url:{}", url, e);
            throw e;
        }
    }

    /**
     * put
     *
     * @param url
     * @param params
     * @param headers
     * @return
     */
    public static RspEntity put(String url, Map params, HttpHeaders headers) {
        try {
            HttpEntity<String> entity = new HttpEntity<>(JSONObject.toJSONString(params), headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
            return new RspEntity(response.getBody());
        } catch (ErrorResponseException e) {
            logger.warn("Failed to put data, url:{}", url, e);
            throw e;
        } catch (Exception e) {
            // params里面可能包含密码，因此不打印params的log
            logger.warn("Failed to get content, url:{}", url, e);
            throw e;
        }
    }

    /**
     * delete
     *
     * @param url
     * @param headers
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static RspEntity delete(String url, Map<String, String> params, HttpHeaders headers) {
        HttpEntity entity = new HttpEntity(headers);

        if (params != null && !params.isEmpty()) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
            params.forEach(builder::queryParam);
            url = builder.build().encode().toUriString();
        }

        try {
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);
            return new RspEntity(response.getBody());
        } catch (ErrorResponseException e) {
            logger.warn("Failed to post data, url:{}", url, e);
            throw e;
        } catch (Exception e) {
            logger.warn("Failed to get content, url:{}", url, e);
            throw e;
        }
    }
}
