package com.ly.adp.xapi.api.base.biz;

import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Service
public class CdpBiz {
    private static final Logger log = LoggerFactory.getLogger(CdpBiz.class);

    private static final Base64.Encoder encoder = Base64.getEncoder();

    public static String getToken(XapiInstance xapiInstance) throws Exception {
        String text = xapiInstance.getSystem().getProvideAccount() + ":" + xapiInstance.getSystem().getProvidePassword();
        byte[] textByte = text.getBytes("UTF-8");
        String encodedText = encoder.encodeToString(textByte);

        Map<String, String> tokenHead = new HashMap<>();
        tokenHead.put("Authorization", "Basic " + encodedText);
        MultiValueMap<String, String> tokenBody = new LinkedMultiValueMap<>();
        tokenBody.add("grant_type", "client_credentials");

        String tokenJ = CdpWebUtil.postData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getSystem().getXapiKv("token_url").getValueTee(), MediaType.APPLICATION_FORM_URLENCODED, tokenHead, tokenBody);
        Map<String, Object> tokenM = JSONObject.parseObject(tokenJ, Map.class);
        if (!tokenM.containsKey("access_token") || StringHelper.IsEmptyOrNull(tokenM.get("access_token"))) {

            throw new Exception("账号密码错误，获取token失败");

        }
        return String.valueOf(tokenM.get("access_token"));
    }
}
