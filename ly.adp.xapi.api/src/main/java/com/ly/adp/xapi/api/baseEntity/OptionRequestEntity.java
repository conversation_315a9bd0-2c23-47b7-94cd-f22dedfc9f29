package com.ly.adp.xapi.api.baseEntity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;

@ApiModel("获取选装数据接口请求参数")
public class OptionRequestEntity implements Serializable {
	  private static final long serialVersionUID = 1L;

	    /*平台*/
	    private String  platForm;
	    /*开始变更时间*/
	    private String  updateB;
	    /*开始变更时间*/
	    private String  updateD;

	    public String getPlatForm() {
	        return platForm;
	    }

	    public void setPlatForm(String platForm) {
	        this.platForm = platForm;
	    }

	    public String getUpdateB() {
	        return updateB;
	    }

	    public void setUpdateB(String updateB) {
	        this.updateB = updateB;
	    }

	    public String getUpdateD() {
	        return updateD;
	    }

	    public void setUpdateD(String updateD) {
	        this.updateD = updateD;
	    }
}
