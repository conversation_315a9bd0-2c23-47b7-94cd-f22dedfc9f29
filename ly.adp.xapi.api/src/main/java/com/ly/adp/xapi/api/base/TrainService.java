package com.ly.adp.xapi.api.base;

import com.ly.adp.xapi.api.base.biz.TrainBiz;
import com.ly.bucn.component.xapi.service.normal.NormalDataGenericBase;
import com.ly.bucn.component.xapi.service.normal.NormalFunctionGeneric;
import com.ly.bucn.component.xapi.service.normal.NormalService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2021/12/13 17:43
 */
@NormalService("TRAIN_ADP")
public class TrainService extends NormalDataGenericBase {

    @Autowired
    TrainBiz trainBiz;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
        handlers.put("findUser", trainBiz::findUser);
        handlers.put("getUserNum", trainBiz::getUserNum);

    }
}
