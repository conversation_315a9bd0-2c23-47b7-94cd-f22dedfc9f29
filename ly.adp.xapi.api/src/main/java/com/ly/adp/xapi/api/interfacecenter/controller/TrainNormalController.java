package com.ly.adp.xapi.api.interfacecenter.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.adp.common.entity.Result;
import com.ly.adp.xapi.api.boot.TdaNormalController;
import com.ly.adp.xapi.api.interfacecenter.entities.dto.TrainResultDto;
import com.ly.adp.xapi.api.interfacecenter.services.ITrainNormalService;
import com.ly.adp.xapi.api.util.XapiLogUtil;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.mp.busicen.common.context.BusicenException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "TRAIN_ADP自定义接口")
@RestController
@RequestMapping("/noauth/rest/TRAIN_ADP")
public class TrainNormalController {

    private static final Logger log = LoggerFactory.getLogger(TdaNormalController.class);

    @Autowired
    private ITrainNormalService trainNormalService;

    @Autowired
    private IXapiLogBiz xapiLogBiz;

    @ApiOperation("培训结果保存")
    @PostMapping("/insertTrainResult")
    public Result insertTrainResult(@RequestBody TrainResultDto trainResultDto) {
        Result result = null;
        String originalJson = "";
        try {
            // 记录原始的下划线入参
            ObjectMapper objectMapper = new ObjectMapper();
            originalJson = objectMapper.writeValueAsString(trainResultDto);
            result = trainNormalService.insertTrainResult(trainResultDto);
        } catch (BusicenException e) {
            result = Result.Builder
                    .failure(e.getMessage());
        } catch (Exception e) {
            log.error("培训结果保存接口异常", e);
            result = Result.Builder
                    .failure("培训结果保存调用失败");
        } finally {
            // 记录xapi日志
            XapiLogUtil.addSuccessXapiLog(xapiLogBiz, "insertTrainResult", originalJson, JSON.toJSONString(result));
            return result;
        }
    }
}
