package com.ly.adp.xapi.api.otd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface OtdResMapper extends BaseMapper {

    List<Map<String, Object>> selectStockCarCount(@Param("param") Map<String, Object> paramMap);

    int updateStockCar(@Param("param") Map<String, Object> stockCarMap);

    int indertStockCar(@Param("param") Map<String, Object> stockCarMap);

    int selectDetailCount(@Param("param") Map<String, Object> scopeMap);

    int deleteDetail(@Param("param") Map<String, Object> scopeMap);

    int insertDetail(@Param("param") Map<String, Object> scopeMap);

    List<String> selectDlrCodeList(@Param("param") Map<String, Object> stockCarMap);

    int insertCdpAdpPhoneUpdate(@Param("list") List<Map<String, Object>> list);

    Map<String, Object> selectOrderIsExists(@Param("param") Map<String, Object> map);

    int updateOrderByRevocation(@Param("param") Map<String, Object> map);

    Map<String, String> findSalesRange(Map<String, Object> paramMap);

    List<String> findNationwideDlr();

    int insertNationwideDetail(@Param("param") Map<String, Object> scopeMap, @Param("list") List<String> nationwideDlr);

    int insertDlrDetail(@Param("param") Map<String, Object> scopeMap, @Param("salesRange") String salesRange);

    List<String> findRegionDlr(@Param("region") String region);

    String findRegion(Map<String, Object> scopeMap);

    List<String> findAgent(Map<String, Object> scopeMap);

    Map<String, String> findDCRange(Map<String, Object> paramMap);

    int updateOrderToTradeinright(Map<String, Object> map);

    int updateStockVehicle(Map<String, Object> map);

    Map<String, Object> findStockVehicle(Map<String, Object> map);

    Map<String, String> findRange(@Param("param") Map<String, Object> manualSetting, @Param("storeHouse") Object storeHouse);

    List<String> selecDlrCodeList(@Param("param") Map<String, Object> map);

    String findEcRegion(Map<String, Object> map);

    List<String> findecAgent(Map<String, Object> map);

    String findecUrl();

    Map<String, String> findEcDCRange(@Param("param") Map<String, Object> map, @Param("pno18") Object pno18, @Param("vehicleType") Object vehicleType);

    int queryVinExists(@Param("vin") String vin);

    List<String> queryAgentCityDlr(@Param("storeHouse") String storeHouse);
}
