package com.ly.adp.xapi.api.tda.feign;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @<PERSON> ly-zhouxin
 * @Date 2024/7/8
 * @Version 1.0.0
 **/
@FeignClient(name = "TdaFeign"
        , url = "${refer.tda.url}")
public interface TdaFeign {
    @ApiOperation(value = "获取tda用户信息", notes = "获取tda用户信息")
    @GetMapping("/Sca/Saas/Users")
    TdaResponse userInfo(@RequestHeader("X-JWT-TOKEN") String token,
                            @RequestParam("menu") String menu,
                            @RequestParam("offset") int offset,
                            @RequestParam("page") int page,
                            @RequestParam("sn") String sn);
}
