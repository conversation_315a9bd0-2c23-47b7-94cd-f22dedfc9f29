package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 修改配置事件
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class ModifyConfig implements Serializable {
    
    private static final long serialVersionUID = 979277817872173128L;

    /**
     * 时间戳
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 配置名称
     */
    @JsonProperty("config_name")
    private String configName;

    /**
     * 修改结果
     */
    @JsonProperty("resulf")
    private long result;

    /**
     * 旧值
     */
    @JsonProperty("old_value")
    private long oldValue;

    /**
     * 新值
     */
    @JsonProperty("new_value")
    private long newValue;

    /**
     * 时间戳格式化字符串
     */
    @JsonProperty("ts_format")
    private String tsFormat;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public long getResult() {
        return result;
    }

    public void setResult(long result) {
        this.result = result;
    }

    public long getOldValue() {
        return oldValue;
    }

    public void setOldValue(long oldValue) {
        this.oldValue = oldValue;
    }

    public long getNewValue() {
        return newValue;
    }

    public void setNewValue(long newValue) {
        this.newValue = newValue;
    }

    public String getTsFormat() {
        return tsFormat;
    }

    public void setTsFormat(String tsFormat) {
        this.tsFormat = tsFormat;
    }
}
