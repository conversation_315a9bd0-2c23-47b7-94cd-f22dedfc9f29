package com.ly.adp.xapi.api.tda.receive.impl;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.tda.mapper.TdaReceive002Mapper;
import com.ly.adp.xapi.api.tda.receive.TdaReceive002;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.mp.component.helper.StringHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/9
 */
@Service
public class TdaReceive002Impl implements TdaReceive002 {


    final TdaReceive002Mapper tdaReceive002Mapper;

    public TdaReceive002Impl(TdaReceive002Mapper tdaReceive002Mapper) {
        this.tdaReceive002Mapper = tdaReceive002Mapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void tdaReceiveModel(XapiInstance instance, XapiMap<String, Object> context, Map<String, Object> mainData, XapiResult.XapiResultData<Object> resultData) {

        if (StringHelper.IsEmptyOrNull(mainData.get("custId"))) {
            resultData.setSuccess(false);
            resultData.setMessage("缺失客户ID");
            return;
        }

        if (StringHelper.IsEmptyOrNull(mainData.get("testDriveCode"))) {
            resultData.setSuccess(false);
            resultData.setMessage("缺失试驾单号");
            return;
        }

        if (StringHelper.IsEmptyOrNull(mainData.get("record_id"))) {
            resultData.setSuccess(false);
            resultData.setMessage("缺失录音ID");
            return;

        }
        Map<String, Object> custInfo = tdaReceive002Mapper.findOnecustInfo(mainData);
        if (StringHelper.IsEmptyOrNull(custInfo)) {

            resultData.setSuccess(false);
            resultData.setMessage("客户信息不存在");
            return;
        }
        int i = tdaReceive002Mapper.updateDriveSheet(mainData);
        if (StringHelper.IsEmptyOrNull(custInfo.get("extendJson"))) {
            custInfo.put("extendJson", JSONObject.toJSONString(mainData));
        } else {
            String extendJsons = custInfo.get("extendJson").toString();
            Map extendJson = JSONObject.parseObject(extendJsons, Map.class);
            extendJson.putAll(mainData);
            custInfo.put("extendsJson", JSONObject.toJSONString(extendJson));

        }
        i = tdaReceive002Mapper.updateOnecustInfo(custInfo);
        resultData.setSuccess(true);
        resultData.setMessage("接收成功");

    }
}
