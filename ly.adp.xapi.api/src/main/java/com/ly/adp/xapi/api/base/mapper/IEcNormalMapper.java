package com.ly.adp.xapi.api.base.mapper;

import com.ly.adp.xapi.api.baseEntity.*;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/4/3
 */
public interface IEcNormalMapper {
    EcStockVehicleBo ecStockVehicleQuery(EcStockVehicleRequest ecStockVehicleQuery);

    List<EcStockVehicleResponse> otdStockVehicleQuery(EcStockVehicleRequest ecStockVehicleQuery);

    BigDecimal findPolicyDetail(EcStockVehicleBo ecStockVehicle);

    EcStockVehicleBo findStockVehicle(EcstockVehicleUpdateRequest updateRequest);

    int updateStockVehicle(EcstockVehicleUpdateRequest updateRequest);

    EcStockVehicleCodeBo ecStockVehicleCodeQuery(EcStockVehicleCodeRequest ecStockVehicleCode);

    BigDecimal findPolicyDetailCode(EcStockVehicleCodeBo ecStockVehicle);

    BigDecimal findPno18Amount(EcStockVehicleCodeResponse ecStockVehicleResponse);

    BigDecimal findPno18(EcStockVehicleResponse ecStockVehicleResponse);

    EcStockVehicleBo findPno18Policy(EcStockVehicleRequest ecStockVehicleQuery);

    EcStockVehicleCodeBo ecStockVehicleCodePno18(EcStockVehicleCodeRequest ecStockVehicleCode);

    EcStockVehicleCodeBo queryecStockVehicleCode(@Param("code") String code);

    /**
     * 根据vin查EcStockVehicleBo
     * @param vin
     * @return
     */
    EcStockVehicleBo ecStockVehicleQueryByVin(@Param("vin") String vin);

}
