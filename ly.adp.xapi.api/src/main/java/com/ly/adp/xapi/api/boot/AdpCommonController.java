package com.ly.adp.xapi.api.boot;

import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiKv;
import com.ly.bucn.component.xapi.kernal.config.SignInterceptor;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.config.token.AuthInterceptor;
import com.ly.bucn.component.xapi.kernal.config.token.TokenUtils;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.kernal.model.XapiResponse;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.receive.ReceiveController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Api(tags = "*XAPI-EX", value = "reactive", description = "数据响应-")
@Controller
@RequestMapping("/xapi/ex/reactive")
public class AdpCommonController {

    private Logger logger = LoggerFactory.getLogger(ReceiveController.class);

    @Resource
    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage<String, String> passiveServiceManage;

    @Resource
    WhiteListInterceptor whiteListInterceptor;

    @Autowired
    TokenUtils tokenUtils;
    @ResponseBody
    @RequestMapping(value = {"/token"},method = {RequestMethod.GET}
    )
    public Object getToken(String name, String passwd) {
        Map<String, Object> result = new HashMap();
        XapiInstance instance = xServiceHolder.getInstance("adp_common");
        Optional<XapiKv> optionalXapiKv = instance.getSystem().getKvs()
                .stream().filter(n -> n.getIdentity() != null && n.getIdentity()
                        .startsWith("auth.")).filter(n -> name.equals(n.getValueTee()) && "name".equals(n.getKeyTee())).findFirst();
        if (optionalXapiKv.isPresent()) {
            XapiKv xapiKvName = optionalXapiKv.get();
            Optional<XapiKv> xapiKvPass = instance.getSystem().getKvs()
                    .stream().filter(n -> xapiKvName.getIdentity().equals(n.getIdentity()) && "passwd".equals(n.getKeyTee()))
                    .filter(n -> passwd.equals(n.getValueTee())).findFirst();
            if (xapiKvPass.isPresent()) {
                result.put("code", "0");
                result.put("msg", "success");
                result.put("data", this.tokenUtils.getToken(name));
                return result;
            }
        }
        result.put("code", "-1");
        result.put("msg", "用户不存在或者密码错误");
        result.put("data", null);
        return result;
    }

    @ApiOperation("rest")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "system", value = "系统编码", required = false, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "service", value = "接口编码", required = false, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "data", value = "数据", required = false, dataType = "String", paramType = "body")}
    )
    @RequestMapping(value = "/{system}/{service}", method = RequestMethod.POST)
    public void normal(HttpServletRequest request, HttpServletResponse response, @PathVariable("system") String system, @PathVariable("service") String service, String token) throws Exception {
        AtomicReference<String> respStr = new AtomicReference<>();

        try {
            XapiResponse xapiResponse = new XapiResponse();
            int flag = this.tokenUtils.checkToken(token);
            if (flag != 0) {
                if (flag == 1) {
                    xapiResponse.setCode("-2");
                    xapiResponse.setMsg("token超时");
                } else {
                    xapiResponse.setCode("-1");
                    xapiResponse.setMsg("token异常");
                }

                response.setCharacterEncoding("UTF-8");
                response.setContentType("application/json; charset=utf-8");
                response.getWriter().print(JSONObject.toJSON(xapiResponse));
                response.getWriter().flush();
                response.getWriter().close();
                return;
            }

            XapiInstance instance = xServiceHolder.getInstance(service);

            try (BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"))) {
                StringBuilder responseStrBuilder = new StringBuilder();
                String inputStr;
                while ((inputStr = streamReader.readLine()) != null)
                    responseStrBuilder.append(inputStr);
                String data = responseStrBuilder.toString();
                passiveServiceManage.handle(instance, data, m -> respStr.set(m));
            } catch (Exception e) {
                logger.error("{}-{} 执行异常", system, service, e);
            }

            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            String errResp = String.format("[%s]执行异常,请联系接口负责人", instance.getLabel());
            response.getWriter().print(respStr.get() == null ? errResp : respStr.get());
            response.getWriter().flush();
            response.getWriter().close();
        } catch (Exception e) {
            logger.error("{}-{} 执行异常", system, service, e);
            response.setCharacterEncoding("UTF-8");

            response.getWriter().print(e.getMessage());
            response.getWriter().flush();
            response.getWriter().close();
        } finally {

        }

    }
}
