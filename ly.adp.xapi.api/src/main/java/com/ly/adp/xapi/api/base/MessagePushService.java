package com.ly.adp.xapi.api.base;

import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;

import com.ly.adp.xapi.api.base.biz.MessagePushBiz;
import com.ly.bucn.component.xapi.service.normal.NormalDataGenericBase;
import com.ly.bucn.component.xapi.service.normal.NormalFunctionGeneric;
import com.ly.bucn.component.xapi.service.normal.NormalService;
@NormalService("SMS_ADP")
public class MessagePushService extends NormalDataGenericBase{
	@Autowired
	MessagePushBiz messagePushBiz;
	
	 public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
	        handlers.put("sendMessage", messagePushBiz::sendMessage);
	    }
}
