package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 时间记录
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class DateRecord implements Serializable {

    private static final long serialVersionUID = 2268389897945778962L;

    /**
     * 服务器时间戳
     */
    @JsonProperty("server")
    private long server;

    /**
     * 本地时间戳
     */
    @JsonProperty("local")
    private long local;

    /**
     * 时间差
     */
    @JsonProperty("diff")
    private long diff;

    public long getServer() {
        return server;
    }

    public void setServer(long server) {
        this.server = server;
    }

    public long getLocal() {
        return local;
    }

    public void setLocal(long local) {
        this.local = local;
    }

    public long getDiff() {
        return diff;
    }

    public void setDiff(long diff) {
        this.diff = diff;
    }
}
