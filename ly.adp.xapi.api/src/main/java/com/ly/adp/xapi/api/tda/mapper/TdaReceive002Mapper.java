package com.ly.adp.xapi.api.tda.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/9
 */
public interface TdaReceive002Mapper {

    Map<String, Object> findOnecustInfo(Map<String, Object> mainData);

    int updateOnecustInfo(@Param("param") Map<String, Object> custInfo);

    int updateDriveSheet(Map<String, Object> mainData);
}
