package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 工牌信息DTO
 * <AUTHOR>
 * @Date 2024/7/3
 * @Version 1.0.0
 **/
public class IdBargeInfoDto implements Serializable {

    private static final long serialVersionUID = 6723279667837782540L;

    /**
     * 通用信息
     */
    @JsonProperty("common")
    private Common common;

    /**
     * 设备信息
     */
    @JsonProperty("dev_info")
    private DevInfo devInfo;

    /**
     * ota 版本信息
     */
    @JsonProperty("ota_info")
    private List<OtaInfo> otaInfo;

    public Common getCommon() {
        return common;
    }

    public void setCommon(Common common) {
        this.common = common;
    }

    public DevInfo getDevInfo() {
        return devInfo;
    }

    public void setDevInfo(DevInfo devInfo) {
        this.devInfo = devInfo;
    }

    public List<OtaInfo> getOtaInfo() {
        return otaInfo;
    }

    public void setOtaInfo(List<OtaInfo> otaInfo) {
        this.otaInfo = otaInfo;
    }
}
