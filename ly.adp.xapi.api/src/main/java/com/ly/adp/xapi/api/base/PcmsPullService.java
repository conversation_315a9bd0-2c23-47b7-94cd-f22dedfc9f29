package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.PcmsWebUtil;
import com.ly.adp.xapi.api.baseEntity.Pno18ResponseEntity;
import com.ly.bucn.component.xapi.entity.XapiField;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.normal.NormalService;
import com.ly.bucn.component.xapi.service.pull.PullDataBase;
import com.ly.bucn.component.xapi.service.pull.PullService;
import com.ly.mp.component.helper.StringHelper;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.beans.PropertyDescriptor;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.PropertyUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@PullService("PCMS_ADP")
public class PcmsPullService extends PullDataBase<String> {

    private static final Logger log = LoggerFactory.getLogger(PcmsPullService.class);

    @SuppressWarnings("unchecked")
    @Override
    public XapiData deserializeData(XapiInstance instance, XapiMap<String, Object> context, Object data) {
        try {
            String jsonresp = String.valueOf(data);
            if ("null".equals(jsonresp)) {
                return XapiData.creat();
            }
            Map<String, Object> resp = JSONObject.parseObject(jsonresp, Map.class);
            String errormsg = "";
            if (resp.containsKey("code")) {
                if ("200".equals(String.valueOf(resp.get("code")))) {
                    if (resp.containsKey("data")) {
                        List<Map<String, Object>> listData = (List<Map<String, Object>>) resp.get("data");
                        List<XapiTable> childTables = context.<List<XapiTable>>getValue("childTables");
                        XapiData xapiData = XapiData.creat();
                        listData.forEach(m -> {
                            String batchNo = UUID.randomUUID().toString().replace("-", "").toLowerCase();
                            XapiData.XapiDataRow xapiDataRow = XapiData.XapiDataRow.creat();
                            xapiDataRow.getMainData().put("xapibatchno", batchNo);
                            m.put("xapibatchno", batchNo);
                            xapiDataRow.setMainData(m);
                            childTables.forEach(n -> {

                                String firstFlag = n.getSubFlag();
                                List<Map<String, Object>> childdata = (List<Map<String, Object>>) m.get(firstFlag);
                                childdata.forEach(cd -> {
                                    cd.put("xapibatchno", batchNo);
                                });
                                xapiDataRow.addChild(n, childdata);
                                m.remove(firstFlag);
                            });
                            xapiData.add(xapiDataRow);
                        });
                        return xapiData;
                    }
                }
                if (resp.containsKey("msg")) {
                    errormsg = String.valueOf(resp.get("msg"));
                }
            }
            throw new RuntimeException("返回值异常:" + errormsg);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public Object serializeParam(XapiInstance xapiInstance, XapiMap<String, Object> context, XapiMap<String, Object> param) {
        if (context.containsKey("loopMap")) {
            Map<String, Object> loopMap = (Map<String, Object>) context.get("loopMap");
            String[] valueList = (String[]) loopMap.get("valueList");
            int index = Integer.parseInt(String.valueOf(loopMap.get("index")));
            param.put(String.valueOf(loopMap.get("key")), valueList[++index]);
            loopMap.put("index", index);
            context.put("loopMap", loopMap);
        } else {
            List<XapiField> xapiFieldList = ((XapiTable) context.getValue("paramTable")).getFields().stream().filter((m) -> {
                return "List".equals(m.getDbType());
            }).collect(Collectors.toList());
            if (xapiFieldList.size() > 0) {
                XapiField xapiField = xapiFieldList.get(0);
                String[] valueList = String.valueOf(param.get(xapiField.getViewField())).split("\\|");
                Map<String, Object> loopMap = new HashMap<>();
                loopMap.put("valueList", valueList);
                loopMap.put("valueListSize", valueList.length);
                loopMap.put("index", 0);
                loopMap.put("key", xapiField.getViewField());
                context.put("loopMap", loopMap);
                param.put(xapiField.getViewField(), valueList[0]);
            }
        }
        return param;
    }

    @Override
    public Object pullData(XapiInstance instance, XapiMap<String, Object> context, Object param) {
        //Pno18ResponseEntity pno18ResponseEntity =new Pno18ResponseEntity();
        String resp = null;
        try {
            //物料pno18拉取接口
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + instance.getService();
            String client_id = instance.getSystem().getXapiKv("client_id").getValueTee();
            String token = null;
            resp = PcmsWebUtil.getData(url, (Map<String, String>) param, client_id, token);
        } catch (Exception e) {
            log.error("获取PCMS数据失败", e);

        }
        return resp;
    }

    @Override
    public void endPull(XapiInstance instance, XapiMap<String, Object> context, XapiResult<String> result) {
        if (context.containsKey("loopMap")) {
            Map<String, Object> loopMap = (Map<String, Object>) context.get("loopMap");
            if (Integer.parseInt(String.valueOf(loopMap.get("valueListSize"))) > Integer.parseInt(String.valueOf(loopMap.get("index"))) + 1) {
                still();
            }
        }
        super.endPull(instance, context, result);
    }

//	@Override
//	public Object pullData(XapiInstance instance, XapiMap<String, Object> context, Object param) {
//		//Pno18ResponseEntity pno18ResponseEntity =new Pno18ResponseEntity();
//		String resp=null;
//		try {
//    		//物料pno18拉取接口
//        	String url =instance.getSystem().getXapiKv("provide_path").getValueTee()+instance.getService();
//			String client_id=instance.getSystem().getXapiKv("client_id").getValueTee();
//			String updateB =instance.getSystem().getXapiKv("update_b").getValueTee();
//			String updateD =instance.getSystem().getXapiKv("update_d").getValueTee();
//			String platForm=instance.getRemark();
//			Map<String,String> params=new HashMap<>();
//			params.put("platForm", platForm);
//			if(StringHelper.IsEmptyOrNull(updateB)&&StringHelper.IsEmptyOrNull(updateD)){
//				SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//				Date beginDate = new Date();
//				String bdate=dateFormat.format(beginDate);
//				Calendar date = Calendar.getInstance();
//				date.setTime(beginDate);
//				date.set(Calendar.DATE, date.get(Calendar.DATE)- 1);
//				String edate=dateFormat.format(date.getTime());
//				params.put("updateB", edate);
//				params.put("updateD", bdate);
//			}else{
//				if(!StringHelper.IsEmptyOrNull(updateB)){
//					params.put("updateB", updateB);
//				}
//				if(!StringHelper.IsEmptyOrNull(updateD)){
//					params.put("updateD", updateD);
//				}
//
//			}
//			//Map<String, String> params=beanToMap(param);
//
//			//params.put("updateB", edate);
//			//params.put("updateD", bdate);
//            String token=null;
//			//token=PcmsWebUtil.getToken();
//			resp =PcmsWebUtil.getData(url, params,client_id,token);
//			//resp="{\"data\":[{\"Id\":\"0df20b85-ea72-4311-ac83-28a1282b15e6\",\"productCode\":null,\"vehicletypeDesc\":\"HX11\",\"salesEffectiveinDate\":\"1900-01-0100:00:00\",\"plant\":null,\"salesEffectiveoutDate\":\"1900-01-0100:00:00\",\"salesDesc\":\"HX11_132Ah+200kW/343Nm(CATL-VREMTS+NFREENG+NIDEC)_ADVANTAGE_SRGRWD_B07MOYUBLACK_ADVANTAGEBLACK_LHD_CHINA\",\"description\":\"HX11_122Ah+200kW/384Nm(CATL-CATL+NFREENG+NIDEC)_ADVANTAGE_2022SPRING_SRGRWD_LHD_CHINA_B07MOYUBLACK_ADVANTAGEBLACK_NCONTRAST\",\"optionalId\":null,\"optionalCode\":null,\"vccOptionalCode\":null,\"pno18Id\":null,\"pno18Sequence\":\"010037\",\"platform\":\"11/HX\",\"pno181\":\"HX1EPD195151010037\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":\"HX1EPD195151\",\"pno34\":null,\"exteriorColor\":\"026\",\"vccexteriorColorid\":null,\"interiorColor\":\"B48\",\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndate\":\"2021-06-0100:00:00\",\"effectiveOutdate\":\"9999-12-3000:00:00\",\"createdTime\":\"1900-01-0100:00:00\",\"updatedTime\":\"1900-01-0100:00:00\",\"createdTimeTC\":\"2021-11-1819:02:50\",\"updatedTimeTC\":\"2021-11-2419:03:29\",\"isOverdue\":0,\"isMaintainPlant\":0,\"isMaintainDXLG\":0,\"isProductCode\":0,\"salesFeatureIdList\":\"\",\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDate\":0,\"salesEffectiveinDateStart\":\"1900-01-0100:00:00\",\"salesEffectiveinDateEnd\":\"1900-01-0100:00:00\",\"pno18s\":null,\"isManually\":0,\"chineseDescription\":\"HX11_122Ah+200kW/384Nm(宁德时代-宁德时代+无前驱动电机+后驱电机日电产)_领先版_2022春季_单级减速器后驱_左舵_中国_墨玉黑(B07)_时尚黑_无对比颜色\",\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null,\"optionCodeIds\":\"MF01\",\"options\":[{\"Id\":\"4517f1f7-a8df-4353-a9ab-11ef3b1653d1\",\"productCode\":null,\"vehicletypeDesc\":null,\"salesEffectiveinDateSpecified\":false,\"plant\":null,\"salesEffectiveoutDateSpecified\":false,\"salesDesc\":null,\"description\":null,\"optionalIdSpecified\":false,\"optionalCode\":\"MF01\",\"vccOptionalCode\":null,\"pno18IdSpecified\":false,\"pno18Sequence\":null,\"platform\":\"11/HX\",\"pno181\":\"HX1EPD195151010037\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":null,\"pno34\":null,\"exteriorColor\":null,\"vccexteriorColorid\":null,\"interiorColor\":null,\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndateSpecified\":false,\"effectiveOutdateSpecified\":false,\"createdTimeSpecified\":false,\"updatedTimeSpecified\":false,\"createdTimeTCSpecified\":false,\"updatedTimeTCSpecified\":false,\"isOverdueSpecified\":false,\"isMaintainPlantSpecified\":false,\"isMaintainDXLGSpecified\":false,\"isProductCodeSpecified\":false,\"salesFeatures\":null,\"salesFeatureIds\":null,\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDateSpecified\":false,\"salesEffectiveinDateStartSpecified\":false,\"salesEffectiveinDateEndSpecified\":false,\"isManuallySpecified\":false,\"chineseDescription\":null,\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null}],\"salesFeatures\":[]},{\"Id\":\"51f17d24-b885-47bc-b249-39959c633524\",\"productCode\":null,\"vehicletypeDesc\":\"HX11\",\"salesEffectiveinDate\":\"1900-01-0100:00:00\",\"plant\":null,\"salesEffectiveoutDate\":\"1900-01-0100:00:00\",\"salesDesc\":\"HX11_132Ah+200kW/343Nm(CATL-VREMTS+NFREENG+NIDEC)_ADVANTAGE_SRGRWD_B07MOYUBLACK_ADVANTAGEFASHIONICON_LHD_CHINA\",\"description\":\"HX11_122Ah+200kW/384Nm(CATL-CATL+NFREENG+NIDEC)_ADVANTAGE_2022SPRING_SRGRWD_LHD_CHINA_B07MOYUBLACK_ADVANTAGEFASHIONICON_NCONTRAST\",\"optionalId\":null,\"optionalCode\":null,\"vccOptionalCode\":null,\"pno18Id\":null,\"pno18Sequence\":\"010041\",\"platform\":\"11/HX\",\"pno181\":\"HX1EPD195151010041\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":\"HX1EPD195151\",\"pno34\":null,\"exteriorColor\":\"026\",\"vccexteriorColorid\":null,\"interiorColor\":\"B49\",\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndate\":\"2021-06-0100:00:00\",\"effectiveOutdate\":\"9999-12-3000:00:00\",\"createdTime\":\"1900-01-0100:00:00\",\"updatedTime\":\"1900-01-0100:00:00\",\"createdTimeTC\":\"2021-11-1819:02:50\",\"updatedTimeTC\":\"2021-11-2419:03:29\",\"isOverdue\":0,\"isMaintainPlant\":0,\"isMaintainDXLG\":0,\"isProductCode\":0,\"salesFeatureIdList\":\"\",\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDate\":0,\"salesEffectiveinDateStart\":\"1900-01-0100:00:00\",\"salesEffectiveinDateEnd\":\"1900-01-0100:00:00\",\"pno18s\":null,\"isManually\":0,\"chineseDescription\":\"HX11_122Ah+200kW/384Nm(宁德时代-宁德时代+无前驱动电机+后驱电机日电产)_领先版_2022春季_单级减速器后驱_左舵_中国_墨玉黑(B07)_时尚灰_无对比颜色\",\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null,\"optionCodeIds\":\"MF01\",\"options\":[{\"Id\":\"8ac9602b-6d7b-49c0-bc93-ac53f0f54076\",\"productCode\":null,\"vehicletypeDesc\":null,\"salesEffectiveinDateSpecified\":false,\"plant\":null,\"salesEffectiveoutDateSpecified\":false,\"salesDesc\":null,\"description\":null,\"optionalIdSpecified\":false,\"optionalCode\":\"MF01\",\"vccOptionalCode\":null,\"pno18IdSpecified\":false,\"pno18Sequence\":null,\"platform\":\"11/HX\",\"pno181\":\"HX1EPD195151010041\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":null,\"pno34\":null,\"exteriorColor\":null,\"vccexteriorColorid\":null,\"interiorColor\":null,\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndateSpecified\":false,\"effectiveOutdateSpecified\":false,\"createdTimeSpecified\":false,\"updatedTimeSpecified\":false,\"createdTimeTCSpecified\":false,\"updatedTimeTCSpecified\":false,\"isOverdueSpecified\":false,\"isMaintainPlantSpecified\":false,\"isMaintainDXLGSpecified\":false,\"isProductCodeSpecified\":false,\"salesFeatures\":null,\"salesFeatureIds\":null,\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDateSpecified\":false,\"salesEffectiveinDateStartSpecified\":false,\"salesEffectiveinDateEndSpecified\":false,\"isManuallySpecified\":false,\"chineseDescription\":null,\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null}],\"salesFeatures\":[]},{\"Id\":\"9ed525ea-d366-4b8f-ba5c-b8a160c5601c\",\"productCode\":null,\"vehicletypeDesc\":\"HX11\",\"salesEffectiveinDate\":\"1900-01-0100:00:00\",\"plant\":null,\"salesEffectiveoutDate\":\"1900-01-0100:00:00\",\"salesDesc\":\"HX11_132Ah+200kW/343Nm(CATL-VREMTS+NFREENG+NIDEC)_ADVANTAGE_SRGRWD_B07MOYUBLACK_ADVANTAGEFASHIONICON_LHD_CHINA\",\"description\":\"HX11_122Ah+200kW/384Nm(CATL-CATL+NFREENG+NIDEC)_ADVANTAGE_2022SPRING_SRGRWD_LHD_CHINA_B07MOYUBLACK_ADVANTAGEFASHIONICON_F42CONT\",\"optionalId\":null,\"optionalCode\":null,\"vccOptionalCode\":null,\"pno18Id\":null,\"pno18Sequence\":\"010042\",\"platform\":\"11/HX\",\"pno181\":\"HX1EPD195151010042\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":\"HX1EPD195151\",\"pno34\":null,\"exteriorColor\":\"026\",\"vccexteriorColorid\":null,\"interiorColor\":\"B49\",\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndate\":\"2021-06-0100:00:00\",\"effectiveOutdate\":\"9999-12-3000:00:00\",\"createdTime\":\"1900-01-0100:00:00\",\"updatedTime\":\"1900-01-0100:00:00\",\"createdTimeTC\":\"2021-11-1819:02:50\",\"updatedTimeTC\":\"2021-11-2419:03:29\",\"isOverdue\":0,\"isMaintainPlant\":0,\"isMaintainDXLG\":0,\"isProductCode\":0,\"salesFeatureIdList\":\"\",\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDate\":0,\"salesEffectiveinDateStart\":\"1900-01-0100:00:00\",\"salesEffectiveinDateEnd\":\"1900-01-0100:00:00\",\"pno18s\":null,\"isManually\":0,\"chineseDescription\":\"HX11_122Ah+200kW/384Nm(宁德时代-宁德时代+无前驱动电机+后驱电机日电产)_领先版_2022春季_单级减速器后驱_左舵_中国_墨玉黑(B07)_时尚灰_动感黄\",\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null,\"optionCodeIds\":\"MF18\",\"options\":[{\"Id\":\"30fe4554-e38a-4996-88da-fd6de45deebd\",\"productCode\":null,\"vehicletypeDesc\":null,\"salesEffectiveinDateSpecified\":false,\"plant\":null,\"salesEffectiveoutDateSpecified\":false,\"salesDesc\":null,\"description\":null,\"optionalIdSpecified\":false,\"optionalCode\":\"MF18\",\"vccOptionalCode\":null,\"pno18IdSpecified\":false,\"pno18Sequence\":null,\"platform\":\"11/HX\",\"pno181\":\"HX1EPD195151010042\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":null,\"pno34\":null,\"exteriorColor\":null,\"vccexteriorColorid\":null,\"interiorColor\":null,\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndateSpecified\":false,\"effectiveOutdateSpecified\":false,\"createdTimeSpecified\":false,\"updatedTimeSpecified\":false,\"createdTimeTCSpecified\":false,\"updatedTimeTCSpecified\":false,\"isOverdueSpecified\":false,\"isMaintainPlantSpecified\":false,\"isMaintainDXLGSpecified\":false,\"isProductCodeSpecified\":false,\"salesFeatures\":null,\"salesFeatureIds\":null,\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDateSpecified\":false,\"salesEffectiveinDateStartSpecified\":false,\"salesEffectiveinDateEndSpecified\":false,\"isManuallySpecified\":false,\"chineseDescription\":null,\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null}],\"salesFeatures\":[]},{\"Id\":\"a4021b5c-6a47-437f-969a-cf80e49e4d8e\",\"productCode\":null,\"vehicletypeDesc\":\"HX11\",\"salesEffectiveinDate\":\"1900-01-0100:00:00\",\"plant\":null,\"salesEffectiveoutDate\":\"1900-01-0100:00:00\",\"salesDesc\":\"HX11_132Ah+200kW/343Nm(CATL-VREMTS+NFREENG+NIDEC)_STANDARD_SRGRWD_A13MULIWHITE_ENTRYBLACK_LHD_CHINA\",\"description\":\"HX11_122Ah+200kW/384Nm(CATL-CATL+NFREENG+NIDEC)_STANDARD_2022SPRING_SRGRWD_LHD_CHINA_A13MULIWHITE_ENTRYBLACK\",\"optionalId\":null,\"optionalCode\":null,\"vccOptionalCode\":null,\"pno18Id\":null,\"pno18Sequence\":\"010004\",\"platform\":\"11/HX\",\"pno181\":\"HX1EP8095151010004\",\"pno18Str\":null,\"pno18Desc\":null,\"pno12\":\"HX1EP8095151\",\"pno34\":null,\"exteriorColor\":\"031\",\"vccexteriorColorid\":null,\"interiorColor\":\"B47\",\"vccinteriorColor\":null,\"optionalCodeid\":null,\"vccoptionalCodeid\":null,\"effectiveIndate\":\"2021-06-0100:00:00\",\"effectiveOutdate\":\"9999-12-3000:00:00\",\"createdTime\":\"1900-01-0100:00:00\",\"updatedTime\":\"1900-01-0100:00:00\",\"createdTimeTC\":\"2021-11-1819:02:50\",\"updatedTimeTC\":\"2021-11-2419:03:29\",\"isOverdue\":0,\"isMaintainPlant\":0,\"isMaintainDXLG\":0,\"isProductCode\":0,\"salesFeatureIdList\":\"\",\"series\":null,\"seriesName\":null,\"engine\":null,\"engineName\":null,\"gearbox\":null,\"gearboxName\":null,\"sale\":null,\"saleName\":null,\"interiorColorName\":null,\"exteriorColorName\":null,\"optionalCodeName\":null,\"interiorChineseColorName\":null,\"salesFeatureIdName\":null,\"familyId\":null,\"familyDesc\":null,\"familyDescCn\":null,\"optionDesc\":null,\"optionDescCn\":null,\"optionCodeId\":null,\"isSalesEffectiveinDate\":0,\"salesEffectiveinDateStart\":\"1900-01-0100:00:00\",\"salesEffectiveinDateEnd\":\"1900-01-0100:00:00\",\"pno18s\":null,\"isManually\":0,\"chineseDescription\":\"HX11_122Ah+200kW/384Nm(宁德时代-宁德时代+无前驱动电机+后驱电机日电产)_标准型_2022春季_单级减速器后驱_左舵_中国_牡蛎白_入门黑\",\"roofColor\":null,\"roofColorDescription\":null,\"mainFeatureText\":null,\"optionalText\":null,\"baseModelText\":null,\"mainNewFeatureText\":null,\"salesFeature\":null,\"optionCodeIds\":null,\"options\":[],\"salesFeatures\":[]}],\"code\":200,\"msg\":\"success\",\"requestId\":\"\"}";
//
//
//			 //resp1="{\"data\":[{\"vehicletypeDesc\":\"BX11\",\"description\":\"BX11_GEP3\",\"platform\":\"-/BX11\",\"pno181\":\"BX1G5075F151010334\",\"exteriorColor\":\"054\",\"interiorColor\":\"A88\",\"options\":[{\"optionalCode\":\"KE03\"},{\"optionalCode\": \"JB31\"}]}],\"code\":200,\"msg\":\"success\"}";
//			// resp1="{\"data\": [{\"chineseDesc\": \"蓝色\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"interiorCodeId\": \"A73\"},{\"chineseDesc\": \"中配版耀蓝色\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"interiorCodeId\": \"A70\"}],\"code\": 200,\"msg\": \"success\"}";
//			//resp1="{\"data\": [{\"chineseDesc\": \"冰晶白\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"exteriorCodeId\": \"043\"},{\"chineseDesc\": \"汽油灰\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"exteriorCodeId\": \"054\"}],\"code\": 200,\"msg\": \"success\"}";
//			//resp="{\"data\": [{\"optionalDesc\": \"N CONTRAST\",\"optionalDescCn\": \"无对比颜色\",\"optionCodeId\": \"MF01\"},{\"optionalDesc\": \"N SS\",,\"optionalDescCn\": \"无启/停系统\",\"optionCodeId\": \"BK01\"}],\"code\": 200,\"msg\": \"success\"}";
//			//pno18ResponseEntity = JSONObject.parseObject(resp, Pno18ResponseEntity.class);
//		} catch (Exception e) {
//			log.error("获取PCMS数据失败",e);
//
//		}
//		return resp;
//	}

	/*@Override
	public Object serializeParam(XapiInstance instance, XapiMap<String, Object> context, XapiMap<String, Object> param) {
		*//*String timeStamp = String.valueOf(System.currentTimeMillis());
		String nonce = String.valueOf(System.currentTimeMillis()/10000);
		String UserID = "30000";
		try {
			UserID = instance.getSystem().getXapiKv("mesuser").getValueTee();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			log.error("serializeParam",e);
		}
		Map<String, String> map= new HashMap<>();
		map.put("timeStamp", timeStamp);
		map.put("nonce", nonce);
		map.put("UserID", UserID);
		String json = JSONObject.toJSONString(map);*//*
		
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date beginDate = new Date();
		String bdate=dateFormat.format(beginDate);
		Calendar date = Calendar.getInstance();
		date.setTime(beginDate);
		date.set(Calendar.DATE, date.get(Calendar.DATE)- 1);
		String edate=dateFormat.format(date.getTime());
		String platForm=instance.getRemark();
		 Map<String,String> params=new HashMap<>();
		 params.put("platForm", platForm);
		 params.put("updateB", edate);
		 params.put("updateD", bdate);
		 
		return params;
		

	}*/

    public static Map<String, String> beanToMap(Object obj) {
        Map<String, String> params = new HashMap<String, String>();
        try {
            PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean();
            PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(obj);
            for (int i = 0; i < descriptors.length; i++) {
                String name = descriptors[i].getName();
                if (!"class".equals(name) && !StringHelper.IsEmptyOrNull(propertyUtilsBean.getNestedProperty(obj, name))) {
                    params.put(name, propertyUtilsBean.getNestedProperty(obj, name) + "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return params;
    }
}
