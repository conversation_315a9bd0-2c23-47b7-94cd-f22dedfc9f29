package com.ly.adp.xapi.api.zt.mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/10/31
 */
public interface IZtNormalMapper {

    Map<String, Object> findClueInfoDlr(Map<String, Object> map);

    String finddownloadOSS();

    String finddownload();

    Integer findDlrEmpSum(Map<String, Object> map);

    List<Map<String, Object>> findDlrEmp(Map<String, Object> map);

    int updateInfoDlr(@Param("param") Map<String, Object> clueInfoDlr, @Param("reviewId") Object reviewId);

    int updateInfoDlrCsc(@Param("param") Map<String, Object> clueInfoDlr, @Param("reviewId") Object reviewId);

    void updateReview(@Param("param") Map<String, Object> clueInfoDlr, @Param("reviewId") Object reviewId);
}
