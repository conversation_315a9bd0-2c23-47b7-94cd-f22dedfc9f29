package com.ly.adp.xapi.api.interfacecenter.services.impl;

import com.ly.adp.common.entity.Result;
import com.ly.adp.xapi.api.interfacecenter.entities.SapSaleCommissionTotal;
import com.ly.adp.xapi.api.interfacecenter.entities.dto.SapSaleCommissionTotalDTO;
import com.ly.adp.xapi.api.interfacecenter.mapper.SapSaleCommissionTotalMapper;
import com.ly.adp.xapi.api.interfacecenter.services.InterfacecenterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/18
 * @Version 1.0.0
 **/
@Service
public class InterfacecenterServiceImpl implements InterfacecenterService {

    @Autowired
    private SapSaleCommissionTotalMapper sapSaleCommissionTotalMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result batchSaveCommissionTotal(SapSaleCommissionTotalDTO sapSaleCommissionTotalDTO) {
        List<SapSaleCommissionTotal> commissionTotalList = sapSaleCommissionTotalDTO.getCommissionTotalList();
        sapSaleCommissionTotalMapper.batchInsertCommissionTotal(commissionTotalList);
        return Result.Builder.success();
    }
}
