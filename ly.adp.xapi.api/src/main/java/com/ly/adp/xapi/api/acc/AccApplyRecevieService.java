package com.ly.adp.xapi.api.acc;

import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiReceiveResponse;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import com.ly.bucn.component.xapi.service.receive.ReceiveService;
import com.ly.bucn.component.xapi.util.XapiUtil;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.util.*;

@ReceiveService("activity_adp")
public class AccApplyRecevieService extends ReceiveDataBase<Object> {
    private static final Logger log = LoggerFactory.getLogger(AccApplyRecevieService.class);

    @Override
    public XapiResult.XapiResultData<Object> Insert(XapiInstance instance, XapiMap<String, Object> context, XapiData.XapiDataRow dataRow) {
        XapiTable mainTable = context.getValue("mainTable");
        mainTable.getFields().stream().filter(f -> "json".equals(f.getDbType())).filter(f -> dataRow.getMainData().get(f.getSqlParaName()) != null).forEach(f -> {
            Object v = dataRow.getMainData().get(f.getSqlParaName());
            String json = "";
            if ("tickets".equals(f.getSqlParaName())) {
                if (!StringHelper.IsEmptyOrNull(v)) {
                    List<Map<String, Object>> tickets = (List<Map<String, Object>>) v;
                    List<Map<String, Object>> newTickets = new ArrayList<>();
                    for (Map<String, Object> map : tickets) {
                        if ("家庭票".equals(map.get("name"))) {
                            map.put("code", "1");
                        } else if ("成人票".equals(map.get("name"))) {
                            map.put("code", "2");
                        } else if ("儿童票".equals(map.get("name"))) {
                            map.put("code", "3");
                        } else if ("普通票".equals(map.get("name"))) {
                            map.put("code", "4");
                        } else {
                            map.put("code", "0");
                        }
                        newTickets.add(map);
                    }
                    json = JSONObject.toJSONString(newTickets);
                }
            } else {
                json = JSONObject.toJSONString(v);
            }
            dataRow.getMainData().put(f.getSqlParaName(), json);
        });
        return super.Insert(instance, context, dataRow);
    }

    public XapiResult<Object> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<Object> xapiResult = XapiResult.creat();
        XapiUtil.dataCount(data.getData().size());
        if ("activity_r_001".equals(instance.getObjectId())) {
            data.getData().forEach((d) -> {
                XapiUtil.dataCountDown();
                //String adpActivityId=UUID.randomUUID().toString();
                //d.getMainData().put("adpActivityId",adpActivityId);
                XapiResult.XapiResultData<Object> resultData = Insert(instance, context, d);
                if (!resultData.isSuccess()) {
                    //resultData.setId(adpActivityId);
                    xapiResult.addErrorData(resultData);
                } else {
                    //resultData.setId(adpActivityId);
                    xapiResult.addRightData(d);
                }

            });
        } else {
            data.getData().forEach((d) -> {
                XapiUtil.dataCountDown();
                XapiResult.XapiResultData<Object> resultData = this.Insert(instance, context, d);
                if (!resultData.isSuccess()) {
                    xapiResult.addErrorData(resultData);
                }

            });
        }
        return xapiResult;
    }

    public XapiReceiveResponse responseResult(XapiInstance instance, XapiMap<String, Object> context, XapiResult<Object> error) {
        XapiReceiveResponse response = XapiReceiveResponse.creat();
        if ("activity_r_001".equals(instance.getObjectId())) {
            XapiReceiveResponseMessage messResponse = XapiReceiveResponseMessage.creat();
            if (error == null) {
                messResponse.setCode("-1");
                messResponse.setMsg("Error");
            }
            if (error.getErrorData().size() > 0) {
                messResponse.setCode("1");
                error.getErrorData().forEach((e) -> {
                    messResponse.addErrors(e.getId(), e.getMessage());
                });
                //return response;
            }
            if (error.getRightData().size() > 0) {
                // messResponse.setCode("0");
                error.getRightData().forEach((e) -> {
                    messResponse.addSuccess(e.getMainData().get("activityId"), null, null);
                });
            }
            return messResponse;
        } else {
            if (error == null) {
                response.setCode("-1");
                response.setMsg("Error");
            }
            if (error.getErrorData().size() > 0) {
                response.setCode("1");
                error.getErrorData().forEach((e) -> {
                    response.add(e.getId(), e.getMessage());
                });
                //return response;
            }
        }
        return response;
    }

}
