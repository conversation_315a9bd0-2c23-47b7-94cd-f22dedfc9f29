package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiReceiveResponse;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.kernal.model.XapiResult.XapiResultData;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import com.ly.bucn.component.xapi.service.receive.ReceiveService;
import com.ly.bucn.component.xapi.util.XapiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;

@ReceiveService("ADP_CSP")
public class CspReceiveData extends ReceiveDataBase<Object> {
    private static final Logger log = LoggerFactory.getLogger(CspReceiveData.class);
    @Resource
    IXapiLogBiz xapiLogBiz;
    @Autowired
    PlatformTransactionManager transactionManager;
    @Autowired
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    public boolean handle(XapiInstance instance, Object data, Consumer<String> consumer) {

        XapiMap<String, Object> context=new XapiMap<>();
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(instance.getLabel());
        loginfo.setObjectType(instance.getObjectType());
        loginfo.setLogTime(new Date());
        loginfo.setLogStatus("0");
        context.put("loginfo", loginfo);

        XapiReceiveResponse receiveResponse = XapiReceiveResponse.creat();

        XapiResult<Object> insertResult=XapiResult.creat();


        try {
            init(instance, context);
            if (!checkConfig(instance, context)) {
                log.error(context.get("checkMessage").toString());
                log.error("停止接收任务:{}",instance.getLabel());
                throw new Exception(context.<String>getValue("checkMessage"));
            }
            XapiData xdata = convertData(instance, context, data);
            XapiData ximprodata = improveColumn(instance, context, xdata);
            insertResult = this.insertData(instance, context, ximprodata);
//            return true;

        } catch (Exception e) {
            receiveResponse.setCode("-1");
            receiveResponse.setMsg(e.getMessage());
            loginfo.setLogMessage(e.getMessage());
            loginfo.setLogStatus("-1");

            log.error("接收调用异常",e);
        }finally {
            Map<String, Object> resultData = new HashMap<>();
            List<Map<String, Object>> mess = new ArrayList<>();

            insertResult.getErrorData().forEach(m->{
                Map<String, Object> map = new HashMap<>();
                map.put("id", m.getId());
                map.put("isSuccess", m.isSuccess()?"Y":"N");
                map.put("message", m.isSuccess()?"":m.getMessage());
                mess.add(map);
            });
            resultData.put("data", mess);
            String jsonresp = JSONObject.toJSONString(resultData);
            consumer.accept(jsonresp);
            try {
                loginfo.setOutJson(jsonresp);
                loginfo.setInJson(data.toString());
                xapiLogBiz.add(loginfo);
            } catch (Exception e2) {
            }
        }

        return true;
    }

  /*  @Override
    public XapiResult<Object>    insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {

        XapiResult<Object> xapiResult = new XapiResult<>();

        data.getData().forEach(d->{
            XapiResultData<Object> resultData = Insert(instance, context, d);
            xapiResult.addErrorData(resultData);
        });

        return xapiResult;
    }*/


    public XapiResult<Object> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<Object> xapiResult = XapiResult.creat();
        XapiUtil.dataCount(data.getData().size());
        data.getData().forEach((d) -> {
            XapiUtil.dataCountDown();
            XapiResultData<Object> resultData =Insert(instance, context, d);
           // if (!resultData.isSuccess()) {
                xapiResult.addErrorData(resultData);
           // }

        });
        return xapiResult;
    }
}
