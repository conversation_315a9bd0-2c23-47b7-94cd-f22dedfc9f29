package com.ly.adp.xapi.api.base.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface CdpResMapper extends BaseMapper {

    // TODO 战败动作
    int updateClueInfo(Map<String, Object> paramMap);

    void insertOnecustLogout(Map<String, Object> paramMap);

    void updateOnecustInfo(Map<String, Object> paramMap);

    List<Map<String, Object>> queryClueInfo(Map<String, Object> paramMap);

    void deleteReviewInfo(Map<String, Object> paramMap);

    void updateCdpCustomers(Map<String, Object> paramMap);

    Map<String, Object> findCustInfo(@Param("smartId") Object c_smartid);

    List<Map<String, Object>> queryStoreManager(@Param("dlrCode") Object dlrCode);

    Map<String, Object> findCustInfoByPhone(@Param("phone") Object phone);

    void createMsgRecord(@Param("param") Map<String, Object> custInfo);

    int deleteNewClueDB(@Param("param") Map<String, Object> paramMap);

    boolean isDccClueInfo(@Param("mobile") String mobile);
}
