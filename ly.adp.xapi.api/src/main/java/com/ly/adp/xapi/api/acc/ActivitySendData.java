package com.ly.adp.xapi.api.acc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.ValueFilter;
import com.ly.adp.xapi.api.base.biz.OtdWebUtil;
import com.ly.bucn.component.xapi.entity.XapiField;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.bucn.component.xapi.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SendService("activity_adp")
public class ActivitySendData extends SendDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(ActivitySendData.class);

    @Override
    public Object convertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {

        XapiTable mainTable = context.getValue("mainTable");
        List<XapiField> jsonListFields = mainTable.getFields().stream().filter(n -> "1".equals(n.getState()))
                .filter(n -> "jsonList".equals(n.getDbType())).collect(Collectors.toList());
        List<XapiField> jsonMapFields = mainTable.getFields().stream().filter(n -> "1".equals(n.getState()))
                .filter(n -> "jsonMap".equals(n.getDbType())).collect(Collectors.toList());
        List<XapiField> jsonIntFields = mainTable.getFields().stream().filter(n -> "1".equals(n.getState()))
                .filter(n -> "jsonInt".equals(n.getDbType())).collect(Collectors.toList());
        List<XapiField> jsonBoolFields = mainTable.getFields().stream().filter(n -> "1".equals(n.getState()))
                .filter(n -> "jsonBool".equals(n.getDbType())).collect(Collectors.toList());

        ValueFilter valueFilter = new ValueFilter() {
            @Override
            public Object process(Object object, String name, Object value) {
                if (jsonListFields.stream().anyMatch(n -> name.equals(n.getViewField()))) {
                    if (value != null) {
                        String json = value.toString();
                        if (!json.isEmpty()) {
                            List<?> list = JSONObject.parseArray(json);
                            return list;
                        }
                    }
                }
                if (jsonMapFields.stream().anyMatch(n -> name.equals(n.getViewField()))) {
                    if (value != null) {
                        String json = value.toString();
                        if (!json.isEmpty()) {
                            Map<?, ?> map = (Map<?, ?>) JSON.parseObject(json);
                            return map;
                        }
                    }
                }
                if (jsonIntFields.stream().anyMatch(n -> name.equals(n.getViewField()))) {
                    if (value != null) {
                        String json = value.toString();
                        if (!json.isEmpty()) {
                            Integer i = Integer.parseInt(json);
                            return i;
                        }
                    }
                }

                if (jsonBoolFields.stream().anyMatch(n -> name.equals(n.getViewField()))) {
                    if (value != null) {
                        String json = value.toString();
                        if (!json.isEmpty()) {
                            Boolean b = Boolean.parseBoolean(json);
                            return b;
                        }
                    }
                }

                if (jsonIntFields.stream().anyMatch(n -> name.equals(n.getViewField()))) {
                    if (value != null) {
                        String json = value.toString();
                        if (!json.isEmpty()) {
                            Integer i = Integer.parseInt(json);
                            return i;
                        }
                    }
                }
                return value;
            }
        };
        if ("activity_s_detail".equals(instance.getObjectId())) {
            context.put("adp_activity_id", data.getDataEx().get(0).get("id"));
        }
        return JSONObject.toJSONString(data.getDataEx(), valueFilter);
    }

    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) throws Exception {
        String json = String.valueOf(o);
        String url = xapiInstance.getSystem().getXapiKv("provide_path").getValueTee() + xapiInstance.getService();
        String result = null;
        //Map<String,Object> mapParam=()o;
        List<?> list = JSONObject.parseArray(json);
        Map<String, Object> mapParam = (Map<String, Object>) list.get(0);
        if (url.contains("{activityId}")) {
            String newUrl = url.replace("{activityId}", mapParam.get("activityId").toString());
            mapParam.remove("activityId");
            result = ActivityWebUtil.postData(newUrl, mapParam);
        } else {
            result = ActivityWebUtil.postData(url, mapParam);
        }
        //result="{\"code\":\"success\",\"message\":\"string\",\"requestId\":\"string\",\"data\":{\"id\":\"11111111111111\",\"joinUrl\":\"string\"}}";
        return result;
    }

    @Override
    public XapiResult<String> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) {
        XapiResult<String> result = XapiResult.creat();
        try {
            String respJson = String.valueOf(o);
            if ("httperror".equals(respJson)) {
                throw new RuntimeException("httperror");
            }
            Map<String, Object> respMap = JSONObject.parseObject(respJson, Map.class);
            if (respMap.containsKey("code")) {
                if ("success".equals(respMap.get("code").toString())) {

                    if (manage(xapiInstance, o, result, stringObjectXapiMap)) {
                        return result;
                    }

                    return result;
                  /*  if(respMap.containsKey("message")&&respMap.get("message")!=null) {
                        // String s=respMap.get("message").toString();
                        //for(Map<String,String> messageMap:message){
                        Map<String, Object>
                        if("N".equals(messageMap.get("isSuccess"))){
                                result.addErrorData(new XapiResult.XapiResultData<String>(messageMap.get("id"),StringUtil.subStr(messageMap.get("message"),200)));
                            }
                       // }
                    }*/
                } else {
                    result.setStatus(1);
                    if (respMap.containsKey("message")) {
                        result.setMessage(StringUtil.subStr(String.valueOf(respMap.get("message")), 200));
                    } else {
                        result.setMessage("对方未返回错误信息");
                    }
                }
            } else {
                result.setStatus(1);
                result.setMessage("对方未返回响应码");
            }
        } catch (Exception e) {
            log.error("调用异常", e);
            result.setStatus(1);
            result.setMessage(StringUtil.subStr(e.getMessage(), 200));
        }
        return result;
    }

    /*public static void main(String[] args) {
        String s="https://mss-msg-sit.smartchina.com.cn/md-activity/forAdp/activity/{activityId}/cancelJoin";
        s.replace("{activityId}","1");
        System.out.println(s);
    }*/
    boolean manage(XapiInstance instance, Object resp, XapiResult<String> result, XapiMap<String, Object> context) {
        try {
            if (Flux.fromArray(instance.getSystem().getXapiKv("ERP_RESP_LABEL").getComments().split(",")).any(m -> instance.getLabel().equals(m)).block()) {
                if (resp != null && StringUtils.hasText(String.valueOf(resp))) {
                    Map<String, Object> rrdd = JSONObject.parseObject(String.valueOf(resp), Map.class);
                    // Map<String, Object> relMap= (Map<String,Object>) rrdd.get("data");
                    rrdd.put("adp_activity_id", context.get("adp_activity_id"));
                    List<Map<String, Object>> dataList = new ArrayList<>();
                    dataList.add(rrdd);
                    List<XapiTable> childTables = context.<List<XapiTable>>getValue("respChildTables");
                    dataList.forEach(m -> {
                        XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                        dr.setMainData(m);
                        childTables.forEach(n -> {
                            String firstFlag = n.getSubFlag();
                            List<Map<String, Object>> childdata = (List<Map<String, Object>>) m.get(firstFlag);
                            dr.addChild(n, childdata);
                            //m.remove(firstFlag);
                        });
                        result.addRightData(dr);
                    });

                } else {
                    result.setStatus(1);
                    result.setMessage("活动中台未返回数据");
                }
                return true;
            }

        } catch (Exception e) {
            result.setStatus(1);
            result.setMessage("");
            return true;

        }
        return false;
    }

}
