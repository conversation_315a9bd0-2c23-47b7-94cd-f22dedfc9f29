package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 关机事件记录
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class PowerOff implements Serializable {

    private static final long serialVersionUID = 8029711361639312429L;

    /**
     * 关机时间
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 电量
     */
    @JsonProperty("power")
    private int power;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getPower() {
        return power;
    }

    public void setPower(int power) {
        this.power = power;
    }
}
