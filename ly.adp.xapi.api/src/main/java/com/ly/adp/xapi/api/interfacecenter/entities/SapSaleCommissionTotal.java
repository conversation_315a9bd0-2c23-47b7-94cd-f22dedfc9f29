package com.ly.adp.xapi.api.interfacecenter.entities;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;

/**
 * t_ifs_base_sap_sale_commission_total 对应实体
 * <AUTHOR>
 * @Date 2024/7/18
 * @Version 1.0.0
 **/
@TableName("t_ifs_base_sap_sale_commission_total")
public class SapSaleCommissionTotal {

    /**
     * 主键ID
     */
    @TableId("LOGS_ID")
    private String logsId;

    /**
     * 代理商编码
     */
    @TableField("AGENT_CODE")
    private String agentCode;

    /**
     * 代理商名称
     */
    @TableField("AGENT_NAME")
    private String agentName;

    /**
     * 大区名称
     */
    @TableField("BIG_AREA")
    private String bigArea;

    /**
     * 城市公司编码
     */
    @TableField("COMPANY_CODE")
    private String companyCode;

    /**
     * 城市公司
     */
    @TableField("COMPANY_NANE")
    private String companyName;

    /**
     * 门店编码
     */
    @TableField("DLR_CODE")
    private String dlrCode;

    /**
     * 门店名称
     */
    @TableField("DLR_NAME")
    private String dlrName;

    /**
     * 门店类型
     */
    @TableField("DLR_TYPE")
    private String dlrType;

    /**
     * 结算月份
     */
    @TableField("SETTLEMENT_MONTH")
    private String settlementMonth;

    /**
     * 佣金类型编码
     */
    @TableField("COMMISSION_CODE")
    private String commissionCode;

    /**
     * 佣金金额
     */
    @TableField("COMMISSION_AMOUNT")
    private String commissionAmount;

    /**
     * 佣金类型
     */
    @TableField("COMMISSION_TYPE")
    private String commissionType;

    /**
     * 插入日期
     */
    @TableField("INSERT_DATE")
    private LocalDateTime insertDate;

    /**
     * 发送状态
     */
    @TableField("SEND_FLAG")
    private String sendFlag;

    /**
     * 错误日志
     */
    @TableField("ERR_LOG")
    private String errLog;

    /**
     * 发送日期
     */
    @TableField("SEND_DATE")
    private LocalDateTime sendDate;

    // Getters and Setters
    public String getLogsId() {
        return logsId;
    }

    public void setLogsId(String logsId) {
        this.logsId = logsId;
    }

    public String getAgentCode() {
        return agentCode;
    }

    public void setAgentCode(String agentCode) {
        this.agentCode = agentCode;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getBigArea() {
        return bigArea;
    }

    public void setBigArea(String bigArea) {
        this.bigArea = bigArea;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getDlrCode() {
        return dlrCode;
    }

    public void setDlrCode(String dlrCode) {
        this.dlrCode = dlrCode;
    }

    public String getDlrName() {
        return dlrName;
    }

    public void setDlrName(String dlrName) {
        this.dlrName = dlrName;
    }

    public String getDlrType() {
        return dlrType;
    }

    public void setDlrType(String dlrType) {
        this.dlrType = dlrType;
    }

    public String getSettlementMonth() {
        return settlementMonth;
    }

    public void setSettlementMonth(String settlementMonth) {
        this.settlementMonth = settlementMonth;
    }

    public String getCommissionCode() {
        return commissionCode;
    }

    public void setCommissionCode(String commissionCode) {
        this.commissionCode = commissionCode;
    }

    public String getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(String commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public String getCommissionType() {
        return commissionType;
    }

    public void setCommissionType(String commissionType) {
        this.commissionType = commissionType;
    }

    public LocalDateTime getInsertDate() {
        return insertDate;
    }

    public void setInsertDate(LocalDateTime insertDate) {
        this.insertDate = insertDate;
    }

    public String getSendFlag() {
        return sendFlag;
    }

    public void setSendFlag(String sendFlag) {
        this.sendFlag = sendFlag;
    }

    public String getErrLog() {
        return errLog;
    }

    public void setErrLog(String errLog) {
        this.errLog = errLog;
    }

    public LocalDateTime getSendDate() {
        return sendDate;
    }

    public void setSendDate(LocalDateTime sendDate) {
        this.sendDate = sendDate;
    }
}

