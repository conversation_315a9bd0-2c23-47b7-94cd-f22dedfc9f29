package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

@ApiModel("获取培训人员信息请求参数")
public class OpenUserRequestEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /*登录名*/
    private String  loginName;
    /*公司编号*/
    private String  corpCode;
    /*APPKEY*/
    private String  appKey_;
    /*数字签名*/
    private String sign_;
    /*系统时间*/
    private Long timestamp_;

    public String getLoginName() {
        return loginName;
    }

    public void setLoginName(String loginName) {
        this.loginName = loginName;
    }

    public String getCorpCode() {
        return corpCode;
    }

    public void setCorpCode(String corpCode) {
        this.corpCode = corpCode;
    }

    public String getAppKey_() {
        return appKey_;
    }

    public void setAppKey_(String appKey_) {
        this.appKey_ = appKey_;
    }

    public String getSign_() {
        return sign_;
    }

    public void setSign_(String sign_) {
        this.sign_ = sign_;
    }

    public Long getTimestamp_() {
        return timestamp_;
    }

    public void setTimestamp_(Long timestamp_) {
        this.timestamp_ = timestamp_;
    }
}
