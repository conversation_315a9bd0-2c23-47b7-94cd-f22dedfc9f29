package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.ms.AdpMsRunner;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.*;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import com.ly.bucn.component.xapi.service.receive.ReceiveService;
import com.ly.bucn.component.xapi.util.XapiUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@ReceiveService("ec_adp")
public class EcReceiveData extends ReceiveDataBase<Object> {

    private static final Logger log = LoggerFactory.getLogger(EcReceiveData.class);

    @Resource
    IXapiLogBiz xapiLogBiz;

    @Autowired
    PlatformTransactionManager transactionManager;

    @Autowired
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public boolean handle(XapiInstance instance, Object data, Consumer<String> consumer) {

        XapiMap<String, Object> context = new XapiMap<>();
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(instance.getLabel());
        loginfo.setObjectType(instance.getObjectType());
        loginfo.setLogTime(new Date());
        loginfo.setLogStatus("0");
        context.put("loginfo", loginfo);

        XapiReceiveResponse receiveResponse = XapiReceiveResponse.creat();

        XapiResult<Object> insertResult = XapiResult.creat();


        try {
            init(instance, context);
            if (!checkConfig(instance, context)) {
                log.error(context.get("checkMessage").toString());
                log.error("停止接收任务:{}", instance.getLabel());
                throw new Exception(context.<String>getValue("checkMessage"));
            }
            XapiData xdata = convertData(instance, context, data);
            XapiData ximprodata = improveColumn(instance, context, xdata);
            insertResult = this.insertData(instance, context, ximprodata);
//            return true;

        } catch (Exception e) {
            receiveResponse.setCode("-1");
            receiveResponse.setMsg(e.getMessage());
            loginfo.setLogMessage(e.getMessage());
            loginfo.setLogStatus("-1");

            log.error("接收调用异常", e);
        } finally {
            Map<String, Object> resultData = new HashMap<>(1);
            List<Map<String, Object>> mess = new ArrayList<>();

            insertResult.getErrorData().forEach(m -> {
                Map<String, Object> map = new HashMap<>(3);
                map.put("id", m.getId());
                map.put("isSuccess", m.isSuccess() ? "Y" : "N");
                map.put("message", m.isSuccess() ? "" : m.getMessage());
                mess.add(map);
            });
            resultData.put("data", mess);
            String jsonresp = JSONObject.toJSONString(resultData);
            consumer.accept(jsonresp);
            try {
                loginfo.setOutJson(jsonresp);
                loginfo.setInJson(data.toString());
                xapiLogBiz.add(loginfo);
            } catch (Exception ignored) {
            }
        }

        return true;
    }

    public XapiData convertData(XapiInstance instance, XapiMap<String, Object> context, Object data) {
        XapiData xData = XapiData.creat();
        List<XapiTable> childTables = (List) context.getValue("childTables");
        //通过mq获取的报文
        if (AdpMsRunner.isMqPublish()) {

            JSONArray jsonArray = new JSONArray();
            JSONObject jsonbody = new JSONObject();
            String j = (String) data;

            if ("[".equals(j.substring(0, 1))) {
                jsonArray = JSONObject.parseArray((String) data);
            } else {
                jsonbody = JSONObject.parseObject((String) data);
            }
            JSONObject newjson = new JSONObject();
            if (jsonArray != null && jsonArray.size() > 0) {
                newjson.put("body", jsonArray);
            } else {
                newjson.put("body", jsonbody);
            }
            String json = JSONObject.toJSONString(newjson);


            XapiReceiveRequest requestObj = JSONObject.parseObject(json, XapiReceiveRequest.class);
            List<Map<String, Object>> body = requestObj.getBody();
            body.forEach((m) -> {
                XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                dr.setMainData(m);
                childTables.forEach((n) -> {
                    String firstFlag = n.getSubFlag();
                    List<Map<String, Object>> childdata = (List) m.get(firstFlag);
                    dr.addChild(n, childdata);
                    m.remove(firstFlag);
                });
                xData.add(dr);
            });

        } else {
            String json = (String) data;
            XapiReceiveRequest requestObj = JSONObject.parseObject(json, XapiReceiveRequest.class);
            if (requestObj.getHead().containsKey("serialnum")) {
                String serialnum = String.valueOf(requestObj.getHead().get("serialnum"));
                XapiLog loginfo = context.getValue("loginfo");
                loginfo.setSerialNum(serialnum);
            }

            List<Map<String, Object>> body = requestObj.getBody();
            body.forEach((m) -> {
                XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                dr.setMainData(m);
                childTables.forEach((n) -> {
                    String firstFlag = n.getSubFlag();
                    List<Map<String, Object>> childdata = (List) m.get(firstFlag);
                    dr.addChild(n, childdata);
                    m.remove(firstFlag);
                });
                xData.add(dr);
            });
        }
        return xData;
    }

    public XapiResult<Object> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<Object> xapiResult = XapiResult.creat();
        XapiUtil.dataCount(data.getData().size());
        data.getData().forEach((d) -> {
            XapiUtil.dataCountDown();
            XapiTable mainTable = context.getValue("mainTable");
            mainTable.getFields().stream().filter(f -> "jsonList".equals(f.getDbType())).filter(f -> d.getMainData().get(f.getSqlParaName()) != null).forEach(f -> {
                Object v = d.getMainData().get(f.getSqlParaName());
                String json = JSONObject.toJSONString(v);
                d.getMainData().put(f.getSqlParaName(), json);
            });
            XapiResult.XapiResultData<Object> resultData = Insert(instance, context, d);
            // if (!resultData.isSuccess()) {
            xapiResult.addErrorData(resultData);
            // }

        });
        return xapiResult;
    }
}
