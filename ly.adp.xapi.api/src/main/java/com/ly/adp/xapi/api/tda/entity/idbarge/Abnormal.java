package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 异常信息
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class Abnormal implements Serializable {

    private static final long serialVersionUID = -5254570481281831777L;

    /**
     * DSP是否就绪
     */
    @JsonProperty("dsp_ready")
    private boolean dspReady;

    /**
     * RTC是否丢失
     */
    @JsonProperty("rtc_lost")
    private boolean rtcLost;

    public boolean isDspReady() {
        return dspReady;
    }

    public void setDspReady(boolean dspReady) {
        this.dspReady = dspReady;
    }

    public boolean isRtcLost() {
        return rtcLost;
    }

    public void setRtcLost(boolean rtcLost) {
        this.rtcLost = rtcLost;
    }
}
