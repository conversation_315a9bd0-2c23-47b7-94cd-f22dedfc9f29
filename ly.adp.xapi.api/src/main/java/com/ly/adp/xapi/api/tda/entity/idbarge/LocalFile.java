package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 本地文件信息
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class LocalFile implements Serializable {

    private static final long serialVersionUID = -3087179485199381381L;

    /**
     * 文件名
     */
    @JsonProperty("filename")
    private String filename;

    /**
     * 文件大小
     */
    @JsonProperty("size")
    private int size;

    /**
     * 开始录音时间
     */
    @JsonProperty("record_ts")
    private long recordTs;

    /**
     * 开始上传时间
     */
    @JsonProperty("upload_ts")
    private long uploadTs;

    /**
     * 开始录音模式
     */
    @JsonProperty("start_mode")
    private String startMode;

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public long getRecordTs() {
        return recordTs;
    }

    public void setRecordTs(long recordTs) {
        this.recordTs = recordTs;
    }

    public long getUploadTs() {
        return uploadTs;
    }

    public void setUploadTs(long uploadTs) {
        this.uploadTs = uploadTs;
    }

    public String getStartMode() {
        return startMode;
    }

    public void setStartMode(String startMode) {
        this.startMode = startMode;
    }
}
