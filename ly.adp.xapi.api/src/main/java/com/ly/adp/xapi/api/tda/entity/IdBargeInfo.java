package com.ly.adp.xapi.api.tda.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.adp.xapi.api.tda.entity.idbarge.*;
import com.ly.adp.xapi.api.util.CommonUtil;
import com.ly.mp.component.helper.StringHelper;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工牌信息
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
@TableName("t_usc_idbarge_info")
public class IdBargeInfo implements Serializable {

    private static final long serialVersionUID = -4902926813589470123L;

    /**
     * 工牌信息id
     */
    @TableId(value = "idbarge_id")
    private String idbargeId;

    /**
     * 工牌SN号
     */
    @TableField("sn")
    private String sn;

    /**
     * 用户账号
     */
    @TableField("user_id")
    private String userId;

    /**
     * 绑定的用户姓名
     */
    @TableField("user_name")
    private String userName;

    /**
     * 联网记录
     */
    @TableField("wifi_history")
    private String wifiHistory;

    /**
     * 文件上传事件
     */
    @TableField("upload_history")
    private String uploadHistory;

    /**
     * 本地文件信息
     */
    @TableField("local_files")
    private String localFiles;

    /**
     * 操作历史
     */
    @TableField("oper_history")
    private String operHistory;

    /**
     *OTA升级
     */
    @TableField("ota_info")
    private String otaInfo;

    /**
     * 蓝牙操作历史
     */
    @TableField("ble_history")
    private String bleHistory;

    /**
     * 是否可用
     */
    @TableField("is_enable")
    private String isEnable;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    @TableField("modifier")
    private String modifier;

    /**
     * 最后更新时间
     */
    @TableField("last_updated_date")
    private LocalDateTime lastUpdatedDate;

    public String getIdbargeId() {
        return idbargeId;
    }

    public void setIdbargeId(String idbargeId) {
        this.idbargeId = idbargeId;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getWifiHistory() {
        return wifiHistory;
    }

    public void setWifiHistory(String wifiHistory) {
        this.wifiHistory = wifiHistory;
    }

    public String getUploadHistory() {
        return uploadHistory;
    }

    public void setUploadHistory(String uploadHistory) {
        this.uploadHistory = uploadHistory;
    }

    public String getLocalFiles() {
        return localFiles;
    }

    public void setLocalFiles(String localFiles) {
        this.localFiles = localFiles;
    }

    public String getOperHistory() {
        return operHistory;
    }

    public void setOperHistory(String operHistory) {
        this.operHistory = operHistory;
    }

    public String getOtaInfo() {
        return otaInfo;
    }

    public void setOtaInfo(String otaInfo) {
        this.otaInfo = otaInfo;
    }

    public String getBleHistory() {
        return bleHistory;
    }

    public void setBleHistory(String bleHistory) {
        this.bleHistory = bleHistory;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public LocalDateTime getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(LocalDateTime lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    /**
     * 通过入参设置插入信息
     * @param idBargeInfoDto
     */
    public void setDataForInsert(IdBargeInfoDto idBargeInfoDto) {
        setIdbargeId(StringHelper.GetGUID());
        Common common = idBargeInfoDto.getCommon();
        setSn(common.getSn());
        setCreator("TDA");
        setCreatedTime(LocalDateTime.now());
        setModifier("TDA");
        setUserId(common.getUserId());
        setUserName(common.getUserName());
        DevInfo devInfo = idBargeInfoDto.getDevInfo();
        if (canSaveDevInfo(devInfo)){
            saveDevInfo(devInfo);
        }
        setLastUpdatedDate(LocalDateTime.now());
    }

    /**
     * 设置设备信息
     * @param devInfo
     */
    private void saveDevInfo(DevInfo devInfo) {
        List<WifiHistory> wifiHistoryList = devInfo.getWifiHistory();
        if (!StringHelper.IsEmptyOrNull(wifiHistoryList)) {
            setWifiHistory(CommonUtil.listToJsonString(wifiHistoryList));
        }
        List<UploadHistory> uploadHistoryList = devInfo.getUploadHistory();
        if (!StringHelper.IsEmptyOrNull(uploadHistoryList)) {
            setUploadHistory(CommonUtil.listToJsonString(uploadHistoryList));
        }
        List<LocalFile> localFileList = devInfo.getLocalFiles();
        if (!StringHelper.IsEmptyOrNull(localFileList)) {
            setLocalFiles(CommonUtil.listToJsonString(localFileList));
        }
        OperHistory operHistoryDto = devInfo.getOperHistory();
        if (!StringHelper.IsEmptyOrNull(operHistoryDto)) {
            setOperHistory(CommonUtil.objToJsonString(operHistoryDto));
        }
        List<OtaInfo> otaInfoList = devInfo.getOtaInfo();
        if (!StringHelper.IsEmptyOrNull(otaInfoList)) {
            setOtaInfo(CommonUtil.listToJsonString(otaInfoList));
        }
        List<BleHistory> bleHistoryList = devInfo.getBleHistory();
        if (!StringHelper.IsEmptyOrNull(bleHistoryList)) {
            setBleHistory(CommonUtil.listToJsonString(bleHistoryList));
        }
    }

    /**
     * 判断设备信息是否有效
     * @param devInfo
     * @return
     */
    private boolean canSaveDevInfo(DevInfo devInfo) {
        return !StringHelper.IsEmptyOrNull(devInfo);
    }

    /**
     * 通过入参设置更新信息
     * @param idBargeInfoDto
     */
    public void setDataForUpdate(IdBargeInfoDto idBargeInfoDto) {
        Common common = idBargeInfoDto.getCommon();
        setUserId(common.getUserId());
        setUserName(common.getUserName());
        DevInfo devInfo = idBargeInfoDto.getDevInfo();
        if (canSaveDevInfo(devInfo)){
            saveDevInfo(devInfo);
        }
        setLastUpdatedDate(LocalDateTime.now());
    }
}
