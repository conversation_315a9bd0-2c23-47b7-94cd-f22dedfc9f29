package com.ly.adp.xapi.api.bpm;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import com.ly.adp.xapi.api.base.mapper.CdpResMapper;
import com.ly.adp.xapi.api.bpm.mapper.IBpmReceive001Mapper;
import com.ly.adp.xapi.api.tda.entity.LookupValue;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.*;

import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.component.helper.StringHelper;

import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;

import java.net.URI;
import java.net.URISyntaxException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;

import java.util.*;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/21
 */
@NormalService("BPM_ADP")
public class BpmNormalService extends NormalDataGenericBase {
    private static final Logger log = LoggerFactory.getLogger(BpmNormalService.class);
    @Autowired
    IBpmReceive001Mapper bpmReceive001Mapper;

    @Autowired
    CdpResMapper cdpResMapper;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> map) {
        map.put("BPM_R_001", this::bpmnormalinsert);
        map.put("VIRTUAL_R_001", this::virtualReactive);
        map.put("VIRTUAL_R_002", this::virtualRecordReactive);
    }


    @Transactional(rollbackFor = Exception.class)
    public NormalResult<Map<String, Object>> bpmnormalinsert(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        String data = param.getData();
        Map map = JSONObject.parseObject(data, Map.class);
        Object body = map.get("body");
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        HashMap<String, Object> responseMsg = Maps.newHashMap();
        Map<String, Object> paramMap = JSONObject.parseObject(JSONObject.toJSONString(body), Map.class);
        if (StringHelper.IsEmptyOrNull(paramMap)) {
            responseMassage(0, paramMap, result, responseMsg, "参数为空");
            return result;
        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("requestCode"))) {
            responseMassage(0, paramMap, result, responseMsg, "审批单号为空");
            return result;
        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("approvalStatus"))) {
            responseMassage(0, paramMap, result, responseMsg, "审批状态为空");
            return result;
        }
        if (!"1".equals(paramMap.get("approvalStatus")) && !"-1".equals(paramMap.get("approvalStatus"))) {
            responseMassage(0, paramMap, result, responseMsg, "审批状态有误");
            return result;
        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("applicationTime"))) {
            responseMassage(0, paramMap, result, responseMsg, "审批时间为空");
            return result;
        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("applicationPerson"))) {
            responseMassage(0, paramMap, result, responseMsg, "审批人为空");
            return result;
        }
        Map<String, Object> subsidyMap = SwitchDbInvoke.invokeTidb(() -> bpmReceive001Mapper.findSubsidy(paramMap));
        Map<String, Object> subsidyDMap = SwitchDbInvoke.invokeTidb(() -> bpmReceive001Mapper.findSubsidyD(paramMap));
        if (StringHelper.IsEmptyOrNull(subsidyMap) && StringHelper.IsEmptyOrNull(subsidyDMap)) {
            responseMassage(0, paramMap, result, responseMsg, "审批单号不存在");
            return result;
        }
        if (!StringHelper.IsEmptyOrNull(subsidyMap)) {
            if (!"3".equals(subsidyMap.get("auditStatus"))) {
                responseMassage(0, paramMap, result, responseMsg, "审批单状态有误");
                return result;
            }
            if ("1".equals(paramMap.get("approvalStatus"))) {
                paramMap.put("status", "1");
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateStatus(paramMap));
                paramMap.put("dlrBuildSubsidyId", subsidyMap.get("dlrBuildSubsidyId"));
                if (subsidyMap.get("payStatus").equals(subsidyMap.get("batchTotal").toString())) {
                    paramMap.put("status", "2");
                }
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateSubsidy(paramMap));
                paramMap.put("logsId", UUID.randomUUID().toString());
                paramMap.put("ztype", "1");
                paramMap.put("lifnr", subsidyMap.get("dlrCode"));
                paramMap.put("amount", subsidyMap.get("adjustAmount"));
                paramMap.put("txz01", subsidyMap.get("remark"));
                bpmReceive001Mapper.insSapApproval(paramMap);
                bpmReceive001Mapper.insSapApprovalData(paramMap);
            }
            if ("-1".equals(paramMap.get("approvalStatus"))) {
                paramMap.put("status", "0");
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateStatus(paramMap));
                paramMap.put("dlrBuildSubsidyId", subsidyMap.get("dlrBuildSubsidyId"));
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateSubsidy(paramMap));
            }
        }

        if (!StringHelper.IsEmptyOrNull(subsidyDMap)) {
            if (!"3".equals(subsidyDMap.get("approvalStatus"))) {
                responseMassage(0, paramMap, result, responseMsg, "审批单状态有误");
                return result;
            }
            if ("1".equals(paramMap.get("approvalStatus"))) {
                paramMap.put("status", "1");
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateSupportDetail(paramMap));
                paramMap.put("rentSupportId", subsidyDMap.get("rentSupportId"));
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateSupport(paramMap));
                String s = UUID.randomUUID().toString();
                paramMap.put("logsId", s);
                paramMap.put("ztype", "2");
                paramMap.put("lifnr", subsidyDMap.get("dlrCode"));
                paramMap.put("amount", subsidyDMap.get("subsidyAmount"));
                paramMap.put("txz01", subsidyDMap.get("remark"));
                bpmReceive001Mapper.insSapApproval(paramMap);
                bpmReceive001Mapper.insSapApprovalData(paramMap);
            }
            if ("-1".equals(paramMap.get("approvalStatus"))) {
                paramMap.put("status", "0");
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateSupportDetail(paramMap));
                paramMap.put("rentSupportId", subsidyDMap.get("rentSupportId"));
                SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateSupport(paramMap));
            }
        }

        // SwitchDbInvoke.invoke("base", () -> bpmReceive001Mapper.updateStatus(paramMap));

        bpmReceive001Mapper.insRApproval(paramMap);
        responseMassage(1, paramMap, result, responseMsg, "成功");
        result.setData(responseMsg);
        result.setSuccess(true);
        return result;
    }

    private void responseMassage(int code, Map map, NormalResult<Map<String, Object>> result, HashMap<String, Object> responseMsg, String errorMsg) {
        responseMsg.put("code", code);
        // responseMsg.put("requestCode", map.get("requestCode"));
        responseMsg.put("message", map.get("requestCode") + errorMsg);
        result.setData(responseMsg);
        result.setSuccess(false);
    }

    @Transactional(rollbackFor = Exception.class)
    public NormalResult<Map<String, Object>> virtualReactive(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        String data = param.getData();
        Map<String, Object> paramMaps = JSONObject.parseObject(data, Map.class);
        List<Map<String, Object>> paramList = JSONObject.parseObject(JSONObject.toJSONString(paramMaps.get("body")), List.class);
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        HashMap<String, Object> responseMsg = Maps.newHashMap();

        if (StringHelper.IsEmptyOrNull(paramList)) {
            responseMsg.put("code", 0);
            responseMsg.put("message", "参数为空");
            result.setData(responseMsg);
            result.setSuccess(false);
            return result;
        }
        StringBuilder error = new StringBuilder();
        boolean flag = false;
        for (Map<String, Object> paramMap : paramList) {
            if (StringHelper.IsEmptyOrNull(paramMap.get("virtualPhone"))) {
                error.append("虚拟手机号为空, ");
                flag = true;
                continue;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("provinceId"))) {
                error.append(paramMap.get("virtualPhone")).append("省份编码为空, ");
                flag = true;
                continue;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("cityId"))) {

                error.append(paramMap.get("virtualPhone")).append("城市编码为空, ");
                flag = true;
                continue;
            }
           /* if (StringHelper.IsEmptyOrNull(paramMap.get("organizationName"))) {
                error.append(paramMap.get("virtualPhone")).append("门店名称为空, ");
                flag = true;
                continue;
            }*/
            if (StringHelper.IsEmptyOrNull(paramMap.get("organizationNo"))) {
                error.append(paramMap.get("virtualPhone")).append("门店编码为空, ");
                flag = true;
                continue;
            }
            Map<String, Object> virtual = bpmReceive001Mapper.findVirtualPhone(paramMap);
            if (!StringHelper.IsEmptyOrNull(virtual)) {
                if ("1".equals(virtual.get("virtualStatus")) || "0".equals(virtual.get("virtualStatus"))) {
                    error.append(paramMap.get("virtualPhone")).append("该虚拟手机号已存在, ");
                    flag = true;
                    continue;
                }
                bpmReceive001Mapper.updateVirtual(paramMap);
                continue;
            }
            bpmReceive001Mapper.insertVirtual(paramMap);
        }

        if (flag) {
            responseMsg.put("code", 0);
            responseMsg.put("message", error);
            result.setData(responseMsg);
            result.setSuccess(false);
            return result;
        }
        responseMsg.put("code", 1);
        responseMsg.put("message", "成功");
        result.setData(responseMsg);
        result.setSuccess(false);
        return result;
    }


    private NormalResult<Map<String, Object>> virtualRecordReactive(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, NormalParam<String> normalParam) {
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        HashMap<String, Object> responseMsg = Maps.newHashMap();
        String data = normalParam.getData();
        Map<String, Object> paramMaps = JSONObject.parseObject(data, Map.class);

        Map<String, Object> empCode = bpmReceive001Mapper.findEmpMsg(paramMaps);
        Map<String, Object> custInfoMap = bpmReceive001Mapper.findOnecustInfo(paramMaps);

        paramMaps.put("empCode", empCode.get("userId"));

        paramMaps.put("custId", custInfoMap.get("custId"));
        String callId = UUID.randomUUID().toString().replace("-", "");
        paramMaps.put("callId", callId);
        int insert = bpmReceive001Mapper.insertVirtualRecord(paramMaps);
        try {
            if (!StringHelper.IsEmptyOrNull(custInfoMap) && "1".equals(paramMaps.get("callType").toString())) {
                Map<String, Object> custInfo = cdpResMapper.findCustInfoByPhone(custInfoMap.get("phone"));
                custInfo.put("messageContent", "您的用户【" + custInfoMap.get("custName") + "-" + custInfoMap.get("phone") + "】于" + paramMaps.get("callStartTime") + ",给您来电");
                custInfo.put("messageId", UUID.randomUUID().toString());
                custInfo.put("phone", custInfoMap.get("phone"));
                custInfo.put("createdDate", paramMaps.get("callStartTime"));
                cdpResMapper.createMsgRecord(custInfo);
            }
        } catch (Exception e) {
            log.info("外呼回拨提示失败{}", e);
        }
        if (!"0".equals(paramMaps.get("status").toString())) {

            responseMsg.put("code", "0");
            responseMsg.put("message", "SUCCESS");
            result.setData(responseMsg);
            result.setSuccess(true);
            return result;
        }


        // Map<String, Object> custMap = Maps.newHashMap();
        List<LookupValue> lookUpValue = bpmReceive001Mapper.findLookUpValue();

        Map<String, String> collectMap = lookUpValue.stream().collect(Collectors
                .toMap(LookupValue::getLookupValueCode, LookupValue::getLookupValueName, (k1, k2) -> k2));
        log.info("读取tda配置{}", JSONObject.toJSONString(collectMap));
        HashMap<String, Object> additionInfo = Maps.newHashMap();
        additionInfo.put("client_id", custInfoMap.get("custId"));
        additionInfo.put("client_name", custInfoMap.get("custName"));
        additionInfo.put("client_phone", custInfoMap.get("phone"));
        String addition = JSONObject.toJSONString(additionInfo);
        List<Map<String, Object>> callList = Lists.newArrayList();
        Map<String, Object> callMap = Maps.newHashMap();
        callMap.put("callId", callId);
        callMap.put("accountId", collectMap.get("accountId"));
        callMap.put("appId", collectMap.get("appId"));
        callMap.put("userId", empCode.get("empCode"));
        // callMap.put("roleInfo", "");
        callMap.put("additionInfo", addition);
        if ("1".equals(paramMaps.get("callType"))) {
            callMap.put("sceneId", collectMap.get("sceneId2"));
        } else {
            callMap.put("sceneId", collectMap.get("sceneId"));
        }


        callMap.put("audioUrl", paramMaps.get("callRecordUrl"));
        if ("1".equals(paramMaps.get("callType").toString())) {
            callMap.put("roleInfo", "CUSTOMER_AGENT");
        } else {
            callMap.put("roleInfo", collectMap.get("roleInfo"));
        }

        callList.add(callMap);
        HashMap<String, Object> requestMap = Maps.newHashMap();
        requestMap.put("callList", callList);
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json; charset=UTF-8");
        // headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        String request = JSONObject.toJSONString(requestMap);
        log.info("请求tda传输录音入参{}", request);
        //  HttpEntity<String> entity = new HttpEntity<>(request, headers);
        HttpEntity<Object> entity = new HttpEntity<>(request, headers);
        try {
            //  RspEntity postData = RequestClient.post(collectMap.get("url"), requestMap, headers);
            String postData = postData(collectMap.get("url"), entity);
            //  ResponseEntity<String> response = getRestTemplate().exchange(collectMap.get("url"), HttpMethod.POST, entity, String.class);
            log.info("tda质检响应{}", postData);
            //    String body = response.getBody();
        } catch (Exception e) {
            log.info("tda质检请求失败{}", e);
        }

        responseMsg.put("code", "0");
        responseMsg.put("message", "SUCCESS");
        result.setData(responseMsg);
        result.setSuccess(true);
        return result;
    }

    public static String postData(String url, HttpEntity<Object> param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);


            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, param, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }
//    public static String postData(String url, HttpEntity<Object> param) {
//
//        ResponseEntity<String> responseEntity = null;
//        URI uri;
//        try {
//            uri = new URI(url);
//
//            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, param, String.class);
//
//        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
//            log.info("请求质检失败{}",e);
//            throw new RuntimeException(e);
//        }
//
//        return responseEntity.getBody();
//    }
//
//    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
//        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;
//
//        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
//                .loadTrustMaterial(null, acceptingTrustStrategy)
//                .build();
//        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);
//
//
//        CloseableHttpClient httpClient = HttpClients.custom()
//                .setSSLSocketFactory(csf)
//                .build();
//
//        HttpComponentsClientHttpRequestFactory requestFactory =
//                new HttpComponentsClientHttpRequestFactory();
//
//        requestFactory.setHttpClient(httpClient);
//        RestTemplate restTemplate = new RestTemplate(generateHttpRequestFactory());
//        return restTemplate;
//    }
//
//    private static HttpComponentsClientHttpRequestFactory generateHttpRequestFactory()
//            throws NoSuchAlgorithmException, KeyManagementException, KeyStoreException {
//        TrustStrategy acceptingTrustStrategy = (x509Certificates, authType) -> true;
//        SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(null, acceptingTrustStrategy).build();
//        SSLConnectionSocketFactory connectionSocketFactory = new SSLConnectionSocketFactory(sslContext, new NoopHostnameVerifier());
//
//        HttpClientBuilder httpClientBuilder = HttpClients.custom();
//        httpClientBuilder.setSSLSocketFactory(connectionSocketFactory);
//        CloseableHttpClient httpClient = httpClientBuilder.build();
//        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
//        factory.setHttpClient(httpClient);
//        return factory;
//    }
}
