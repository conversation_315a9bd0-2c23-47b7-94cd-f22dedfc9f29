package com.ly.adp.xapi.api.boot;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ly.adp.common.entity.Result;
import com.ly.adp.xapi.api.tda.entity.idbarge.IdBargeInfoDto;
import com.ly.adp.xapi.api.tda.receive.ITdaNormalService;
import com.ly.adp.xapi.api.util.XapiLogUtil;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.mp.busicen.common.context.BusicenException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Api(tags = "*TDA自定义接口")
@RestController
@RequestMapping("/noauth/rest/TDA_ADP")
public class TdaNormalController {

    private static final Logger log = LoggerFactory.getLogger(TdaNormalController.class);

    @Autowired
    private ITdaNormalService tdaNormalService;

    @Autowired
    private IXapiLogBiz xapiLogBiz;

    @ApiOperation("tda工牌日志同步")
    @PostMapping("/idBarge")
    public Result idbargeInfo(@RequestBody IdBargeInfoDto idBargeInfoDto) {
        Result result = null;
        String originalJson = "";
        try {
            // 记录原始的下划线入参
            ObjectMapper objectMapper = new ObjectMapper();
            originalJson = objectMapper.writeValueAsString(idBargeInfoDto);
            result = tdaNormalService.idbargeInfo(idBargeInfoDto);
        } catch (BusicenException e) {
            result = Result.Builder
                    .failure(e.getMessage());
        } catch (Exception e) {
            log.error("tda工牌日志推送接口异常", e);
            result = Result.Builder
                    .failure("tda工牌日志推送接口调用失败");
        } finally {
            // 记录xapi日志
            XapiLogUtil.addSuccessXapiLog(xapiLogBiz, "tda_adp_idBarge", originalJson, JSON.toJSONString(result));
            return result;
        }
    }
}