package com.ly.adp.xapi.api.tda.entity.enums;

/**
 * 充电模式
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public enum ChargeModeEnum {
    OMNIDIRECTIONAL("omnidirectional", "双向"),
    DIRECTIONAL("directional", "单向");

    private final String code;
    private final String message;

    ChargeModeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "AntennaType{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
