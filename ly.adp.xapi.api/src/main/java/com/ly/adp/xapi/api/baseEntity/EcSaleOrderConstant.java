package com.ly.adp.xapi.api.baseEntity;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/4/4
 */
public class EcSaleOrderConstant {
    public static final Integer SUCCESS_CODE = 1;
    public static final String SUCCESS = "success";
    public static final Integer FAILED_CODE = 0;
    public static final String DATA_ISNOTEMPTY = "参数不能为空";
    public static final String VIN_ISNOTEMPTY = "VIN码不能为空";
    public static final String CODE_ISNOTEMPTY = "批售码不能为空";
    public static final String CREATE_SUCCESS = "订单创建成功";
    public static final String SALES_STATUS_FAILED = "现车状态有误";
    public static final String STOCK_VEHICLE_FAILED = "当前车辆状态不可售";
    public static final String BATCH_SALE_DATA_RESULT = "查询批售现车不存在";
    public static final String SALES_STATUS_SUCCESS = "车辆不为可售";
    public static final String BATCH_SALE_SHELF_NO = "该VIN未上架,无法下单";
}
