package com.ly.adp.xapi.api.base.mapper;

import java.util.Map;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

public interface BasePcmsResMapper extends BaseMapper {
	 
	int insertPno18(@Param(value = "item")Map<String,Object> map);
	
	int insertOptional(@Param(value = "item")Map<String,Object> map);
	
	int insertExteriors(@Param(value = "item")Map<String,Object> map);
	
	int insertInteriors(@Param(value = "item")Map<String,Object> map);
	
	int insertOptionalM(@Param(value = "item")Map<String,Object> map);

	int insertTransfer(@Param("param") Map<String, Object> transferMap);
}
