package com.ly.adp.xapi.api.base;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.CdpBiz;
import com.ly.adp.xapi.api.base.biz.CdpWebUtil;
import com.ly.adp.xapi.api.base.biz.CspWebUtil;
import com.ly.adp.xapi.api.base.biz.TrainWebUtil;
import com.ly.bucn.component.xapi.entity.XapiField;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.pull.PullDataBase;
import com.ly.bucn.component.xapi.service.pull.PullService;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;

@PullService("TRAIN_ADP")
public class TrainPullService extends PullDataBase<String> {

    private static final Logger log = LoggerFactory.getLogger(TrainWebUtil.class);

    @Autowired
    NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public Object serializeParam(XapiInstance paramTInstance, XapiMap<String, Object> paramTContext,
                                 XapiMap<String, Object> paramTParam) {
        try {

            List<Map<String, Object>> newListData = new ArrayList<>();

            paramTContext.put("stillFlag", false);
            paramTParam.put("corpCode", paramTInstance.getSystem().getXapiKv("corpCode").getValueTee());
            int rownum = Integer.parseInt(paramTInstance.getSystem().getXapiKv("ROWNUM").getValueTee());

            if (paramTContext.containsKey("PageListData")) {
                List<Map<String, Object>> sendDlrListData = (ArrayList<Map<String, Object>>) paramTContext.get("PageListData");
                for (int i = 0; i < sendDlrListData.size(); i++) {
                    if (i > rownum) {
                        paramTContext.put("stillFlag", true);
                        break;
                    }

                    if (sendDlrListData.get(i).containsKey("organizeCode")) {
                        Map<String, Object> mapDlr = new HashMap<>();
                        mapDlr.put("organizeCode", sendDlrListData.get(i).get("organizeCode"));
                        mapDlr.put("positionCode", sendDlrListData.get(i).get("positionCode"));

                        newListData.add(mapDlr);
                    }

                }
                //去掉已请求过的门店参数
                sendDlrListData.removeAll(newListData);
                paramTParam.put("organizePositionList", newListData);
                paramTContext.put("PageListData", sendDlrListData);
            } else {
                //第一次请求
                List<Map<String, Object>> PageListData = new ArrayList<>();
                List<Map<String, Object>> PosiListData = new ArrayList<>();
                String sql = paramTInstance.getSystem().getXapiKv("DLRLIST").getValueTee();
                String sqlPosi = paramTInstance.getSystem().getXapiKv("POSITION_DATA").getValueTee();

                List<Map<String, Object>> listData = SwitchDbInvoke
                        .invokeTidb(() -> namedParameterJdbcTemplate.queryForList(sql, new HashMap<>()));
                List<Map<String, Object>> positionDataPosi = SwitchDbInvoke
                        .invokeTidb(() -> namedParameterJdbcTemplate.queryForList(sqlPosi, new HashMap<>()));

                for (int i = 0; i < listData.size(); i++) {
                    for (int z = 0; z < positionDataPosi.size(); z++) {
                        if (listData.get(i).containsKey("DLR_CODE") &
                                listData.get(i).containsKey("DLR_TYPE") &
                                positionDataPosi.get(z).get("ORG_ID").equals(listData.get(i).get("DLR_TYPE"))) {
                            Map<String, Object> mapDlr = new HashMap<>();
                            mapDlr.put("organizeCode", listData.get(i).get("DLR_CODE"));
                            mapDlr.put("positionCode", positionDataPosi.get(z).get("STATION_CODE"));
                            PageListData.add(mapDlr);
                        }
                    }

                }

                for (int z = 0; z < PageListData.size(); z++) {
                    if (z > rownum) {
                        paramTContext.put("stillFlag", true);
                        break;
                    }
                    if (PageListData.get(z).containsKey("organizeCode") & PageListData.get(z).containsKey("positionCode")) {
                        Map<String, Object> mapDlr = new HashMap<>();
                        mapDlr.put("organizeCode", PageListData.get(z).get("organizeCode"));
                        mapDlr.put("positionCode", PageListData.get(z).get("positionCode"));
                        newListData.add(mapDlr);
                    }

                }
                //去掉已请求过的门店参数
                PageListData.removeAll(newListData);
                paramTParam.put("organizePositionList", newListData);
                paramTContext.put("PageListData", PageListData);

            }

        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);

        }
        return paramTParam;
    }

    @Override
    public Object pullData(XapiInstance xapiInstance, XapiMap<String, Object> context, Object param) {

        //获取数据
        Map<String, String> headG = new HashMap<>();
        return TrainWebUtil.pullData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getService(), headG, (Map<String, Object>) param);
    }

    @Override
    public XapiData deserializeData(XapiInstance paramTInstance, XapiMap<String, Object> paramTContext, Object data) {
        XapiData xapiData = XapiData.creat();

        String jsonresp = String.valueOf(data);
        Map<String, Object> resp = JSONObject.parseObject(jsonresp, Map.class);
        if (resp.containsKey("jsonObj") & resp.get("jsonObj") != null) {
            List<Map<String, Object>> listData = (List<Map<String, Object>>) resp.get("jsonObj");
            if (listData.size() > 0) {

                listData.forEach(m -> {
                    XapiData.XapiDataRow xapiDataRow = XapiData.XapiDataRow.creat();
                    if (m.containsKey("employeeCodeList")) {
                        String employeeCodeList = m.get("employeeCodeList").toString().replace("[", "").replace("]", "").replace("\"", "");
                        m.put("employeeCodeList", employeeCodeList);
                    }
                    if (m.containsKey("passMapUserList")) {
                        String passMapUserList = m.get("passMapUserList").toString().replace("[", "").replace("]", "").replace("\"", "");
                        m.put("passMapUserList", passMapUserList);
                    }
                    if (m.containsKey("passTmsUserList")) {
                        String passMapUserList = m.get("passTmsUserList").toString().replace("[", "").replace("]", "").replace("\"", "");
                        m.put("passTmsUserList", passMapUserList);
                    }
                    xapiDataRow.setMainData(m);
                    xapiData.add(xapiDataRow);

                });
            }
            return xapiData;
        }

        return xapiData;

    }

    @Override
    public void endPull(XapiInstance instance, XapiMap<String, Object> context, XapiResult<String> result) {
        if (context.containsKey("stillFlag") && (Boolean) context.get("stillFlag")) {
            still();
        }
        super.endPull(instance, context, result);
    }
}
