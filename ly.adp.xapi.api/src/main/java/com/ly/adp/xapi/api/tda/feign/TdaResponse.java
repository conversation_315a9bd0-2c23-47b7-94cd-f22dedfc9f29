package com.ly.adp.xapi.api.tda.feign;

import java.util.List;
import java.util.Map;

public class TdaResponse {
    private int code;
    private String msg;
    private Data data;

    public TdaResponse() {
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Data getData() {
        return data;
    }

    public void setData(Data data) {
        this.data = data;
    }

    public static class Data {
        private List<User> users;
        private Map<String, Node> nodes;
        private int total;

        public List<User> getUsers() {
            return users;
        }

        public void setUsers(List<User> users) {
            this.users = users;
        }

        public Map<String, Node> getNodes() {
            return nodes;
        }

        public void setNodes(Map<String, Node> nodes) {
            this.nodes = nodes;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }
    }

    public static class User {
        private String user_id;
        private String user_name;
        private long user_phone;
        private int node_id;
        private long update_time;
        private String domain_key;
        private String password;
        private List<String> sns;
        private List<String> aliasIDs;
        private boolean is_agent;
        private List<Integer> manager_nodes;
        private List<String> manager_node_names;

        public String getUser_id() {
            return user_id;
        }

        public void setUser_id(String user_id) {
            this.user_id = user_id;
        }

        public String getUser_name() {
            return user_name;
        }

        public void setUser_name(String user_name) {
            this.user_name = user_name;
        }

        public long getUser_phone() {
            return user_phone;
        }

        public void setUser_phone(long user_phone) {
            this.user_phone = user_phone;
        }

        public int getNode_id() {
            return node_id;
        }

        public void setNode_id(int node_id) {
            this.node_id = node_id;
        }

        public long getUpdate_time() {
            return update_time;
        }

        public void setUpdate_time(long update_time) {
            this.update_time = update_time;
        }

        public String getDomain_key() {
            return domain_key;
        }

        public void setDomain_key(String domain_key) {
            this.domain_key = domain_key;
        }

        public String getPassword() {
            return password;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public List<String> getSns() {
            return sns;
        }

        public void setSns(List<String> sns) {
            this.sns = sns;
        }

        public List<String> getAliasIDs() {
            return aliasIDs;
        }

        public void setAliasIDs(List<String> aliasIDs) {
            this.aliasIDs = aliasIDs;
        }

        public boolean isIs_agent() {
            return is_agent;
        }

        public void setIs_agent(boolean is_agent) {
            this.is_agent = is_agent;
        }

        public List<Integer> getManager_nodes() {
            return manager_nodes;
        }

        public void setManager_nodes(List<Integer> manager_nodes) {
            this.manager_nodes = manager_nodes;
        }

        public List<String> getManager_node_names() {
            return manager_node_names;
        }

        public void setManager_node_names(List<String> manager_node_names) {
            this.manager_node_names = manager_node_names;
        }
    }

    public static class Node {
        private int node_id;
        private int parent_id;
        private String parent_name;
        private String node_name;
        private String node_code;
        private long update_time;
        private int node_level;
        private int node_type;

        public int getNode_id() {
            return node_id;
        }

        public void setNode_id(int node_id) {
            this.node_id = node_id;
        }

        public int getParent_id() {
            return parent_id;
        }

        public void setParent_id(int parent_id) {
            this.parent_id = parent_id;
        }

        public String getParent_name() {
            return parent_name;
        }

        public void setParent_name(String parent_name) {
            this.parent_name = parent_name;
        }

        public String getNode_name() {
            return node_name;
        }

        public void setNode_name(String node_name) {
            this.node_name = node_name;
        }

        public String getNode_code() {
            return node_code;
        }

        public void setNode_code(String node_code) {
            this.node_code = node_code;
        }

        public long getUpdate_time() {
            return update_time;
        }

        public void setUpdate_time(long update_time) {
            this.update_time = update_time;
        }

        public int getNode_level() {
            return node_level;
        }

        public void setNode_level(int node_level) {
            this.node_level = node_level;
        }

        public int getNode_type() {
            return node_type;
        }

        public void setNode_type(int node_type) {
            this.node_type = node_type;
        }
    }
}