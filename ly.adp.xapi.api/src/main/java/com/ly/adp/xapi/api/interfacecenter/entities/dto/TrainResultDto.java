package com.ly.adp.xapi.api.interfacecenter.entities.dto;

import com.ly.adp.common.entity.Result;
import com.ly.adp.xapi.api.interfacecenter.entities.TrainResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.UUID;

@ApiModel("培训数据保存入参DTO")
public class TrainResultDto {

    @ApiModelProperty(value = "岗位名称")
    private String positionName;

    @ApiModelProperty(value = "部门名称")
    private String organizeName;

    @ApiModelProperty(value = "部门code")
    private String organizeCode;

    @ApiModelProperty(value = "岗位code")
    private String positionCode;

    @ApiModelProperty(value = "通过培训人数")
    private String passTrainNum;

    @ApiModelProperty(value = "编制人数")
    private String formationNum;

    @ApiModelProperty(value = "通过培训的工号集合")
    private String employeeCodeList;

    @ApiModelProperty(value = "通过爱学在线工号集合")
    private String passMapUserList;

    @ApiModelProperty(value = "通过岗位认证工号集合")
    private String passTmsUserList;

    @ApiModelProperty(value = "缺口人数（编制人数-实际通过培训人数）")
    private String lackNum;

    @ApiModelProperty(value = "当前部门岗位下的人数")
    private String postPersonNum;

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public String getOrganizeName() {
        return organizeName;
    }

    public void setOrganizeName(String organizeName) {
        this.organizeName = organizeName;
    }

    public String getOrganizeCode() {
        return organizeCode;
    }

    public void setOrganizeCode(String organizeCode) {
        this.organizeCode = organizeCode;
    }

    public String getPositionCode() {
        return positionCode;
    }

    public void setPositionCode(String positionCode) {
        this.positionCode = positionCode;
    }

    public String getPassTrainNum() {
        return passTrainNum;
    }

    public void setPassTrainNum(String passTrainNum) {
        this.passTrainNum = passTrainNum;
    }

    public String getFormationNum() {
        return formationNum;
    }

    public void setFormationNum(String formationNum) {
        this.formationNum = formationNum;
    }

    public String getEmployeeCodeList() {
        return employeeCodeList;
    }

    public void setEmployeeCodeList(String employeeCodeList) {
        this.employeeCodeList = employeeCodeList;
    }

    public String getPassMapUserList() {
        return passMapUserList;
    }

    public void setPassMapUserList(String passMapUserList) {
        this.passMapUserList = passMapUserList;
    }

    public String getPassTmsUserList() {
        return passTmsUserList;
    }

    public void setPassTmsUserList(String passTmsUserList) {
        this.passTmsUserList = passTmsUserList;
    }

    public String getLackNum() {
        return lackNum;
    }

    public void setLackNum(String lackNum) {
        this.lackNum = lackNum;
    }

    public String getPostPersonNum() {
        return postPersonNum;
    }

    public void setPostPersonNum(String postPersonNum) {
        this.postPersonNum = postPersonNum;
    }

    public TrainResult convertToEntity() {
        TrainResult entity = new TrainResult();
        BeanUtils.copyProperties(this, entity);
        // 设置其他字段
        entity.setLogsId(UUID.randomUUID().toString().replace("-", ""));
        return entity;
    }

    /**
     * 校验培训结果数据
     * @return Result 校验结果
     */
    public Result validate() {
        // 校验必填项
        if (StringUtils.isBlank(this.organizeCode) || StringUtils.isBlank(this.organizeName)) {
            return Result.Builder.failure("门店信息不能为空");
        }

        if (StringUtils.isBlank(this.positionCode) || StringUtils.isBlank(this.positionName)) {
            return Result.Builder.failure("岗位信息不能为空");
        }

        // 校验三选一
        boolean hasEmployeeCode = StringUtils.isNotBlank(this.employeeCodeList);
        boolean hasPassMapUser = StringUtils.isNotBlank(this.passMapUserList);
        boolean hasPassTmsUser = StringUtils.isNotBlank(this.passTmsUserList);

        if (!hasEmployeeCode && !hasPassMapUser && !hasPassTmsUser) {
            return Result.Builder.failure("通过培训的工号集合、通过爱学在线工号集合、通过岗位认证工号集合必须填写其中一项");
        }

        return Result.Builder.success();
    }
}
