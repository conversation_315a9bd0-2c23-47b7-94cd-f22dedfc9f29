package com.ly.adp.xapi.api.base.biz;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.entities.MailSendDTO;
import com.ly.adp.xapi.api.base.entities.MssResponse;
import com.ly.adp.xapi.api.base.entities.VerificationCodeDTO;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.normal.NormalResult;
import com.ly.mp.busicen.common.context.BusicenException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 中台接口对接业务类
 */
@Service
public class MssBiz {

	private static final Logger log = LoggerFactory.getLogger(MssBiz.class);

	/**
	 * 发送邮箱验证码
	 * @param instance
	 * @param stringObjectXapiMap
	 * @param normalParam
	 * @return
	 */
	public NormalResult<MssResponse> sendVerificationCode(XapiInstance instance, XapiMap<String, Object> stringObjectXapiMap, NormalParam normalParam) {
		try {
			// 获取中台发送邮箱验证码接口url
			String url = instance.getSystem().getProvidePath() + instance.getRemark();
			// String url = "http://10.170.223.211:28006" + instance.getRemark();
			VerificationCodeDTO verificationCodeDTO = JSONUtil.toBean((String) normalParam.getData(), VerificationCodeDTO.class);
			MailSendDTO mailSendDTO = verificationCodeDTO.toMailSendDTO();
			String reqStr = JSONUtil.toJsonStr(mailSendDTO);
			log.info("sendVerificationCode入参:{}", reqStr);
			// 使用http进行发送请求
			String result = HttpUtil.createPost(url)
					.header("Content-Type", "application/json")
					.body(reqStr)
					.execute()
					.body();
			log.info("sendVerificationCode响应:{}", result);
			MssResponse response = JSONObject.parseObject(result, MssResponse.class);
			if (!response.isSuccess()) {
				throw BusicenException.create("发送邮箱验证码失败:" + response.getMessage());
			}
			return NormalResult.createOk(response);
		} catch (Exception e) {
			log.error("发送邮箱验证码失败",e);
			NormalResult<MssResponse> normalResult = NormalResult.create();
			normalResult.setSuccess(Boolean.FALSE);
			normalResult.setMessage("发送邮箱验证码失败");
			return normalResult;
		}
	}
}
