package com.ly.adp.xapi.api.tda;


import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.CspReceiveData;
import com.ly.adp.xapi.api.tda.receive.TdaReceive001;
import com.ly.adp.xapi.api.tda.receive.TdaReceive002;
import com.ly.bucn.component.xapi.entity.XapiInstance;

import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.*;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;

import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import com.ly.bucn.component.xapi.service.receive.ReceiveService;
import com.ly.bucn.component.xapi.util.XapiUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @description: tda数据接收
 * @date 2023/8/7
 */

@ReceiveService("TDA_ADP")
public class TdaReactiveService extends ReceiveDataBase<Object> {


    private static final Logger log = LoggerFactory.getLogger(CspReceiveData.class);

    final TdaReceive001 tdaReceive001;
    final TdaReceive002 tdaReceive002;
    final IXapiLogBiz xapiLogBiz;

    public TdaReactiveService(TdaReceive001 tdaReceive001, IXapiLogBiz xapiLogBiz, TdaReceive002 tdaReceive002) {
        this.tdaReceive001 = tdaReceive001;
        this.xapiLogBiz = xapiLogBiz;
        this.tdaReceive002 = tdaReceive002;
    }

    @Override
    public boolean handle(XapiInstance instance, Object data, Consumer<String> consumer) {

        XapiMap<String, Object> context = new XapiMap<>();
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(instance.getLabel());
        loginfo.setObjectType(instance.getObjectType());
        loginfo.setLogTime(new Date());
        loginfo.setLogStatus("0");
        context.put("loginfo", loginfo);

        XapiReceiveResponse receiveResponse = XapiReceiveResponse.creat();

        XapiResult<Object> insertResult = XapiResult.creat();


        try {
            init(instance, context);
            if (!checkConfig(instance, context)) {
                log.error(context.get("checkMessage").toString());
                log.error("停止接收任务:{}", instance.getLabel());
                throw new Exception(context.<String>getValue("checkMessage"));
            }
            XapiData xdata = this.convertData(instance, context, data);
            //     XapiData ximprodata = improveColumn(instance, context, xdata);
            insertResult = this.insertData(instance, context, xdata);
//            return true;

        } catch (Exception e) {
            receiveResponse.setCode("-1");
            receiveResponse.setMsg(e.getMessage());
            loginfo.setLogMessage(e.getMessage());
            loginfo.setLogStatus("-1");
            insertResult.setMessage(e.getMessage());

            log.error("接收调用异常", e);
        } finally {
            Map<String, Object> resultData = new HashMap<>();
            if (!CollectionUtils.isEmpty(insertResult.getErrorData()) || "-1".equals(receiveResponse.getCode())) {
                resultData.put("result", "0");
                resultData.put("msg", insertResult.getMessage());

            } else {
                resultData.put("result", "1");
                resultData.put("msg", "SUCCESS");

            }
            String jsonresp = JSONObject.toJSONString(resultData);
            consumer.accept(jsonresp);
            // try {
            loginfo.setOutJson(jsonresp);
            loginfo.setInJson(data.toString());
            xapiLogBiz.add(loginfo);
            //} catch (Exception e2) {
            //   }
        }

        return true;
    }

    @Override
    public XapiResult.XapiResultData<Object> Insert(XapiInstance instance, XapiMap<String, Object> context, XapiData.XapiDataRow dataRow) {
        XapiResult.XapiResultData<Object> resultData = new XapiResult.XapiResultData<>();
        if ("TDA_R_001".equalsIgnoreCase(instance.getLabel())) {
            tdaReceive001.tdaReceiveModel(instance, context, dataRow.getMainData(), resultData);
        }
        if ("TDA_R_002".equalsIgnoreCase(instance.getLabel())) {
            tdaReceive002.tdaReceiveModel(instance, context, dataRow.getMainData(), resultData);
        }
        return resultData;
    }

    @Override
    public XapiResult<Object> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<Object> xapiResult = XapiResult.creat();
        XapiUtil.dataCount(data.getData().size());
        if ("TDA_R_001".equals(instance.getObjectId()) || "TDA_R_002".equals(instance.getObjectId())) {
            data.getData().forEach((d) -> {
                XapiResult.XapiResultData<Object> resultData = Insert(instance, context, d);
                if (!resultData.isSuccess()) {
                    xapiResult.addErrorData(resultData);
                    xapiResult.setMessage(resultData.getMessage());
                } else {
                    xapiResult.addRightData(d);
                }
            });
        } else {
            data.getData().forEach((d) -> {
                XapiUtil.dataCountDown();
                XapiResult.XapiResultData<Object> resultData = this.Insert(instance, context, d);
                if (!resultData.isSuccess()) {
                    xapiResult.addErrorData(resultData);
                }

            });
        }
        return xapiResult;
    }

/*    @Override
    public XapiReceiveResponse responseResult(XapiInstance instance, XapiMap<String, Object> context, XapiResult<Object> error) {
        XapiReceiveResponse response = XapiReceiveResponse.creat();
        if ("TDA_R_001".equals(instance.getObjectId()) ) {
            XapiReceiveResponseMessage messResponse = XapiReceiveResponseMessage.creat();
            if (error == null) {
                messResponse.setCode("-1");
                messResponse.setMsg("Error");
            }
            if (error.getErrorData().size() > 0) {
                messResponse.setCode("1");
                error.getErrorData().forEach((e) -> {
                    messResponse.addErrors(e.getId(), e.getMessage());
                });
                //return response;
            }
            if (error.getRightData().size() > 0) {
                // messResponse.setCode("0");
                error.getRightData().forEach((e) -> {
                    messResponse.addSuccess(e.getMainData().get("activityId"), null, null);
                });
            }
            return messResponse;
        } else {
            if (error == null) {
                response.setCode("-1");
                response.setMsg("Error");
            }
            if (error.getErrorData().size() > 0) {
                response.setCode("1");
                error.getErrorData().forEach((e) -> {
                    response.add(e.getId(), e.getMessage());
                });
                //return response;
            }
        }
        return response;
    }*/

    @Override
    public XapiData convertData(XapiInstance instance, XapiMap<String, Object> context, Object data) {
        XapiData xData = XapiData.creat();
        List<XapiTable> childTables = context.getValue("childTables");
        String json = (String) data;
        XapiReceiveRequest requestObj = JSONObject.parseObject(json, XapiReceiveRequest.class);
        if (requestObj.getHead().containsKey("serialnum")) {
            String serialnum = String.valueOf(requestObj.getHead().get("serialnum"));
            XapiLog loginfo = context.getValue("loginfo");
            loginfo.setSerialNum(serialnum);
        }

        List<Map<String, Object>> body = requestObj.getBody();
        body.forEach((m) -> {
            XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
            dr.setMainData(m);
            //   m.put()
             /*   childTables.forEach((n) -> {
                    String firstFlag = n.getSubFlag();
                    List<Map<String, Object>> childdata = (List) m.get(firstFlag);
                    dr.addChild(n, childdata);
                    m.remove(firstFlag);
                });*/
            xData.add(dr);
        });
        return xData;
    }
}
