package com.ly.adp.xapi.api.baseEntity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;

@ApiModel("物料pno12拉取接口请求参数")
public class Pno12RequestEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/*平台*/ 
	private String  platForm;
	/*车型名称*/
	private String  vehicletypeDesc;
	/*PNO18*/
	private String  pno12;
	/*开始变更时间*/
	private String  updateB;
	/*开始变更时间*/
	private String  updateD;
	public String getPlatForm() {
		return platForm;
	}
	public void setPlatForm(String platForm) {
		this.platForm = platForm;
	}
	public String getVehicletypeDesc() {
		return vehicletypeDesc;
	}
	public void setVehicletypeDesc(String vehicletypeDesc) {
		this.vehicletypeDesc = vehicletypeDesc;
	}
	public String getPno12() {
		return pno12;
	}
	public void setPno12(String pno12) {
		this.pno12 = pno12;
	}
	public String getUpdateB() {
		return updateB;
	}
	public void setUpdateB(String updateB) {
		this.updateB = updateB;
	}
	public String getUpdateD() {
		return updateD;
	}
	public void setUpdateD(String updateD) {
		this.updateD = updateD;
	}
	
	
}
