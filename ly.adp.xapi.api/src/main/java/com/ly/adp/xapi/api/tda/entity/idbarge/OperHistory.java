package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 操作历史
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class OperHistory implements Serializable {

    private static final long serialVersionUID = 5145404096667782329L;

    /**
     * 开机事件记录
     */
    @JsonProperty("power_on")
    private PowerOn powerOn;

    /**
     * 关机事件记录
     */
    @JsonProperty("power_off")
    private PowerOff powerOff;

    /**
     * 充电事件
     */
    @JsonProperty("charge")
    private List<Charge> charge;

    /**
     * 录音模式切换
     */
    @JsonProperty("signal_sets")
    private List<SignalSet> signalSets;

    @JsonProperty("date_record")
    private List<DateRecord> dateRecord;

    public PowerOn getPowerOn() {
        return powerOn;
    }

    public void setPowerOn(PowerOn powerOn) {
        this.powerOn = powerOn;
    }

    public PowerOff getPowerOff() {
        return powerOff;
    }

    public void setPowerOff(PowerOff powerOff) {
        this.powerOff = powerOff;
    }

    public List<Charge> getCharge() {
        return charge;
    }

    public void setCharge(List<Charge> charge) {
        this.charge = charge;
    }

    public List<SignalSet> getSignalSets() {
        return signalSets;
    }

    public void setSignalSets(List<SignalSet> signalSets) {
        this.signalSets = signalSets;
    }

    public List<DateRecord> getDateRecord() {
        return dateRecord;
    }

    public void setDateRecord(List<DateRecord> dateRecord) {
        this.dateRecord = dateRecord;
    }
}
