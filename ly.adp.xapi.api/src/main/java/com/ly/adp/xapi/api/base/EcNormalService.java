package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ly.adp.xapi.api.base.mapper.IEcNormalMapper;
import com.ly.adp.xapi.api.baseEntity.*;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.helper.RedisLockUtil;
import com.ly.mp.component.helper.StringHelper;
import com.szlanyou.common.redis.util.RedisUtil;
import jodd.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/21
 */
@NormalService("ec_adp")
public class EcNormalService extends NormalDataGenericBase {
    private static final Logger log = LoggerFactory.getLogger(EcNormalService.class);

    public static final int ORDER_NUMBER_EXPIRE_SECONDS_DAY = 86400;


    final IEcNormalMapper ecNormalMapper;
    final RedisUtil redisUtil;
    final RedisLockUtil redisLockUtil;

    public EcNormalService(IEcNormalMapper ecNormalMapper, RedisUtil redisUtil, RedisLockUtil redisLockUtil) {
        this.ecNormalMapper = ecNormalMapper;
        this.redisUtil = redisUtil;
        this.redisLockUtil = redisLockUtil;

    }

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> map) {
        map.put("stock_vehicle_query", this::stockVehicleQuery);
        map.put("stock_vehicle_update", this::stockVehicleUpdate);
        map.put("stock_vehicle_query_code", this::stockVehicleQueryCode);
    }

    /**
     * 批售查询状态
     * @param xapiInstance
     * @param stringObjectXapiMap
     * @param normalParam
     * @return
     */
    private NormalResult<Map<String, Object>> stockVehicleQueryCode(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, NormalParam<String> normalParam) {
        String data = normalParam.getData();
        log.info("stock_vehicle_query_code参数：{}", data);
        HashMap<String, Object> responseMsg = Maps.newHashMap();
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        if (StringHelper.IsEmptyOrNull(data)) {
            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.DATA_ISNOTEMPTY);
            responseMsg.put("errMsg", EcSaleOrderConstant.DATA_ISNOTEMPTY);
            result.setData(responseMsg);
            result.setSuccess(false);
            result.setData(responseMsg);
            return result;
        }
        EcStockVehicleCodeRequest ecStockVehicleCode = JSONObject.parseObject(data, EcStockVehicleCodeRequest.class);
        if (StringUtil.isEmpty(ecStockVehicleCode.getCode())) {
            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.CODE_ISNOTEMPTY);
            responseMsg.put("errMsg", EcSaleOrderConstant.CODE_ISNOTEMPTY);
            result.setData(responseMsg);
            result.setSuccess(false);
            result.setData(responseMsg);
            return result;
        }
        EcStockVehicleCodeBo ecStockVehicle = ecNormalMapper.ecStockVehicleCodeQuery(ecStockVehicleCode);
        if (StringHelper.IsEmptyOrNull(ecStockVehicle)) {
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
            responseMsg.put("errMsg", EcSaleOrderConstant.BATCH_SALE_DATA_RESULT);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        EcStockVehicleCodeResponse ecStockVehicleResponse = new EcStockVehicleCodeResponse();
        //根据vehicleType车辆类型4批售展车现车去区分
        if (CarVehicleTypeEnum.CAR_VEHICLE_TYPE_WHOLESALE.getCode().equals(ecStockVehicle.getVehicleType().toString())) {
            return wholesale(result, ecStockVehicleResponse, responseMsg, ecStockVehicleCode.getCode());
        }
        BeanUtils.copyProperties(ecStockVehicle, ecStockVehicleResponse);
        if (ecStockVehicleResponse.getSalesStatus().compareTo(0) == 0 && StringHelper.IsEmptyOrNull(ecStockVehicleResponse.getDiscountAmount())) {
            ecStockVehicleResponse.setSalesStatus(2);
        }
        responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
        responseMsg.put("data", ecStockVehicleResponse);
        responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
        result.setSuccess(true);
        result.setData(responseMsg);
        return result;

    }

    private NormalResult<Map<String, Object>> wholesale(NormalResult<Map<String, Object>> result,EcStockVehicleCodeResponse ecStockVehicleResponse,
                                                        HashMap<String, Object> responseMsg,String code) {
        EcStockVehicleCodeBo ecStockVehicleCodeBo = ecNormalMapper.queryecStockVehicleCode(code);
        if (ecStockVehicleCodeBo == null) {
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
            responseMsg.put("errMsg", EcSaleOrderConstant.BATCH_SALE_DATA_RESULT);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        Integer salesStatus = ecStockVehicleCodeBo.getSalesStatus();
        if (Objects.nonNull(ecStockVehicleCodeBo.getSalesStatus()) && SalesStatusEnum.NO_SALES.getCode().equals(salesStatus)) {
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
            responseMsg.put("errMsg", EcSaleOrderConstant.BATCH_SALE_SHELF_NO);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        BeanUtils.copyProperties(ecStockVehicleCodeBo, ecStockVehicleResponse);
        responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
        responseMsg.put("data", ecStockVehicleResponse);
        responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
        result.setSuccess(true);
        result.setData(responseMsg);
        return result;
    }
    public NormalResult<Map<String, Object>> stockVehicleQuery(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        String data = param.getData();
        log.info("stock_vehicle_query参数：{}", data);
        HashMap<String, Object> responseMsg = Maps.newHashMap();
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        if (StringHelper.IsEmptyOrNull(data)) {
            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.DATA_ISNOTEMPTY);
            responseMsg.put("errMsg", EcSaleOrderConstant.DATA_ISNOTEMPTY);
            result.setData(responseMsg);
            result.setSuccess(false);
            result.setData(responseMsg);
            return result;
        }

        EcStockVehicleRequest ecStockVehicleQuery = JSONObject.parseObject(data, EcStockVehicleRequest.class);
        if (StringUtil.isEmpty(ecStockVehicleQuery.getVin()) && StringHelper.IsEmptyOrNull(ecStockVehicleQuery.getVins())) {
            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.VIN_ISNOTEMPTY);
            responseMsg.put("errMsg", EcSaleOrderConstant.VIN_ISNOTEMPTY);
            result.setData(responseMsg);
            result.setSuccess(false);
            result.setData(responseMsg);
            return result;
        }
        if (StringUtil.isNotEmpty(ecStockVehicleQuery.getVin())) {
            EcStockVehicleBo ecStockVehicle = ecNormalMapper.ecStockVehicleQuery(ecStockVehicleQuery);
            if (StringHelper.IsEmptyOrNull(ecStockVehicle)) {
                    responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
                    responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
                    responseMsg.put("errMsg", EcSaleOrderConstant.SUCCESS);
                    result.setSuccess(true);
                    result.setData(responseMsg);
                    return result;
            }
            EcStockVehicleResponse ecStockVehicleResponse = new EcStockVehicleResponse();
            //根据vehicleType车辆类型4批售展车现车去区分
            if (CarVehicleTypeEnum.CAR_VEHICLE_TYPE_WHOLESALE.getCode().equals(ecStockVehicle.getVehicleType().toString())) {
                return wholesale(result, ecStockVehicleResponse, responseMsg, ecStockVehicleQuery.getVin());
            }
            BeanUtils.copyProperties(ecStockVehicle, ecStockVehicleResponse);
            if (ecStockVehicleResponse.getSalesStatus().compareTo(0) == 0 && StringHelper.IsEmptyOrNull(ecStockVehicleResponse.getDiscountAmount())) {
                ecStockVehicleResponse.setSalesStatus(2);
            }
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("data", ecStockVehicleResponse);
            responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        List<EcStockVehicleResponse> otdStockVehicle = ecNormalMapper.otdStockVehicleQuery(ecStockVehicleQuery);
        responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
        responseMsg.put("data", otdStockVehicle);
        responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
        responseMsg.put("errMsg", "");
        result.setSuccess(true);
        result.setData(responseMsg);
        return result;

    }

    private NormalResult<Map<String, Object>> wholesale(NormalResult<Map<String, Object>> result,EcStockVehicleResponse ecStockVehicleResponse,
                                                        HashMap<String, Object> responseMsg,String vin) {
        EcStockVehicleBo ecStockVehicleBo = ecNormalMapper.ecStockVehicleQueryByVin(vin);
        if (ecStockVehicleBo == null) {
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
            responseMsg.put("errMsg", EcSaleOrderConstant.BATCH_SALE_DATA_RESULT);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        Integer salesStatus = ecStockVehicleBo.getSalesStatus();
        if (Objects.nonNull(ecStockVehicleBo.getSalesStatus()) && SalesStatusEnum.NO_SALES.getCode().equals(salesStatus)) {
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
            responseMsg.put("errMsg", EcSaleOrderConstant.BATCH_SALE_SHELF_NO);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        BeanUtils.copyProperties(ecStockVehicleBo, ecStockVehicleResponse);
        responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
        responseMsg.put("data", ecStockVehicleResponse);
        responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
        result.setSuccess(true);
        result.setData(responseMsg);
        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    public NormalResult<Map<String, Object>> stockVehicleUpdate(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        String data = param.getData();
        log.info("stock_vehicle_update参数：{}", data);
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        HashMap<String, Object> responseMsg = Maps.newHashMap();
        if (StringUtil.isEmpty(data)) {
            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.DATA_ISNOTEMPTY);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;

        }
        EcstockVehicleUpdateRequest updateRequest = JSONObject.parseObject(data, EcstockVehicleUpdateRequest.class);

        EcStockVehicleBo ecStockVehicle = ecNormalMapper.findStockVehicle(updateRequest);
        if (StringHelper.IsEmptyOrNull(ecStockVehicle)) {
            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", updateRequest.getVin() + "不存在");
            result.setData(responseMsg);
            result.setSuccess(false);
            result.setData(responseMsg);
            return result;

        }
        // SaleNode=2 退款 直接返回
        if ("2".equals(updateRequest.getSaleNode()) && "2".equals(ecStockVehicle.getSalesStatus().toString())) {
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", "接收成功");
            result.setData(responseMsg);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }

        if (!"0".equals(updateRequest.getSaleNode()) && !"1".equals(ecStockVehicle.getSalesStatus().toString())) {

            responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.SALES_STATUS_FAILED);
            result.setData(responseMsg);
            result.setSuccess(false);
            result.setData(responseMsg);
            return result;
        }
        if ("0".equals(updateRequest.getSaleNode())) {
            // 校验是否锁单
            if (!redisLockUtil.lock(updateRequest.getVin(), updateRequest.getVin(), ORDER_NUMBER_EXPIRE_SECONDS_DAY)) {
                responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
                responseMsg.put("msg", updateRequest.getVin() + "已被锁单");
                result.setData(responseMsg);
                result.setSuccess(false);
                result.setData(responseMsg);
                return result;
            }
            if (!"0".equals(ecStockVehicle.getSalesStatus().toString())) {
                responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
                responseMsg.put("msg", updateRequest.getVin() + EcSaleOrderConstant.STOCK_VEHICLE_FAILED);
                result.setData(responseMsg);
                result.setSuccess(false);
                result.setData(responseMsg);
                redisLockUtil.unlock(updateRequest.getVin(), updateRequest.getVin());
                return result;
            }
            if (StringUtil.isEmpty(updateRequest.getSaleOrder())) {
                responseMsg.put("code", EcSaleOrderConstant.FAILED_CODE);
                responseMsg.put("msg", updateRequest.getVin() + "的订单号不能为空");
                result.setData(responseMsg);
                result.setSuccess(false);
                result.setData(responseMsg);
                redisLockUtil.unlock(updateRequest.getVin(), updateRequest.getVin());
                return result;
            }
            //  boolean b = redisUtil.hasKey(updateRequest.getVin());
            //if (redisUtil.hasKey(updateRequest.getVin()) && "0".equals(updateRequest.getSaleNode())) {
            updateRequest.setSaleNode(EcSaleNodeEnum.EC_SALE_NODE_CREATE.getCode());
            int update = ecNormalMapper.updateStockVehicle(updateRequest);
            if (update <= 0) {
                redisLockUtil.unlock(updateRequest.getVin(), updateRequest.getVin());
                throw new BusicenException("修改现车池失败");
            }
            redisUtil.incr(updateRequest.getVin(), ORDER_NUMBER_EXPIRE_SECONDS_DAY);
            responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
            responseMsg.put("msg", EcSaleOrderConstant.CREATE_SUCCESS);
            result.setData(responseMsg);
            result.setSuccess(true);
            result.setData(responseMsg);
            return result;
        }
        updateRequest.setSaleNode(EcSaleNodeEnum.getEcSaleNodeEnum(updateRequest.getSaleNode()));
        int update = ecNormalMapper.updateStockVehicle(updateRequest);
        if (update <= 0) {
            throw new BusicenException("修改现车池失败");
        }
        // redisUtil.del(updateRequest.getVin());
        redisLockUtil.unlock(updateRequest.getVin(), updateRequest.getVin());
        responseMsg.put("code", EcSaleOrderConstant.SUCCESS_CODE);
        responseMsg.put("msg", EcSaleOrderConstant.SUCCESS);
        result.setData(responseMsg);
        result.setSuccess(true);
        result.setData(responseMsg);
        return result;
    }
}


