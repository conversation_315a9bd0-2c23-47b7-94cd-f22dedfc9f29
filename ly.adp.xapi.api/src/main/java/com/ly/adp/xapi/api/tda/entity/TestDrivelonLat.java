package com.ly.adp.xapi.api.tda.entity;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 试驾经纬度接收
 * @date 2023/8/7
 */
public class TestDrivelonLat implements Serializable {

    private static final long serialVersionUID = -8473742187106234917L;

    @ApiModelProperty("试驾单号")
    @NotNull(message = "试驾单号不能为空")
    private String testDriveOrderNo;

    @ApiModelProperty("经纬度集合")
    private List<Data> data;

    public static class Data {

        @ApiModelProperty("经度")
        @NotNull(message = "经度不能为空")
        private String lon;

        @ApiModelProperty("纬度")
        @NotNull(message = "纬度不能为空")
        private String lat;

        @ApiModelProperty("时间戳")
        @NotNull(message = "时间戳不能为空")
        private Integer time;

        public String getLon() {
            return lon;
        }

        public void setLon(String lon) {
            this.lon = lon;
        }

        public String getLat() {
            return lat;
        }

        public void setLat(String lat) {
            this.lat = lat;
        }

        public Integer getTime() {
            return time;
        }

        public void setTime(Integer time) {
            this.time = time;
        }
    }

    public String getTestDriveOrderNo() {
        return testDriveOrderNo;
    }

    public void setTestDriveOrderNo(String testDriveOrderNo) {
        this.testDriveOrderNo = testDriveOrderNo;
    }

    public List<Data> getData() {
        return data;
    }

    public void setData(List<Data> data) {
        this.data = data;
    }
}