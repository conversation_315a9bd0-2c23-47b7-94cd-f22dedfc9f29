package com.ly.adp.xapi.api.acc;

import com.alibaba.fastjson.JSONObject;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.pull.PullDataBase;
import com.ly.bucn.component.xapi.service.pull.PullService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

@PullService("activity_adp")
public class ActivityPullService extends PullDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(ActivityPullService.class);

    @Override
    public Object serializeParam(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, XapiMap<String, Object> stringObjectXapiMap2) {
        return null;
    }

    public XapiResult.XapiResultData<String> Insert(XapiInstance instance, XapiMap<String, Object> context, XapiData.XapiDataRow dataRow) {
        XapiTable mainTable = context.getValue("mainTable");
        mainTable.getFields().stream().filter(f -> "jsonList".equals(f.getDbType())).filter(f -> dataRow.getMainData().get(f.getSqlParaName()) != null).forEach(f -> {
            Object v = dataRow.getMainData().get(f.getSqlParaName());
            String json = JSONObject.toJSONString(v);
            dataRow.getMainData().put(f.getSqlParaName(), json);
        });
        return super.Insert(instance, context, dataRow);
    }

    @Override
    public Object pullData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) throws Exception {
        return ActivityWebUtil.postData(xapiInstance.getSystem().getXapiKv("provide_path").getValueTee() + xapiInstance.getService(), o);
    }

    @Override
    public XapiData deserializeData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) {
        XapiData xapiData = XapiData.creat();
        try {
            String jsonresp = String.valueOf(o);
            Map<String, Object> resp = JSONObject.parseObject(jsonresp, Map.class);
            if (resp.containsKey("code")) {
                if ("success".equals(resp.get("code"))) {
                    if (resp.containsKey("data")) {
                        Map<String, Object> data = (Map<String, Object>) resp.get("data");
                        // listData.forEach(m -> {
                        XapiData.XapiDataRow xapiDataRow = XapiData.XapiDataRow.creat();
                        xapiDataRow.setMainData(data);
                        xapiData.add(xapiDataRow);
                        // });

                    }
                }
            }
        } catch (Exception e) {
            log.error("活动类型获取失败", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        return xapiData;
    }
}
