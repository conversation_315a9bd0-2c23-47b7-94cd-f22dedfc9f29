package com.ly.adp.xapi.api.boot;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

import javax.annotation.Resource;

import com.ly.adp.xapi.api.base.CdpReceiveService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.ly.mp.component.helper.StringHelper;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

@Api(tags = "*自定义接口",description="接收")
@RestController
@RequestMapping("")
public class CspReceiveController {
	@Resource
    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage passiveServiceManage;
//    @Resource
//    private CdpReceiveService cdpReceiveService;

    @Resource
    WhiteListInterceptor whiteListInterceptor;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @ApiOperation("csp接收")
    @ApiImplicitParams({
            @ApiImplicitParam(name="data",value="数据",required=false,dataType="String",paramType="body")}
    )
    @PostMapping("receivesys/csp")
    public Map<String,Object> receiveSys(@RequestBody Map<String,Object> map) {
        Map<String,Object> result = new HashMap<>();
        List<Map<String,Object>> dataList=new ArrayList<>();
        result.put("message","");
        result.put("isSuccess","N");
        try {
            // 判断数据是否为空
            if(!CollectionUtils.isEmpty(map)){
                String data = JSONObject.toJSONString(map);
                Map<String, Object> header = (Map<String, Object>) map.get("head");
                String service = Objects.toString(header.get("service"),"");
                XapiInstance instance = xServiceHolder.getInstance(service);
                // 判断是否有对应的配置
                if(instance!=null){
                    AtomicReference<String> respStr = new AtomicReference<>();
                    passiveServiceManage.handle(instance, data, m -> respStr.set((String) m));
                    result = JSON.parseObject(respStr.get(),Map.class);
                }else {
                    String msg = String.format("接口%s不存在",service);
                    logger.error(msg);
                    result.put("isSuccess","N");
                    result.put("message",msg);
                    dataList.add(result);
                }
            }else{
                result.put("message","请求数据不能为空");
                result.put("isSuccess","N");
                dataList.add(result);
            }
        } catch (Exception e) {
            logger.error("接收ec数据发生异常：{}",e.getMessage(),e);
          /*  XapiResponse resp=new XapiResponse();
            resp.setCode("-1");
            resp.setMsg(e.getMessage());
            result = JSON.parseObject(JSONObject.toJSONString(resp),Map.class);*/
            result.put("message",e.getMessage());
            result.put("isSuccess","N");
            dataList.add(result);
        } finally {
            if(!StringHelper.IsEmptyOrNull(dataList)){
                Map<String,Object> res = new HashMap<>();
                res.put("data",dataList);
                result = res;
            }

        }
        return result;
    }
    @PostMapping("receivesys/testEvent")
    public void receiveSys1(@RequestBody String data) {
        String x = "{\"label\":\"cdp_leads_event\",\"moudle\":\"xapi\",\"objectId\":\"cdp_leads_event\",\"objectName\":\"cdp关键事件接收\",\"objectType\":\"receive\",\"remark\":\"cdp|smart.ex.cdp.leads.event|smart.queue.cdp.leadsEvent.to.adp.leadsEvent\",\"service\":\"MQ\",\"state\":\"1\",\"system\":{\"kvs\":[{\"keyTee\":\"token_url\",\"kvId\":\"cdp_adp_01\",\"state\":\"1\",\"valueTee\":\"/v2/oauth2/token\"}],\"provideAccount\":\"cl039917faf4c4f4f\",\"providePassword\":\"18d989a00c1e6cddb00b9f525abaac6ca8f08dd5\",\"providePath\":\"http://cdp-api-up.smartchina.com.cn\",\"remark\":\"\",\"systemId\":\"cdp_adp\"},\"tables\":[{\"executeSql\":\"insert into csc.t_sac_onecust_info_event(INFO_EVENT_ID,EVENT_CODE,EVENT_NAME,MOBILE,EVENT_TIME,SMART_ID,CREATOR,CREATED_DATE,MODIFIER,LAST_UPDATED_DATE,IS_ENABLE,UPDATE_CONTROL_ID) values (UUID(),:event_code,:event_name,:mobile,:event_time,:c_smartid,'cdp_leads_event',now(),'cdp_leads_event',now(),'1',uuid())\\n\",\"fields\":[{\"fieldId\":\"cdp_leads_event_01_01\",\"isPrimaryKey\":\"\",\"sqlParaName\":\"logsId\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"tableName\":\"t_sac_onecust_info_event\",\"viewField\":\"logsId\"},{\"fieldId\":\"cdp_leads_event_01_02\",\"isPrimaryKey\":\"1\",\"sqlParaName\":\"event_code\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"viewField\":\"event_code\"},{\"fieldId\":\"cdp_leads_event_01_03\",\"sqlParaName\":\"event_name\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"viewField\":\"event_name\"},{\"fieldId\":\"cdp_leads_event_01_04\",\"sqlParaName\":\"mobile\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"viewField\":\"mobile\"},{\"fieldId\":\"cdp_leads_event_01_05\",\"sqlParaName\":\"event_time\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"viewField\":\"event_time\"},{\"fieldId\":\"cdp_leads_event_01_06\",\"sqlParaName\":\"c_smartid\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"viewField\":\"c_smartid\"}],\"isMainTable\":\"1\",\"mlRefField\":\"null\",\"objectId\":\"cdp_leads_event\",\"state\":\"1\",\"tableId\":\"cdp_leads_event_01\",\"tableName\":\"t_sac_onecust_info_event\"}]}";
        XapiInstance xapiInstance = JSONObject.parseObject(x, XapiInstance.class);
        String c = "{\"event_code\":\"mini_program_open\",\"c_smartid\":\"\",\"mobile\":\"13061350403\",\"event_name\":\"垂媒留资\",\"event_time\":\"2025-04-08 11:16:18\"}";
        //cdpReceiveService.handle(xapiInstance, data);
    }
}
