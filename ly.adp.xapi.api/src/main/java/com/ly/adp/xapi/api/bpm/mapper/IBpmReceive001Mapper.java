package com.ly.adp.xapi.api.bpm.mapper;

import com.ly.adp.xapi.api.tda.entity.LookupValue;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/20
 */
public interface IBpmReceive001Mapper {
    void inserBpmReceive001His(Map<String, Object> mainData);

    Map<String, Object> findSubsidy(Map paramMap);

    Map<String, Object> findSubsidyD(Map paramMap);

    void insRApproval(Map<String, Object> paramMap);

    void updateStatus(Map paramMap);

    void updateSubsidy(Map paramMap);

    void updateSupportDetail(Map paramMap);

    void updateSupport(Map paramMap);

    void insSapApproval(Map paramMap);

    void insSapApprovalData(Map paramMap);

    Map<String, Object> findVirtualPhone(Map<String, Object> paramMap);

    void updateVirtual(Map<String, Object> paramMap);

    void insertVirtual(Map<String, Object> paramMap);

    Map<String, Object> findEmpMsg(Map<String, Object> paramMaps);

    Map<String, Object> findOnecustInfo(Map<String, Object> paramMaps);

    int insertVirtualRecord(Map<String, Object> paramMaps);

    List<LookupValue> findLookUpValue();
}
