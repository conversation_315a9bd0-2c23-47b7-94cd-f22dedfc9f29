package com.ly.adp.xapi.api.bpm;


import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.BpmWebUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @description: BPM发送
 * @date 2022/10/19
 */

@SendService("BPM_ADP")
public class BpmSendService extends SendDataBaseDecorator<Object>{
    private static final Logger log = LoggerFactory.getLogger(BpmSendService.class);

    @Override
    public XapiData improveColumn(XapiInstance instance, XapiMap<String, Object> context, XapiData xData) {
        return super.improveColumn(instance, context, xData);
    }

    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> context, Object arg2) {
        String url = xapiInstance.getSystem().getProvidePath() + xapiInstance.getService();
        HttpHeaders httpHeaders = new HttpHeaders();
        if ("BPM_S_001".equals(xapiInstance.getLabel())) {
            List<String> lists = JSONObject.parseArray(arg2.toString(), String.class);
            List<String> list = new ArrayList<>();
            for (String s : lists) {
                String post = BpmWebUtil.post(url, s, httpHeaders, xapiInstance.getSystem().getProvideAccount(), xapiInstance.getSystem().getProvidePassword());
                list.add(post);
            }
            return list;
        }
        return BpmWebUtil.post(url, arg2.toString(), httpHeaders, xapiInstance.getSystem().getProvideAccount(), xapiInstance.getSystem().getProvidePassword());
    }

    @Override
    public XapiResult<Object> manageResp(XapiInstance arg0, XapiMap<String, Object> arg1, Object arg2) {
        if ("BPM_S_001".equals(arg0.getLabel())) {
            return manageRespBPM(arg0, arg1, arg2);
        }
        XapiResult<Object> creat = XapiResult.creat();
        return creat;
    }

    public XapiResult<Object> manageRespBPM(XapiInstance arg0, XapiMap<String, Object> arg1, Object arg2) {
        log.info("arg2 :: " + arg2);
        List<String> args2 = (List<String>) arg2;
        XapiResult<Object> creat = XapiResult.creat();
        // 异常,将对应状态改为4
        if (args2 == null) {
            //执行异常语句 优先级最高
            creat.setStatus(1);
            creat.setMessage("服务访问出错");
            return creat;
        }
        List<String> strings = (List<String>) arg1.get("errorIds");
        for (int i = 0; i < strings.size(); i++) {
            // 错误，将对应状态改为3
            Rsp rsp = JSONObject.parseObject(args2.get(i), Rsp.class);
            String msg = rsp.getMessage();
            if (msg != null && msg.length() > 1000) {
                msg = msg.substring(0, 1000);
            }
            if (!"1".equals(rsp.getCode())) {
                XapiResult.XapiResultData errorData = new XapiResult.XapiResultData<>(strings.get(i), msg);
                errorData.setSuccess(false);
                creat.addErrorData(errorData);
            }
        }
        return creat;

    }


    /**
     * 返回对象
     */
    public static class Rsp {
        String message;
        String requestCode;
        String code;

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getRequestCode() {
            return requestCode;
        }

        public void setRequestCode(String requestCode) {
            this.requestCode = requestCode;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }
    }
}
