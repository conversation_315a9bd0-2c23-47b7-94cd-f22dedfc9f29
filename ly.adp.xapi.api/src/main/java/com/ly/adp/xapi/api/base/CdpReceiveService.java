package com.ly.adp.xapi.api.base;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.mapper.CdpResMapper;
import com.ly.adp.xapi.api.boot.XapiApiService;
import com.ly.adp.xapi.api.clue.feign.AgentClueFeign;
import com.ly.adp.xapi.api.clue.feign.ClueEventFlowRsp;
import com.ly.adp.xapi.api.clue.feign.QueryUserEventFlowReq;
import com.ly.adp.xapi.api.clue.feign.RespBody;
import com.ly.adp.xapi.api.ms.AdpMsRunner;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.*;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import com.ly.bucn.component.xapi.service.receive.ReceiveService;
import com.ly.bucn.component.xapi.util.XapiUtil;
import com.ly.mp.busicen.common.helper.SpringContextHolder;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

import org.springframework.transaction.PlatformTransactionManager;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/9/12
 */
@ReceiveService("cdp_adp")
public class CdpReceiveService extends ReceiveDataBase<Object> {


    private static final Logger log = LoggerFactory.getLogger(CdpReceiveService.class);

    @Resource
    IXapiLogBiz xapiLogBiz;

    @Autowired
    CdpResMapper cdpResMapper;

    @Resource
    private AgentClueFeign agentClueFeign;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    private RedisScript<Long> rateLimitScript;
    private static final ZoneId BUSINESS_ZONE = ZoneId.of("Asia/Shanghai");
    private static final String CONFIG_KEY = "event:rate_config";

    final PlatformTransactionManager transactionManager;
    final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    public CdpReceiveService(PlatformTransactionManager transactionManager,
                             NamedParameterJdbcTemplate namedParameterJdbcTemplate
    ) {
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
        this.transactionManager = transactionManager;
    }

    // 类成员变量 ↓
    private static final List<String> EVENT_CODE_EXCLUDE = Collections.unmodifiableList(
            Arrays.asList("mini_program_open", "mini_program_resign_active", "open_mobile_app",
                    "mobile_app_resign_active", "c_aiche_wishlist", "c_fin_my", "c_aiche_home",
                    "c_basic_config", "c_fin_exterior", "c_fin_interior", "c_fin_package")
    );

    public boolean handle(XapiInstance instance, Object data, Consumer<String> consumer) {

        XapiMap<String, Object> context = new XapiMap<>();
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(instance.getLabel());
        loginfo.setObjectType(instance.getObjectType());
        loginfo.setLogTime(new Date());
        loginfo.setLogStatus("0");
        context.put("loginfo", loginfo);

        XapiReceiveResponse receiveResponse = XapiReceiveResponse.creat();

        XapiResult<Object> insertResult = XapiResult.creat();

        try {
            init(instance, context);
            if (!checkConfig(instance, context)) {
                log.error(context.get("checkMessage").toString());
                log.error("停止接收任务:{}", instance.getLabel());
                throw new Exception(context.<String>getValue("checkMessage"));
            }
            XapiData xdata = convertData(instance, context, data);
            XapiData ximprodata = improveColumn(instance, context, xdata);
            insertResult = this.insertData(instance, context, ximprodata);

        } catch (Exception e) {
            receiveResponse.setCode("-1");
            receiveResponse.setMsg(e.getMessage());
            loginfo.setLogMessage(e.getMessage());
            loginfo.setLogStatus("-1");

            log.error("接收调用异常", e);
        } finally {
            Map<String, Object> resultData = new HashMap<>();
            List<Map<String, Object>> mess = new ArrayList<>();

            insertResult.getErrorData().forEach(m -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", m.getId());
                map.put("isSuccess", m.isSuccess() ? "Y" : "N");
                map.put("message", m.isSuccess() ? "" : m.getMessage());
                mess.add(map);
            });
            resultData.put("data", mess);
            String jsonresp = JSONObject.toJSONString(resultData);
            consumer.accept(jsonresp);
            try {
                loginfo.setOutJson(jsonresp);
                loginfo.setInJson(data.toString());
                xapiLogBiz.add(loginfo);
            } catch (Exception ignored) {
            }
        }

        return true;
    }

    public boolean handle(XapiInstance instance, Object data) {

        XapiMap<String, Object> context = new XapiMap<>();
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel(instance.getLabel());
        loginfo.setObjectType(instance.getObjectType());
        loginfo.setLogTime(new Date());
        loginfo.setLogStatus("0");
        context.put("loginfo", loginfo);

        XapiReceiveResponse receiveResponse = XapiReceiveResponse.creat();

        XapiResult<Object> insertResult = XapiResult.creat();

        try {
            init(instance, context);
            if (!checkConfig(instance, context)) {
                log.error(context.get("checkMessage").toString());
                log.error("停止接收任务:{}", instance.getLabel());
                throw new Exception(context.<String>getValue("checkMessage"));
            }
            XapiData xdata = convertData(instance, context, data);
            XapiData ximprodata = improveColumn(instance, context, xdata);
            insertResult = this.insertData(instance, context, ximprodata);
        } catch (Exception e) {
            receiveResponse.setCode("-1");
            receiveResponse.setMsg(e.getMessage());
            loginfo.setLogMessage(e.getMessage());
            loginfo.setLogStatus("-1");

            log.error("接收调用异常", e);
        } finally {
            Map<String, Object> resultData = new HashMap<>();
            List<Map<String, Object>> mess = new ArrayList<>();

            insertResult.getErrorData().forEach(m -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", m.getId());
                map.put("isSuccess", m.isSuccess() ? "Y" : "N");
                map.put("message", m.isSuccess() ? "" : m.getMessage());
                mess.add(map);
            });
            resultData.put("data", mess);
            String jsonresp = JSONObject.toJSONString(resultData);
            try {
                loginfo.setOutJson(jsonresp);
                loginfo.setInJson(data.toString());
                xapiLogBiz.add(loginfo);
            } catch (Exception ignored) {
            }
        }

        return true;
    }

    public XapiData convertData(XapiInstance instance, XapiMap<String, Object> context, Object data) {
        XapiData xData = XapiData.creat();
        List<XapiTable> childTables = (List) context.getValue("childTables");
        //通过mq获取的报文
        if (AdpMsRunner.isMqPublish()) {
            JSONArray jsonArray = new JSONArray();
            JSONObject jsonbody = new JSONObject();
            String j = (String) data;

            if ("[".equals(j.substring(0, 1))) {
                jsonArray = JSONObject.parseArray((String) data);
            } else {
                jsonbody = JSONObject.parseObject((String) data);
            }
            JSONObject newjson = new JSONObject();
            if (jsonArray != null && jsonArray.size() > 0) {
                newjson.put("body", jsonArray);
            } else {
                newjson.put("body", jsonbody);
            }
            String json = JSONObject.toJSONString(newjson);

            XapiReceiveRequest requestObj = JSONObject.parseObject(json, XapiReceiveRequest.class);
            List<Map<String, Object>> body = requestObj.getBody();
            body.forEach((m) -> {
                XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                dr.setMainData(m);
                childTables.forEach((n) -> {
                    String firstFlag = n.getSubFlag();
                    List<Map<String, Object>> childdata = (List) m.get(firstFlag);
                    dr.addChild(n, childdata);
                    m.remove(firstFlag);
                });
                xData.add(dr);
            });
        } else {
            String json = (String) data;
            XapiReceiveRequest requestObj = JSONObject.parseObject(json, XapiReceiveRequest.class);
            if (requestObj.getHead().containsKey("serialnum")) {
                String serialnum = String.valueOf(requestObj.getHead().get("serialnum"));
                XapiLog loginfo = context.getValue("loginfo");
                loginfo.setSerialNum(serialnum);
            }

            List<Map<String, Object>> body = requestObj.getBody();
            body.forEach((m) -> {
                XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
                dr.setMainData(m);
                childTables.forEach((n) -> {
                    String firstFlag = n.getSubFlag();
                    List<Map<String, Object>> childdata = (List) m.get(firstFlag);
                    dr.addChild(n, childdata);
                    m.remove(firstFlag);
                });
                xData.add(dr);
            });
        }
        return xData;
    }

    public XapiResult<Object> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<Object> xapiResult = XapiResult.creat();
        // 其他活跃事件每天只允许接收额定条
        XapiUtil.dataCount(data.getData().size());
        data.getData().forEach((d) -> {
            XapiUtil.dataCountDown();
            Boolean allowSaveResult = Boolean.TRUE;
            if ("cdp_leads_event".equals(instance.getLabel())) {
                Map<String, Object> mainData = d.getMainData();
                if (!StringHelper.IsEmptyOrNull(mainData.get("c_smartid"))) {
                    Map<String, Object> custInfo = cdpResMapper.findCustInfo(mainData.get("c_smartid"));//todo
                    String mobile = (String) mainData.get("mobile");
                    // 校验手机号是否为dcc如果是dcc就跳过此次操作
                    log.info("准备获取用户旅程信息先判断是否是地磁场手机号");
                    if (CollectionUtil.isNotEmpty(custInfo) && !isDccMobile(mobile)) {
                        log.info("准备获取用户旅程信息 {}", JSONObject.toJSONString(custInfo));
                        // 添加调用/queryUserEventFlow接口，获取用户旅程，判断是否有下定或者交付的节点
                        QueryUserEventFlowReq queryUserEventFlowReq =
                                new QueryUserEventFlowReq(Arrays.asList(custInfo.get("custId").toString()));
                        log.info("旅程信息参数 {}", JSONObject.toJSONString(queryUserEventFlowReq));
                        System.out.println(agentClueFeign);
                        agentClueFeign.queryUserEventFlow(queryUserEventFlowReq);
                        log.info("请求结果");
                        RespBody<List<ClueEventFlowRsp>> listAgentClueResult = agentClueFeign.queryUserEventFlow(queryUserEventFlowReq);
                        log.info("获取用户旅程信息完成结果 {}", JSONObject.toJSONString(listAgentClueResult));
                        AtomicReference<String> hasDealFlagContent = new AtomicReference<>(null);
                        if (ObjectUtil.isEmpty(listAgentClueResult) || ObjectUtil.isEmpty(listAgentClueResult.getBody())) {
                            hasDealFlagContent.set(null);
                        } else {
                            List<ClueEventFlowRsp> eventFlowRspList = listAgentClueResult.getBody();
                            // 判断结果集合中eventType是否有4或者5的节点，如果有那就是有订单
                            eventFlowRspList.forEach((clueEventFlowRsp) -> {
                                if (clueEventFlowRsp.getEventType().toString().equals("4")) {
                                    hasDealFlagContent.set("【已下定】");
                                } else if (clueEventFlowRsp.getEventType().toString().equals("5")) {
                                    hasDealFlagContent.set("【已交车】");
                                }
                            });
                        }
                        custInfo.put("messageContent",
                                String.format("您的用户【%s-%s】%s产生%s，请您关注",
                                        custInfo.get("custName"),
                                        mainData.get("mobile"),
                                        hasDealFlagContent.get(),
                                        mainData.get("event_name")));
                        custInfo.put("messageId", UUID.randomUUID().toString());
                        custInfo.put("phone", mainData.get("mobile"));
                        custInfo.put("createdDate", mainData.get("event_time"));
                        log.info("接收到的事件: {}", mainData.get("event_code"));
                        if (!EVENT_CODE_EXCLUDE.contains(mainData.get("event_code").toString())) {
                            cdpResMapper.createMsgRecord(custInfo);
                            // 查询专家所在的店长
                            List<Map<String, Object>> storeManager = cdpResMapper.queryStoreManager(custInfo.get("dlrCode"));
                            if (CollectionUtil.isNotEmpty(storeManager)) {
                                log.info("查询到的店长信息: {}", JSONObject.toJSONString(storeManager));
                                String reviewPersonId = custInfo.get("reviewPersonId").toString();
                                storeManager.stream()
                                        .filter(m ->
                                                // 直接过滤掉 empId 等于 reviewPersonId 的条目
                                                !Objects.equals(
                                                        Optional.ofNullable(m.get("empId")).map(Object::toString).orElse(""),
                                                        reviewPersonId
                                                )
                                        )
                                        .forEach((m) -> {
                                            custInfo.put("reviewPersonId", m.get("empId"));
                                            custInfo.put("messageId", UUID.randomUUID().toString());
                                            custInfo.put("messageContent",
                                                    String.format("【专家-%s】的【客户-%s-%s】%s产生%s，请您关注",
                                                            custInfo.get("reviewPersonName"),
                                                            custInfo.get("custName"),
                                                            mainData.get("mobile"),
                                                            hasDealFlagContent.get(),
                                                            mainData.get("event_name")));
                                            cdpResMapper.createMsgRecord(custInfo);
                                        });
                            }
                        }
                    }
                }
                // 如果是几个特殊事件，需要判断是否可以插入，因为每天接收的次数有限制
                if (EVENT_CODE_EXCLUDE.contains(mainData.get("event_code").toString())) {
                    allowSaveResult = shouldProcessEvent(mainData.get("mobile").toString(), mainData.get("event_code").toString());
                    log.info("脚本lua判断结果：{}", allowSaveResult);
                }
            }
            if (allowSaveResult) {
                XapiResult.XapiResultData<Object> resultData = Insert(instance, context, d);
                xapiResult.addErrorData(resultData);
            }
        });
        return xapiResult;
    }

    @PostConstruct
    public void init() {
        this.rateLimitScript = buildRateLimitScript();
    }

    /**
     * 初始化Lua脚本
     * A[开始] --> B{键是否存在?}
     * B -->|不存在| C[初始化哈希表: count=1, 设置过期时间]
     * B -->|存在| D{存储日期=当前日期?}
     * D -->|否| E[重置计数器: count=1, 更新日期并设置新的过期时间]
     * D -->|是| F[计数器递增]
     * C --> G[返回1]
     * E --> G
     * F --> H[返回新计数值]
     */
    private RedisScript<Long> buildRateLimitScript() {
        String luaScript =
                "local key = KEYS[1]\n" +
                        "local currentDate = ARGV[1]\n" +
                        "local expireSeconds = tonumber(ARGV[2])\n" +
                        "local threshold = tonumber(ARGV[3])\n" +

                        // 获取存储的日期和计数
                        "local stored = redis.call('HMGET', key, 'date', 'count')\n" +

                        // 键不存在时的初始化逻辑
                        "if not stored[1] then\n" +
                        "    redis.call('HMSET', key, 'date', currentDate, 'count', 1)\n" +
                        "    redis.call('EXPIRE', key, expireSeconds)\n" +
                        "    return 1\n" +

                        "else\n" +
                        "    -- 日期校验逻辑\n" +
                        "    if stored[1] ~= currentDate then\n" +
                        "        redis.call('HMSET', key, 'date', currentDate, 'count', 1)\n" +
                        "        redis.call('EXPIRE', key, expireSeconds)\n" +
                        "        return 1\n" +

                        "    else\n" +
                        "        -- 强制递增计数器并返回新值\n" +
                        "        local newCount = redis.call('HINCRBY', key, 'count', 1)\n" +
                        "        return newCount\n" +
                        "    end\n" +
                        "end";
        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(luaScript);
        script.setResultType(Long.class);
        return script;
    }

    /**
     * 处理事件入口方法
     *
     * @param mobile    手机号
     * @param eventCode 事件类型
     */
    private boolean shouldProcessEvent(String mobile, String eventCode) {
        try {
            log.info("处理时间入口方法 手机号{}，事件{}", mobile, eventCode);
            // 获取当前日期和阈值配置
            String currentDate = getCurrentDate();
            Integer threshold = getEventThreshold(eventCode);

            // 无需限流的情况
            if (threshold == null || threshold <= 0) {
                return true;
            }

            // 准备脚本参数
            String redisKey = buildRedisKey(eventCode, mobile);
            long expireSeconds = calculateExpireSeconds() + 2; // 增加2秒缓冲

            // 执行Lua脚本
            List<String> keys = Collections.singletonList(redisKey);
            Object[] args = {currentDate, String.valueOf(expireSeconds), String.valueOf(threshold)};
            log.info("执行脚本参数 keys{}, args{}", JSONObject.toJSONString(keys), JSONObject.toJSONString(args));
            Long currentCount = redisTemplate.execute(rateLimitScript, keys, args);
            log.info("执行个数：{}", currentCount);
            // 判断是否允许处理
            return currentCount != null && currentCount <= threshold;
        } catch (Exception e) {
            // 异常处理：记录日志并降级处理（允许通过）
            log.error("限流检查异常 mobile={}, eventCode={}", mobile, eventCode, e);
            return true;
        }
    }

    /**
     * 构建Redis存储Key
     * 格式：eventLimiter:{eventCode}:{mobile}
     */
    private String buildRedisKey(String eventCode, String mobile) {
        return String.format("eventLimiter:%s:%s", eventCode, mobile);
    }

    /**
     * 获取当前日期字符串(yyyyMMdd)
     */
    private String getCurrentDate() {
        return LocalDate.now(BUSINESS_ZONE).format(DateTimeFormatter.BASIC_ISO_DATE);
    }

    /**
     * 计算到当日午夜剩余的秒数
     */
    private long calculateExpireSeconds() {
        LocalDateTime now = LocalDateTime.now(BUSINESS_ZONE);
        LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
        return Duration.between(now, midnight).getSeconds();
    }

    /**
     * 从Redis获取事件阈值配置
     */
    private Integer getEventThreshold(String eventCode) {
        try {
            String threshold = (String) redisTemplate.opsForHash()
                    .get(CONFIG_KEY, eventCode);
            return threshold != null ? Integer.parseInt(threshold) : 1;
        } catch (Exception e) {
            log.error("获取阈值配置失败 eventCode={}", eventCode, e);
            return null;
        }
    }

    /**
     * 置管理方法 更新事件阈值配置
     *
     * @param eventCode 事件类型
     * @param threshold 阈值次数（0表示不限）
     */
    public void modifyThreshold(String eventCode, int threshold) {
        redisTemplate.opsForHash().put(CONFIG_KEY, eventCode, String.valueOf(threshold));
    }

    private boolean isDccMobile(String mobile) {
        return cdpResMapper.isDccClueInfo(mobile);
    }
}
