package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.mapper.CdpResMapper;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;


@NormalService("cdp_adp")
public class CdpNormalService extends NormalDataGenericBase {
    private static final Logger log = LoggerFactory.getLogger(CdpNormalService.class);
    @Autowired
    CdpResMapper cdpResMapper;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
        handlers.put("cdp_adp_onecust_logout", this::cdpNormalinsert);//用户注销cdp同步adp
    }

    @Transactional
    public NormalResult<Map<String, Object>> cdpNormalinsert(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        Map<String, Object> data = new HashMap<>();
        String info = param.getData();
        Map<String, Object> paramMap = JSONObject.parseObject(info, Map.class);

        if (StringHelper.IsEmptyOrNull(paramMap)) {
            responseMassage("0", result, data, "参数不能为空");
            return result;
        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("smartId"))) {
            responseMassage("0", result, data, "smartId不能为空");
            result.setMessage("smartId不能为空");
            return result;

        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("mobile"))) {
            responseMassage("0", result, data, "mobile不能为空");
            result.setMessage("mobile不能为空");
            return result;
        }
        if (StringHelper.IsEmptyOrNull(paramMap.get("eventTime"))) {
            responseMassage("0", result, data, "eventTime不能为空");
            result.setMessage("eventTime不能为空");
            return result;
        }

        try {
            //注销线索直接进入战败删除回访单
            cdpResMapper.deleteReviewInfo(paramMap);

            //客户表和线索表smartId清空
            cdpResMapper.updateCdpCustomers(paramMap);
            cdpResMapper.updateOnecustInfo(paramMap);
            // 先修改老表的状态为战败
            int i = cdpResMapper.updateClueInfo(paramMap);
            if (i > 0) {
                paramMap.put("table", "t_ifr_base_cdp_onecust_logout_his");
            } else {
                paramMap.put("table", "t_ifr_base_cdp_onecust_logout");
            }
            cdpResMapper.insertOnecustLogout(paramMap);
            // 删除新库的数据
            i = cdpResMapper.deleteNewClueDB(paramMap);
            if (i == 0) {
                log.info("更新行数为0 com.ly.adp.xapi.api.base.CdpNormalService.cdpNormalinsert");
            }
        } catch (Exception e) {
            // e.printStackTrace();
            responseMassage("0", result, data, "失败");
        }
        responseMassage("1", result, data, "成功");
        return result;
    }

    private void responseMassage(String code, NormalResult<Map<String, Object>> result, Map<String, Object> map, String errorMsg) {
        map.put("result", code);
        map.put("msg", errorMsg);
        result.setData(map);
        if ("0".equals(code)) {
            result.setSuccess(false);
        } else {
            result.setSuccess(true);
        }
        result.setMessage(errorMsg);
    }

}
