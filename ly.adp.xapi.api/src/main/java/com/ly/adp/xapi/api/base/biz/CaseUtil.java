package com.ly.adp.xapi.api.base.biz;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/9
 */
public class CaseUtil {

    public static Map<String, Object> transMapKeyToUpperCase(Map<String, Object> map) {
        if (map == null || map.size() == 0) {
            return map;
        }

        Map<String, Object> resultMap = new HashMap<>(map.size());
        for(Map.Entry<String, Object> entry : map.entrySet()){
            resultMap.put(entry.getKey().toUpperCase(), entry.getValue());
        }

        return resultMap;
    }
}
