package com.ly.adp.xapi.api.base;

import com.ly.bucn.component.xapi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ldap.NameNotFoundException;
import org.springframework.ldap.core.DirContextAdapter;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.support.LdapNameBuilder;
import org.springframework.stereotype.Service;

import javax.naming.directory.BasicAttribute;
import javax.naming.directory.DirContext;
import javax.naming.directory.ModificationItem;
import javax.naming.ldap.LdapName;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

@Service
public class LdapService {
    @Autowired
    private LdapTemplate ldapTemplate;

    @Value("#{'${spring.ldap.ous:smart-HQ,Vendor}'.split(',')}")
    String[] ous;

    public boolean userExit(String[] oulist, String cn, AtomicReference<LdapName> atomicReference) {
        if (oulist == null) {
            oulist = ous;
        }
        LdapNameBuilder ldapNameBuilder = LdapNameBuilder.newInstance();
        Stream.of(oulist).forEach(n -> {
            ldapNameBuilder.add("ou", n);
        });
        ldapNameBuilder.add("cn", cn);
        LdapName dn = ldapNameBuilder.build();
        atomicReference.set(dn);
        try {
            ldapTemplate.lookup(dn);
        } catch (NameNotFoundException e) {
            return false;
        }
        return true;
    }

    public boolean update(LdapName dn, Map<String, String> data) {
        data.remove("cn");
        if (dn == null) {
            throw new RuntimeException("用户不存在");
        }
        List<ModificationItem> list = new ArrayList<>();
        data.forEach((k, v) -> {
            Object value = v;
            if (k.equals("unicodePwd")) {
                if (StringUtil.isEmpty(v)) {
                    return;
                }
                value = encodePwd(v);
            }
            ModificationItem mod = new ModificationItem(DirContext.REPLACE_ATTRIBUTE, new BasicAttribute(k, value));
            list.add(mod);
        });

        ldapTemplate.modifyAttributes(dn, list.toArray(new ModificationItem[list.size()]));
        return true;
    }

    public boolean deleteUser(String[] oulist, String cn) {
        AtomicReference<LdapName> atomicReference = new AtomicReference<>();
        boolean flag = userExit(oulist, cn, atomicReference);
        if (!flag) {
            throw new RuntimeException("用户不存在");
        }
        ldapTemplate.unbind(atomicReference.get());
        return true;
    }

    public boolean addUser(LdapName dn, Map<String, String> data) {
        data.remove("cn");
        DirContextAdapter context = new DirContextAdapter(dn);
        //自定义属性使用setAttributeValues
        context.setAttributeValues("objectclass", new String[]{"top", "person", "organizationalPerson", "user"});
        //context.set(所有属性)
        data.forEach((k, v) -> {
            Object value = v;
            if ("unicodePwd".equals(k)) {
                if (StringUtil.isEmpty(v)) {
                    return;
                }
                value = encodePwd(v);
            }
            context.addAttributeValue(k, value, false);
        });//
        ldapTemplate.bind(context);
        return true;
    }

    private byte[] encodePwd(String source) {
        String quotedPassword = "\"" + source + "\""; // 注意：必须在密码前后加上双引号
        return quotedPassword.getBytes(StandardCharsets.UTF_16LE);
    }

}
