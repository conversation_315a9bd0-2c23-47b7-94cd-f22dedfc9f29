package com.ly.adp.xapi.api.base.biz;

import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.TreeMap;

public class CdpWebUtil {
    private static final Logger log = LoggerFactory.getLogger(CdpWebUtil.class);

    public static String getData(String url, Map<String,String> head, Map<String,String> param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {

            if(param != null && !param.isEmpty()){
                StringBuilder message = new StringBuilder();
                param.forEach((k, v) ->{
                    message.append(k).append("=").append(v).append("&");
                });
                url = url + "?" + message.substring(0, message.length() - 1);
            }

            uri = new URI(url);

            HttpHeaders httpHeaders = new HttpHeaders();
            if(head != null && !head.isEmpty()){
                head.forEach((k, v) ->{
                    httpHeaders.add(k, v);
                });
            }

            HttpEntity<Object> entity = new HttpEntity<>(param, httpHeaders);

            responseEntity = getRestTemplate().exchange(url, HttpMethod.GET, entity, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            // TODO Auto-generated catch block
            log.error("CdpWebUtil", e);
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    public static String postData(String url, MediaType mediaType, Map<String,String> head, Object param) {
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(url);

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(mediaType);
            if(head != null && !head.isEmpty()){
                head.forEach((k, v) ->{
                    httpHeaders.add(k, v);
                });
            }

            HttpEntity<Object> entity = new HttpEntity<>(param, httpHeaders);

            // 请根据需要，对应选择response泛型
            //responseEntity = restTemplate.exchange(uri, HttpMethod.POST, entity, String.class);
            responseEntity = getRestTemplate().exchange(url, HttpMethod.POST, entity, String.class);

        } catch (URISyntaxException e) {
            // TODO Auto-generated catch block
            log.error("CdpWebUtil", e);
            throw new RuntimeException(e);
        }catch (RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("CdpWebUtil", e);
            throw new RuntimeException(e);
        }

        return responseEntity.getBody();
    }

    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }

    public static String formatKv(Map<String, String> messageData, boolean urlEncode) {
        return formatKv(messageData, "&", "=", false, "UTF-8", false);
    }

    public static String formatKv(Map<String, String> messageData, String joiner, String linker,
                                  boolean urlEncode, String encoding, boolean onlyValue) {

        Map<String, String> messageDataSort = new TreeMap<>(messageData);

        StringBuilder message = new StringBuilder();
        for (Map.Entry<String, String> entry : messageDataSort.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            log.debug("add kv data {}:{}", name, value);

            if (urlEncode) {
                try {
                    name = URLEncoder.encode(entry.getKey(), encoding);
                    value = URLEncoder.encode(entry.getValue(), encoding);
                } catch (UnsupportedEncodingException e) {
                    throw new IllegalArgumentException("url encode failed, encoding is unsupported");
                }
                log.debug("add encoded kv data {}:{}", name, value);
            }

            if (onlyValue) {
                message.append(value).append(joiner);
            } else {

                message.append(name).append(linker).append(value).append(joiner);
            }

        }

        if (message.length() > 0 && joiner.length() > 0) {
            message.delete(message.length() - joiner.length(), message.length());
        }

        return message.toString();
    }
}
