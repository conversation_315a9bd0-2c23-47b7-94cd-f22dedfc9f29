package com.ly.adp.xapi.api.tda.receive;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/7
 */
public interface TdaReceive001 {

    void tdaReceiveModel(XapiInstance instance, XapiMap<String, Object> context, Map<String, Object> mainData, XapiResult.XapiResultData<Object> resultData);
}
