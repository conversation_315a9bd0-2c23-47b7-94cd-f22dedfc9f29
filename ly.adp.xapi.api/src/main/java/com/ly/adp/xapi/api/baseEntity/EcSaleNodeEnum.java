package com.ly.adp.xapi.api.baseEntity;

/**
 * <AUTHOR>
 * @description: ec订单状态枚举
 * @date 2023/4/4
 */
public enum EcSaleNodeEnum {

    // ec订单状态转换
    EC_SALE_NODE_CREATE("0", "1", "生成订单"),
    EC_SALE_NODE_CANCEL("1", "0", "取消订单"),
    EC_SALE_NODE_DRAWBACK("2", "0", "退款"),
    EC_SALE_NODE_SUCCESS("3", "2", "支付成功");


    private final String key;
    private final String code;
    private final String name;

    EcSaleNodeEnum(String key, String code, String name) {
        this.code = code;
        this.name = name;
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getEcSaleNodeEnum(String key) {
        EcSaleNodeEnum[] values = EcSaleNodeEnum.values();
        for (EcSaleNodeEnum element : values) {
            if (element.key.equals(key)) {
                return element.code;
            }
        }
        return "";
    }
}
