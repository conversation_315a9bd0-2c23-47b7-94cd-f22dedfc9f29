package com.ly.adp.xapi.api.baseEntity;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;

@ApiModel("物料pno18拉取接口请求参数")
public class Pno18RequestEntity implements Serializable {
	
	private static final long serialVersionUID = 1L;
	/*平台*/ 
	private String  platForm;
	/*车型名称*/
	private String  vehicletypeDesc;
	/*PNO18*/
	private String  pno18;
	/*开始变更时间*/
	private String  updateB;
	
	public String getPlatForm() {
		return platForm;
	}
	public void setPlatForm(String platForm) {
		this.platForm = platForm;
	}
	public String getVehicletypeDesc() {
		return vehicletypeDesc;
	}
	public void setVehicletypeDesc(String vehicletypeDesc) {
		this.vehicletypeDesc = vehicletypeDesc;
	}
	public String getPno18() {
		return pno18;
	}
	public void setPno18(String pno18) {
		this.pno18 = pno18;
	}
	public String getUpdateB() {
		return updateB;
	}
	public void setUpdateB(String updateB) {
		this.updateB = updateB;
	}
	public String getUpdateD() {
		return updateD;
	}
	public void setUpdateD(String updateD) {
		this.updateD = updateD;
	}
	/*开始变更时间*/
	private String  updateD;
	
}
