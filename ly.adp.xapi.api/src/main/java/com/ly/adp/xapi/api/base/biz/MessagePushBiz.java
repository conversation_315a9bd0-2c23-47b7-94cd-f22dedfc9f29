package com.ly.adp.xapi.api.base.biz;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.baseEntity.MessageResponseEntity;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.normal.NormalResult;
@Service
public class MessagePushBiz {
	private static final Logger log = LoggerFactory.getLogger(MessagePushBiz.class);
	
	  public NormalResult<MessageResponseEntity> sendMessage(XapiInstance instance, XapiMap<String, Object> context, NormalParam<Map<String,Object>> param) {
		  MessageResponseEntity messageResponseEntity =new MessageResponseEntity();
	    	try {
	    		//物料pno18拉取接口
	        	String url =instance.getSystem().getXapiKv("provide_path").getValueTee()+instance.getRemark();
				String appId=instance.getSystem().getXapiKv("appId").getValueTee();
				String appKey=instance.getSystem().getXapiKv("appKey").getValueTee();
				String appSecret=instance.getSystem().getXapiKv("appSecret").getValueTee();
				String masterSecret=instance.getSystem().getXapiKv("masterSecret").getValueTee();
			    
				String resp =MessagePushUtil.postData(url, param.getData(), appId, appKey, appSecret, masterSecret);
				messageResponseEntity = JSONObject.parseObject(resp, MessageResponseEntity.class);
			} catch (Exception e) {
				log.error("短信推送数据失败",e);
				
			}
	    	return NormalResult.createOk(messageResponseEntity);
	    }

}
