package com.ly.adp.xapi.api.base.biz;

import com.ly.adp.xapi.api.baseEntity.OpenUserRequestEntity;
import com.ly.adp.xapi.api.baseEntity.UserNumRequestEntity;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.normal.NormalResult;
import com.ly.mp.component.helper.StringHelper;
import org.apache.commons.beanutils.PropertyUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.Map;

import static com.ly.adp.xapi.api.base.biz.MD5Generator.getHexMD5;
import static org.apache.commons.lang3.StringUtils.upperCase;


/**
 * @Description
 * <AUTHOR>
 * @Date 2021/12/13 17:45
 */
@Service
public class TrainBiz {
    @Autowired
    HandelResDataBiz handelResDataBiz;
    private static final Logger log = LoggerFactory.getLogger(TrainBiz.class);

    //取培训人员是否通过信息
    public NormalResult<String> findUser(XapiInstance instance, XapiMap<String, Object> context, NormalParam<OpenUserRequestEntity> param) {
        String resp = null;
        try {
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + "/open" + instance.getRemark() + ".html";

            String corpCode = instance.getSystem().getXapiKv("corpCode").getValueTee();
            String appKey_ = instance.getSystem().getXapiKv("appKey_").getValueTee();
            String appSecret = instance.getSystem().getXapiKv("appSecret").getValueTee();
            String signText = appSecret + "|" + instance.getRemark() + "|" + appSecret;
            String sign_ = upperCase(getHexMD5(signText));

            param.getData().setCorpCode(corpCode);
            param.getData().setAppKey_(appKey_);
            param.getData().setSign_(sign_);
            param.getData().setTimestamp_(System.currentTimeMillis());

            Map<String, String> params = beanToMap(param.getData());

            resp = TrainWebUtil.getData(url, params);

        } catch (Exception e) {
            log.error("获取培训人员信息失败", e);
        }
        return NormalResult.createOk(resp);
    }

    //获取指定公司部门岗位下的编制人数
    public NormalResult<String> getUserNum(XapiInstance instance, XapiMap<String, Object> context, NormalParam<UserNumRequestEntity> param) {

        //UserNumResponseEntity userNumResponseEntity = new UserNumResponseEntity();
        String resp = null;
        try {
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + "/login" + instance.getRemark();

            String appKey_ = instance.getSystem().getXapiKv("appKey_").getValueTee();
            String appSecret = instance.getSystem().getXapiKv("appSecret").getValueTee();
            String signText = appSecret + "|" + instance.getRemark() + "|" + appSecret;
            String sign_ = upperCase(getHexMD5(signText));
            param.getData().setAppKey_(appKey_);
            param.getData().setSign_(sign_);
            param.getData().setTimestamp_(System.currentTimeMillis());

            String corpCode = instance.getSystem().getXapiKv("corpCode").getValueTee();
            param.getData().setCorpCode(corpCode);

            // Map<String, String> params=beanToMap(param.getData());

            resp = TrainWebUtil.postData(url, param.getData());

            //userNumResponseEntity = JSONObject.parseObject(resp, UserNumResponseEntity.class);
        } catch (Exception e) {
            log.error("获取指定公司部门岗位下的编制人数失败", e);
        }
        return NormalResult.createOk(resp);
    }

    public static Map<String, String> beanToMap(Object obj) {
        Map<String, String> params = new HashMap<String, String>();
        try {
            PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean();
            PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(obj);
            for (int i = 0; i < descriptors.length; i++) {
                String name = descriptors[i].getName();
                if (!"class".equals(name) && !StringHelper.IsEmptyOrNull(propertyUtilsBean.getNestedProperty(obj, name))) {
                    params.put(name, propertyUtilsBean.getNestedProperty(obj, name) + "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return params;
    }
}
