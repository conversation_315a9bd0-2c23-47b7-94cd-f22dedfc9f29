package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 充电事件
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class Charge implements Serializable {

    private static final long serialVersionUID = 6120493175680346481L;

    /**
     * 充电时间
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 电量
     */
    @JsonProperty("power")
    private int power;

    /**
     * 充电类型（1：开始充电，2：结束充电）
     */
    @JsonProperty("type")
    private int type;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getPower() {
        return power;
    }

    public void setPower(int power) {
        this.power = power;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
