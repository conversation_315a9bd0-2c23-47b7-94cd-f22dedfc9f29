package com.ly.adp.xapi.api.base;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.ReactivePlusDataBase;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.ReactivePlusService;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.XapiReactiveRequestPage;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.XapiReactiveResponsePage;
import com.ly.bucn.component.xapi.service.normal.NormalResult;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @description: TRADEIN调用
 * @date 2022/12/5
 */
@ReactivePlusService("TRADEIN_ADP")
public class TradeinReactivePlusService extends ReactivePlusDataBase {
    final static Logger logger = LoggerFactory.getLogger(TradeinReactivePlusService.class);

    @Override
    public XapiReactiveRequestPage<Map<String, Object>> wrapParam(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, XapiReactiveRequestPage<Map<String, Object>> mapXapiReactiveRequestPage) {
        return mapXapiReactiveRequestPage;
    }

    @Override
    public XapiReactiveResponsePage<Object> wrapResult(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, XapiReactiveResponsePage<Object> objectXapiReactiveResponsePage) {
        return objectXapiReactiveResponsePage;
    }

    @Override
    public XapiReactiveResponsePage<Object> queryData(XapiInstance instance, XapiMap<String, Object> context,
                                                      XapiReactiveRequestPage<Map<String, Object>> param) {
        logger.info("instance:{}", instance);
        if (StringHelper.IsEmptyOrNull(param)) {
            XapiReactiveResponsePage<Object> responsePage = new XapiReactiveResponsePage<>();
            responsePage.setCode("-1");
            responsePage.setMsg("参数不能为空");
            return responsePage;
        }
        Map<String, Object> paramBody = param.getBody();
        if (StringHelper.IsEmptyOrNull(paramBody.get("smartId")) && StringHelper.IsEmptyOrNull(paramBody.get("saleOrderCode"))) {
            XapiReactiveResponsePage<Object> responsePage = new XapiReactiveResponsePage<>();
            responsePage.setCode("-1");
            responsePage.setMsg("smartId和订单编号不能都为空");
            return responsePage;
        }
        return super.queryData(instance, context, param);
    }
}
