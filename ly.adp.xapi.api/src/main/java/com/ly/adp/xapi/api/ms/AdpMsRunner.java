package com.ly.adp.xapi.api.ms;

import com.alibaba.fastjson.JSON;
import com.ly.adp.common.ms.RabbitMQPool;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.config.XConfigInitedEvent;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.ly.mp.component.helper.SessionHelper;
import com.ly.mp.component.helper.StringHelper;
import com.rabbitmq.client.*;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.SmartLifecycle;
import org.springframework.jms.listener.AbstractMessageListenerContainer;
import org.springframework.jms.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Component
public class AdpMsRunner implements ApplicationListener<XConfigInitedEvent>, SmartLifecycle {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    private static ThreadLocal<String> PUBLISHTYPE = ThreadLocal.withInitial(() -> "web");
    private Boolean running = false;

    public static Boolean isMqPublish() {
        return "mq".equals(PUBLISHTYPE.get());
    }

    @Autowired
    RabbitMQPool rabbitMQPool;

    @Resource
    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage passiveServiceManage;

    Map<String, Channel> consumerMap = new HashMap<>();


    @Override
    public void onApplicationEvent(XConfigInitedEvent event) {
        if (running) {
            load();
        }
    }

    private void dealData(String service, String data) {
        try {
            PUBLISHTYPE.set("mq");
            // 解析data
            XapiInstance instance = xServiceHolder.getInstance(service);
            // 判断是否有对应的配置
            if (instance != null) {
                AtomicReference<String> respStr = new AtomicReference<>();
                logger.info("收到消息的消息体 {}", data);
                passiveServiceManage.handle(instance, data, m -> respStr.set((String) m));
            } else {
                logger.error(String.format("mq报文解析，接口%s不存在", service));
            }
            //System.out.println("MQ报文获取成功：" + service + " ： " + data);
        } catch (Exception e) {
            logger.error(String.format("mq报文解析接口%s失败", service), e);
        } finally {
            PUBLISHTYPE.remove();
        }
    }


    @Override
    public void start() {
        running = true;
        load();
        logger.info("smartLifecycle 容器启动完成 ...");
    }

    @Override
    public void stop() {
        running = false;
        logger.info("smartLifecycle 容器停止启动 ...");
    }


    @Override
    public boolean isRunning() {
        return running;
    }

    private void load() {
        consumerMap.forEach((tag, channel) -> {
            try {
                String[] tags = tag.split("\\|");
                channel.basicCancel(tags[0]);
                rabbitMQPool.returnChannel(tags[1], channel);
            } catch (IOException e) {
                logger.error(String.format("mq队列%s释放失败！！！", ""), e);
            }
        });
        consumerMap.clear();
        logger.info("mq队列启动中.....！！！");
        Collection<XapiInstance> instances = xServiceHolder.getxConfigs().values();
        List<XapiInstance> instancesL = (List) instances.stream().filter((m) -> {
            return "MQ".equals(m.getService()) && "receive".equals(m.getObjectType()) && "1".equals(m.getState());
        }).collect(Collectors.toList());

        instancesL.forEach((l) -> {
            if (StringHelper.IsEmptyOrNull(l.getRemark())) {
                return;
            }
            String[] mqConfig = l.getRemark().split("\\|");
            String group = mqConfig[0];//分组
            String exchange = mqConfig[1];//绑定的交换机
            String queue = mqConfig[2]; //mq为监听队列
            try {
                rabbitMQPool.consumerTemplate(group, channel -> {
                    Consumer consumer = new DefaultConsumer(channel) {
                        @Override
                        public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties,
                                                   byte[] body) throws IOException {
                            dealData(l.getLabel(), new String(body, "UTF-8"));
                        }
                    };
                    channel.queueDeclare(queue, true, false, false, null);
                    channel.queueBind(queue, exchange, "");//绑定交换机
                    String tag = channel.basicConsume(queue, true, consumer);
                    consumerMap.put(tag + "|" + group, channel);
                });
            } catch (Exception e) {
                logger.error(String.format("mq队列%s创建失败！！！", queue), e);
            }
        });

    }
}
