package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModel;

import java.io.Serializable;

@ApiModel("外饰主数据接口请求参数")
public class ExteriorsRequestEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /*平台*/
    private String  platForm;
    /*车型名称*/
    private String  vehicletypeDesc;
    /*开始变更时间*/
    private String  updateB;
    /*开始变更时间*/
    private String  updateD;

    public String getPlatForm() {
        return platForm;
    }

    public void setPlatForm(String platForm) {
        this.platForm = platForm;
    }

    public String getVehicletypeDesc() {
        return vehicletypeDesc;
    }

    public void setVehicletypeDesc(String vehicletypeDesc) {
        this.vehicletypeDesc = vehicletypeDesc;
    }

    public String getUpdateB() {
        return updateB;
    }

    public void setUpdateB(String updateB) {
        this.updateB = updateB;
    }

    public String getUpdateD() {
        return updateD;
    }

    public void setUpdateD(String updateD) {
        this.updateD = updateD;
    }
}
