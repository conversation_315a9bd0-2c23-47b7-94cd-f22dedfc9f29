package com.ly.adp.xapi.api.clue.feign;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/8
 * @Version 1.0.0
 **/
@FeignClient(name = "agentClueFeign", url = "${refer.agent-clue.url}")
public interface AgentClueFeign {
    @ApiOperation(value = "用户旅程", notes = "用户旅程")
    @PostMapping("/api/rpc/agent/clue/queryListUserEventFlow")
    RespBody<List<ClueEventFlowRsp>> queryUserEventFlow(@RequestBody Object param);
}
