package com.ly.adp.xapi.api.base.entities;

/**
 * 邮件附件实体
 */
public class MailAttachmentDTO {
    /**
     * 附件内容（Base64编码）
     */
    private String contentBytes;

    /**
     * 附件名称
     */
    private String name;

    public MailAttachmentDTO() {
    }

    public String getContentBytes() {
        return contentBytes;
    }

    public void setContentBytes(String contentBytes) {
        this.contentBytes = contentBytes;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "MailAttachmentDTO{" +
                "contentBytes='" + contentBytes + '\'' +
                ", name='" + name + '\'' +
                '}';
    }
}
