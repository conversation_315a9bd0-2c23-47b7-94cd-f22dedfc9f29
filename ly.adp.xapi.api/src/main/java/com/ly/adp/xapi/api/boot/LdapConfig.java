package com.ly.adp.xapi.api.boot;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ClassPathResource;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.InputStream;
import java.security.KeyStore;

@Configuration
public class LdapConfig {

    @Value("${spring.ldap.urls:}")
    private String ldapUrl;

    @Value("${spring.ldap.base:}")
    private String ldapBaseDn;

    @Value("${spring.ldap.username:}")
    private String ldapUsername;

    @Value("${spring.ldap.password:}")
    private String ldapPassword;

    @Bean
    public LdapContextSource contextSource() throws Exception {
        // 1. 加载PKCS12格式的证书文件
        // PKCS12是一种标准的证书格式，常用于存储私钥和证书链
        // 它可以包含整个证书链，从根证书到服务器证书
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        try (InputStream is = new ClassPathResource("certs/dc03.pfx").getInputStream()) {
            // 使用密码解锁证书文件
            // PFX/PKCS12文件通常是加密的，需要密码才能访问其内容
            keyStore.load(is, "dc03".toCharArray());
        }

        // 2. 创建信任管理器
        // TrustManagerFactory负责验证证书链的有效性
        // 使用默认算法（通常是"PKIX"或"SunX509"）
        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        // 使用加载的证书初始化信任管理器
        // 这样信任管理器就知道哪些证书是可信的
        tmf.init(keyStore);

        // 3. 配置SSL上下文
        // 创建TLS协议的SSL上下文，TLS是SSL的继任者，更安全
        // 使用"TLS"而不是具体版本可以自动选择最高支持的版本
        SSLContext sslContext = SSLContext.getInstance("TLS");
        // 初始化SSL上下文
        // 第一个参数null表示不需要密钥管理器（因为我们只是客户端）
        // 第二个参数提供信任管理器，用于验证服务器证书
        // 第三个参数null使用默认的随机数生成器
        sslContext.init(null, tmf.getTrustManagers(), null);
        // 将配置好的SSL上下文设置为JVM全局默认值
        // 这样所有的LDAP连接都会使用这个SSL配置
        SSLContext.setDefault(sslContext);

        // 4. 创建和配置LDAP连接源
        // LdapContextSource用于创建到LDAP服务器的连接
        LdapContextSource contextSource = new LdapContextSource();
        // 设置LDAP服务器URL（使用ldaps://表示SSL连接）
        contextSource.setUrl(ldapUrl);
        // 设置LDAP搜索的基础DN
        contextSource.setBase(ldapBaseDn);
        // 设置连接LDAP服务器的用户DN
        contextSource.setUserDn(ldapUsername);
        // 设置连接密码
        contextSource.setPassword(ldapPassword);

        return contextSource;
    }

    @Bean
    public LdapTemplate ldapTemplate() throws Exception {
        return new LdapTemplate(contextSource());
    }
}