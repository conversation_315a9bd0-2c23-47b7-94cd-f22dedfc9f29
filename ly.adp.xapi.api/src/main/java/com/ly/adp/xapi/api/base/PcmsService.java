package com.ly.adp.xapi.api.base;


import java.util.Map;


import com.ly.adp.xapi.api.base.biz.PcmsBiz;

import com.ly.bucn.component.xapi.service.normal.NormalDataGenericBase;
import com.ly.bucn.component.xapi.service.normal.NormalFunctionGeneric;

import com.ly.bucn.component.xapi.service.normal.NormalService;

import org.springframework.beans.factory.annotation.Autowired;


@NormalService("PCMS_ADP")
public class PcmsService extends NormalDataGenericBase {

    @Autowired
    private PcmsBiz pcmsBiz;
	
    /*public NormalResult<User> testUsers(XapiInstance instance, XapiMap<String, Object> context, NormalParam<List<User>> xx) {
        return NormalResult.createOk(xx.getData().get(0));
    }

    public NormalResult<User> testUserA(XapiInstance instance, XapiMap<String, Object> context, NormalParam<User[]> xx) {
        return NormalResult.createOk(xx.getData()[0]);
    }

    public NormalResult<String> testMap(XapiInstance instance, XapiMap<String, Object> context, NormalParam<Map<String, Object>> xx) {
        return NormalResult.createOk(xx.getData().toString());
    }*/

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
        handlers.put("getPno18", pcmsBiz::getPno18);
        handlers.put("getPno12", pcmsBiz::getPno12);
        handlers.put("getInterior", pcmsBiz::getInterior);
        handlers.put("getExteriors", pcmsBiz::getExteriors);
        handlers.put("getOptional", pcmsBiz::getOptional);
    }
    
    

  /*  public static class User {
        private String name;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }
    }*/

}
