package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 联网记录
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class WifiHistory implements Serializable {

    private static final long serialVersionUID = 5490827517935975080L;

    /**
     * 时间
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 联网结果：- config_ok：配网成功 - config_fail：配网失败 - connect_ok：联网成功 - connect_fail：联网失败
     */
    @JsonProperty("result")
    private int result;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }
}
