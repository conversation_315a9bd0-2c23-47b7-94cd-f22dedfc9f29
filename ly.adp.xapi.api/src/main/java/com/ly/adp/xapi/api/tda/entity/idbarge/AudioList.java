package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 音频列表
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class AudioList implements Serializable {

    private static final long serialVersionUID = -8582359375995852915L;

    /**
     * 音频ID
     */
    @JsonProperty("audio_id")
    private String audioId;

    public String getAudioId() {
        return audioId;
    }

    public void setAudioId(String audioId) {
        this.audioId = audioId;
    }
}
