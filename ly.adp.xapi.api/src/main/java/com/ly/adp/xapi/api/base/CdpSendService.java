package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.CdpBiz;
import com.ly.adp.xapi.api.base.biz.CdpWebUtil;
import com.ly.adp.xapi.api.bpm.SendDataBaseDecorator;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.bucn.component.xapi.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

@SendService("cdp_adp")
public class CdpSendService extends SendDataBaseDecorator<Object> {

    private static final Logger log = LoggerFactory.getLogger(CdpSendService.class);

    @Override
    public XapiData improveColumn(XapiInstance instance, XapiMap<String, Object> context, XapiData xData) {
        return super.improveColumn(instance, context, xData);
    }
    @Override
    public Object convertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        log.info("sendCdp调用convertData");
        String json = "";
        if ("map".equals(instance.getRemark())) {

            if (CollectionUtils.isEmpty(data.getDataEx())) {
                json = JSONObject.toJSONString(data.getDataEx());
            } else {
                json = JSONObject.toJSONString(data.getDataEx().get(0));
            }

        } else {
            json = JSONObject.toJSONString(data.getDataEx());
        }
        return json;
    }

    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> context, Object json) throws Exception {
        log.info("sendCdp调用sendData");
        String token = "";
        if (!context.containsKey("access_token")) {

            token = CdpBiz.getToken(xapiInstance);
            context.put("access_token", token);

        } else {
            token = String.valueOf(context.get("access_token"));
        }

        String url = xapiInstance.getSystem().getProvidePath() + xapiInstance.getService();
//        if("cdp_adp_s_leads_evnet".equals(xapiInstance.getLabel())){//线索履历发送
//            Map<String, Object> map = JSONObject.parseObject(String.valueOf(json),Map.class);
//            url = url + "/" + map.get("event");
//        }

        if ("cdp_adp_s_leads_evnet".equals(xapiInstance.getLabel()) ||
                "cdp_adp_s_leads".equals(xapiInstance.getLabel()) ||
                "cdp_adp_s_leads_evnet_testappoint".equals(xapiInstance.getLabel())) {
            url = url + "?access_token=" + token;
        }
        Map<String, String> headG = new HashMap<>(1);
        headG.put("Authorization", "Bearer " + token);
        log.info("sendCdp调用url:{},入参:{},headG:{}", url, json, JSONObject.toJSONString(headG));
        String result = CdpWebUtil.postData(url, MediaType.APPLICATION_JSON, headG, json);
        log.info("sendCdp返回结果：{}", result);
        return result;
    }

    @Override
    public XapiResult<Object> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object data) {
        log.info("sendCdp调用manageResp");
        XapiResult<Object> result = XapiResult.creat();
        try {
            String respJson = String.valueOf(data);
            @SuppressWarnings("unchecked")
            Map<String, Object> resp = JSONObject.parseObject(respJson, Map.class);
            if (resp.containsKey("error")) {

                result.setStatus(1);
                result.setMessage(StringUtil.subStr(JSONObject.toJSONString(resp.get("error")), 200));
                return result;

            } else {

                return result;

            }
        } catch (Exception e) {

            log.error("CdpSendService解析反馈报文：", e);
            result.setStatus(1);
            result.setMessage(e.getMessage());
        }
        return result;
    }
}
