package com.ly.adp.xapi.api.baseEntity;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: ec/otd返回数据
 * @date 2023/4/3
 */
@ApiModel("ec/otd返回数据")
public class EcStockVehicleResponse implements Serializable {
    private static final long serialVersionUID = -6809183291712502287L;

    @ApiModelProperty("VIN码")
    private String vin;
    @ApiModelProperty("销售状态（0:可售,1:售卖中,2:不可售）")
    private Integer salesStatus;
    @TableField("pno18")
    @ApiModelProperty("PNO18")
    private String pno18;
    @ApiModelProperty("车辆类型")
    private Integer vehicleType;
    @ApiModelProperty("优惠金额")
    private BigDecimal discountAmount;
    @ApiModelProperty("售价")
    private BigDecimal retailPrice;
    @ApiModelProperty("是否叠加现车权益(0:否,1:是)")
    private Integer isOverlay;


    public String getVin() {
        return vin;
    }

    public void setVin(String vin) {
        this.vin = vin;
    }

    public Integer getSalesStatus() {
        return salesStatus;
    }

    public void setSalesStatus(Integer salesStatus) {
        this.salesStatus = salesStatus;
    }

    public String getPno18() {
        return pno18;
    }

    public void setPno18(String pno18) {
        this.pno18 = pno18;
    }

    public Integer getVehicleType() {
        return vehicleType;
    }

    public void setVehicleType(Integer vehicleType) {
        this.vehicleType = vehicleType;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getRetailPrice() {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) {
        this.retailPrice = retailPrice;
    }

    public Integer getIsOverlay() {
        return isOverlay;
    }

    public void setIsOverlay(Integer isOverlay) {
        this.isOverlay = isOverlay;
    }

    @Override
    public String toString() {
        return "EcStockVehicleResponse{" +
                "vin='" + vin + '\'' +
                ", salesStatus=" + salesStatus +
                ", pno18='" + pno18 + '\'' +
                ", vehicleType=" + vehicleType +
                ", discountAmount=" + discountAmount +
                ", retailPrice=" + retailPrice +
                ", isOverlay=" + isOverlay +
                '}';
    }
}
