package com.ly.adp.xapi.api.ms;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.common.ms.RabbitMQPool;
import com.ly.adp.common.util.BucnException;
import com.ly.adp.xapi.api.ms.mapper.AdpMsLogMapper;
import com.ly.bucn.component.xapi.entity.PageResult;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.log.db.XapiLogDb;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.normal.NormalResult;
import com.ly.mp.busicen.common.util.BusicenUtils;
import com.ly.mp.busicen.common.util.EntityResultBuilder;
import com.ly.mp.busicen.common.util.ListResultBuilder;
import com.ly.mp.busicen.common.util.OptResultBuilder;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.StringHelper;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.BuiltinExchangeType;
import com.rabbitmq.client.GetResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Service
public class AdpMsLogBiz {

    @Autowired
    AdpMsLogMapper adpMsLogMapper;

    @Autowired
    IXapiLogBiz xapiLogDb;

    @Autowired
    XServiceHolder xServiceHolder;

    @Autowired
    AdpNormalMs adpNormalMs;

    @Autowired
    RabbitMQPool rabbitMQPool;

    public EntityResult<String> pullMessage(String queue) {
        AtomicReference<String> message = new AtomicReference<>();
        EntityResult<String> result = EntityResultBuilder.<String>creatOk().build();
        rabbitMQPool.publishTemplate(c -> {
            GetResponse response = c.basicGet(queue, true);
            message.set(new String(response.getBody(), "utf-8"));
            result.setRows(message.get());
        });
        return result;
    }


    public ListResult<Map<String, Object>> apiLabel(ParamPage<Map<String, Object>> paramPage) {
        Page<Map<String, Object>> page = new Page<>(paramPage.getPageIndex(), paramPage.getPageSize());
        List<Map<String, Object>> list = adpMsLogMapper.apiLabel(page, paramPage.getParam());
        ListResult<Map<String, Object>> listResult = BusicenUtils.page2ListResult(page);
        listResult.setRows(list);
        return listResult;
    }

    public ListResult<XapiLog> msLog(ParamPage<XapiLog> paramPage) {
        if (paramPage.getParam() == null) {
            throw new BucnException("参数不能空");
        }
        if (paramPage.getParam().getLabel() == null || paramPage.getParam().getLabel().isEmpty()) {
            throw new BucnException("接口编码不能为空");
        }
        PageResult<XapiLog> pageResult = xapiLogDb.list(paramPage.getParam(), paramPage.getPageIndex(), paramPage.getPageSize());
        return ListResultBuilder.creatOk()
                .pageindex(paramPage.getPageIndex().longValue())
                .records(pageResult.getRows().longValue())
                .rows(pageResult.getData())
                .pages(pageResult.getPages().longValue()).build();
    }

    /**
     * 通过选择主键重发
     *
     * @param paramBase
     * @return
     */
    public OptResult resendByIds(ParamBase<List<String>> paramBase) {
        paramBase.getParam().forEach(id -> {
            XapiLog xapiLogParam = new XapiLog();
            xapiLogParam.setLogId(id);
            PageResult<XapiLog> pageResult = xapiLogDb.list(xapiLogParam, 1, 1);
            pageResult.getData().forEach(this::resend);
        });
        return OptResultBuilder.createOk().build();
    }

    /**
     * 通过条件查询重发
     *
     * @param paramPage
     * @return
     */
    public OptResult resendByParam(ParamPage<XapiLog> paramPage) {
        PageResult<XapiLog> pageResultAll = xapiLogDb.list(paramPage.getParam(), 1, 10 * 1000);
        pageResultAll.getData().forEach(xl -> {
            XapiLog xapiLogParam = new XapiLog();
            xapiLogParam.setLogId(xl.getLogId());
            PageResult<XapiLog> pageResult = xapiLogDb.list(xapiLogParam, 1, 1);
            pageResult.getData().forEach(this::resend);
        });
        return OptResultBuilder.createOk().build();
    }

    /**
     * 重发
     *
     * @param xapiLog
     */
    public void resend(XapiLog xapiLog) {
        String oldState = xapiLog.getLogStatus();
        xapiLog.setLogTime(new Date());
        if (msResend(xapiLog)) {
            xapiLog.setLogEndTime(new Date());
            xapiLog.setLogStatus("0");
            Map<String, Object> map = new HashMap<>();
            map.put("logId", xapiLog.getLogId());
            map.put("logStatus", 0);
            //新增重发日志
            xapiLog.setLogMessage(String.format("resend link id [%s]", xapiLog.getLogId()));
            xapiLog.setSerialNum(UUID.randomUUID().toString().replace("-", ""));
            xapiLogDb.add(xapiLog);
            if (!"0".equals(oldState)) {
                //修改旧有状态
                adpMsLogMapper.logUpdate(map);
            }

        } else {

        }
    }

    /**
     * mq resend
     *
     * @param xapiLog
     * @return
     */
    public boolean msResend(XapiLog xapiLog) {
        String label = xapiLog.getLabel();
        XapiInstance xapiInstance = xServiceHolder.getInstance(label);
        NormalParam<String> normalParam = new NormalParam<>();
        normalParam.setData(xapiLog.getInJson());
        NormalResult<EntityResult<List<String>>> entityResult = adpNormalMs.accept(xapiInstance, null, normalParam);
        xapiLog.setOutJson(JSONObject.toJSONString(entityResult.getData()));
        return entityResult.isSuccess();
    }

    public EntityResult<String> sendMessage(String group, String exchange , String json) {
        AtomicReference<String> message = new AtomicReference<>();
        EntityResult<String> result = EntityResultBuilder.<String>creatOk().build();
        if(StringHelper.IsEmptyOrNull(group)){
            group = "master";
        }
        rabbitMQPool.publishTemplate(group, channel -> {
            AMQP.BasicProperties rabbitProperties = new AMQP.BasicProperties().builder()
                    .messageId(UUID.randomUUID().toString())
                    .contentEncoding("UTF-8")
                    .deliveryMode(2)
                    .contentType("json").build();
            channel.basicPublish(exchange, "", rabbitProperties, json.getBytes("UTF-8"));
        });
        return result;
    }
}
