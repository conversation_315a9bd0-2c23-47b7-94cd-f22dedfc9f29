package com.ly.adp.xapi.api.interfacecenter.controller;

import com.ly.adp.common.entity.Result;
import com.ly.adp.xapi.api.interfacecenter.entities.dto.SapSaleCommissionTotalDTO;
import com.ly.adp.xapi.api.interfacecenter.services.InterfacecenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.*;


@Api(tags = "接口表服务（供业务服务调用）")
@RestController
@RequestMapping("interface")
public class InterfacecenterController {

    private static final Logger log = LoggerFactory.getLogger(InterfacecenterController.class);

    @Autowired
    private InterfacecenterService interfacecenterService;

    @ApiOperation("批量保存销售佣金汇总数据")
    @PostMapping("commissionTotal/batchSave")
    public Result batchSaveCommissionTotal(@RequestHeader(HttpHeaders.AUTHORIZATION) String token,
                                           @RequestBody SapSaleCommissionTotalDTO sapSaleCommissionTotalDTO) {
        Result result = null;
        try {
            result = interfacecenterService.batchSaveCommissionTotal(sapSaleCommissionTotalDTO);
        } catch (Exception e) {
            log.error("批量保存销售佣金汇总数据异常：{}", e.getMessage(), e);
            result = Result.Builder.failure();
        }
        return result;
    }
}