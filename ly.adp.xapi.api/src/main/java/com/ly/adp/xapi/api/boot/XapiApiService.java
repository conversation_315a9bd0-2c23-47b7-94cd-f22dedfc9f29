package com.ly.adp.xapi.api.boot;

import com.ly.adp.common.configuration.AdpConfiguration;
import com.ly.adp.common.configuration.SwaggerConfiguration;
import com.ly.adp.common.configuration.ThreadPoolConfig;
import com.ly.bucn.component.xapi.configuration.XapiAutoConfiguration;
import com.ly.bucn.component.xapi.log.db.XapiLogDbConfigure;
import com.ly.bucn.component.xapi.plugin.ext.normal.XapiNormalInsert;
import com.ly.mp.busicen.common.helper.RedisLockUtil;
import com.ly.mp.cache.data.RedisTemplateConfiguration;
import com.ly.mp.cloud.conf.web.CloudInitializer;
import com.ly.mp.component.conf.AppConfig;
import com.ly.mp.component.conf.web.CommonInitializer;
import com.ly.mp.dal.comm.jdbc.DynamicDataSource;
import com.ly.mp.dal.comm.jdbc.PagedJdbcTemplate;
import com.szlanyou.common.redis.util.RedisExtUtil;
import com.szlanyou.common.redis.util.RedisUtil;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.XADataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;


@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
        XADataSourceAutoConfiguration.class, ActiveMQAutoConfiguration.class})
@Import({CommonInitializer.class, AppConfig.class, CloudInitializer.class, SwaggerConfiguration.class, XapiAutoConfiguration.class,
        AdpConfiguration.class, XapiLogDbConfigure.class, RedisTemplateConfiguration.class, RedisLockUtil.class, ThreadPoolConfig.class})
@MapperScan({"com.ly.adp.**.mapper", "com.ly.adp.**.biz", "com.ly.adp.**.service"})
@ComponentScan(basePackages = {"com.ly.adp.xapi.api.base.biz", "com.ly.mp.dal.comm.config",
        "com.ly.mp.dal.comm.mybatis", "com.ly.adp.xapi.api.base.mapper",
        "com.ly.adp.xapi.api.boot", "com.ly.adp.xapi.api.ms","com.ly.adp.xapi.api"})
@EnableFeignClients({"com.ly.adp.xapi.api"})
public class XapiApiService {

    public static void main(String[] args) {

        System.setProperty("service.name", "ly.adp.xapi.api.service");

        SpringApplication.run(XapiApiService.class, args);
    }

    @Bean
    public RedisUtil redisUtil() {
        return new RedisUtil();
    }

    @Bean
    public RedisExtUtil redisExtUtil() {
        return new RedisExtUtil();
    }
    @Bean
    public XapiNormalInsert xapiNormalInsert() {
        return new XapiNormalInsert();
    }

    @Bean
    public PagedJdbcTemplate PagedJdbcTemplate(DynamicDataSource dataSource) {
        return new PagedJdbcTemplate(dataSource);
    }


}
