package com.ly.adp.xapi.api.acc;

import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/11/20
 */
public class ErrorResponseException extends RuntimeException {


    private HttpStatus httpStatus;
    private String code;
    private String msg;

    public HttpStatus getHttpStatus() {
        return httpStatus;
    }

    public void setHttpStatus(HttpStatus httpStatus) {
        this.httpStatus = httpStatus;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
