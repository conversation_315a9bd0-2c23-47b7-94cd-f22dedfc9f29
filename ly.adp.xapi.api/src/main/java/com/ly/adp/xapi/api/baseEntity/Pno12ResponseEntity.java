package com.ly.adp.xapi.api.baseEntity;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "获取pno18返回")
@SuppressWarnings("serial")
public class Pno12ResponseEntity  implements Serializable {
		
	 private int code;
		
	 private String msg;
		
	 private List<Map<String, Object>> data;

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}

	public List<Map<String, Object>> getData() {
		return data;
	}

	public void setData(List<Map<String, Object>> data) {
		this.data = data;
	}
	 
	 
}
