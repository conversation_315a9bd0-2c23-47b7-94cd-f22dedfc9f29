package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.SapWebUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;

import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.pull.PullDataBase;
import com.ly.bucn.component.xapi.service.pull.PullService;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;

import java.util.*;

@PullService("sap_adp")
public class SapPullService extends PullDataBase<String> {
    private static final Logger log = LoggerFactory.getLogger(SapPullService.class);
   /* @Override
    public Object serializeParam(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, XapiMap<String, Object> stringObjectXapiMap2) {
        return null;
    }*/

    @Override
    public Object pullData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object queryParam) throws Exception {


        return SapWebUtil.postData(xapiInstance.getSystem().getProvidePath() + xapiInstance.getRemark(), xapiInstance.getSystem().getProvideAccount(), xapiInstance.getSystem().getProvidePassword(), queryParam);
    }

    @Override
    public XapiData deserializeData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) {
        XapiData xapiData = XapiData.creat();
        try {
            String jsonresp = String.valueOf(o);
            Map<String, Object> resp = JSONObject.parseObject(jsonresp, Map.class);
            if (resp.containsKey("ZTYPE")) {
                if ("S".equals(String.valueOf(resp.get("ZTYPE")))) {
                    if (resp.containsKey("RESULT_DATA")) {
                        Map<String, Object> Data = (Map<String, Object>) resp.get("RESULT_DATA");
                        if (Data.containsKey("item")) {
                            List<Map<String, Object>> listData = (List<Map<String, Object>>) Data.get("item");
                            listData.forEach(m -> {
                                XapiData.XapiDataRow xapiDataRow = XapiData.XapiDataRow.creat();
                           /* xapiDataRow.getMainData().put("xapibatchno", batchNo);
                            m.put("xapibatchno", batchNo);*/
                                xapiDataRow.setMainData(m);
                                xapiData.add(xapiDataRow);
                            });
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("sap保证金获取失败", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
        return xapiData;
    }

    public Object serializeParam(XapiInstance instance, XapiMap<String, Object> context, XapiMap<String, Object> param) {
        Map<String, Object> paramMap = new HashMap<>();
        Map<String, Object> map = new HashMap<>();
        try {
            String DATAFORM = instance.getSystem().getXapiKv("DATAFORM").getValueTee();
            String DATATO = instance.getSystem().getXapiKv("DATATO").getValueTee();
            if (StringHelper.IsEmptyOrNull(DATAFORM) && StringHelper.IsEmptyOrNull(DATATO)) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                Date beginDate = new Date();
                String bdate = dateFormat.format(beginDate);
                Calendar date = Calendar.getInstance();
                date.setTime(beginDate);
                date.set(Calendar.DATE, date.get(Calendar.DATE) - 1);
                String edate = dateFormat.format(date.getTime());
                param.put("DATAFORM", edate);
                param.put("DATATO", bdate);
            } else {
                param.put("DATAFORM", DATAFORM);
                param.put("DATATO", DATATO);
            }
            param.put("CUSTCODE", "*");
            paramMap.put("ZINPUT", param);
            map.put("urn:ZIF_ADP_37", paramMap);
        } catch (Exception e) {
            e.printStackTrace();
        }

        String json = JSONObject.toJSONString(map);
        return json;
    }

  /*  public static void main(String[] args) {
        String json = "{\"urn:ZfmTest001\":{\"Num1\":\"3\",\"Num2\":\"20\"}}";
        String str = SapWebUtil.postData("https://production-iwxfh0wv.it-cpi010-rt.cpi.cn40.apps.platform.sapcloud.cn/http/demo/calculate_plus_json","sb-422985d4-1248-495b-896b-1e4ae7e441a7!b906|it-rt-production-iwxfh0wv!b39","430cbdbf-a606-4ee2-913e-48ddbb039dbb$F7CjHwHN7Rv3EWdWaLpEUBhZV7W2VTrk4vGsgl2Ch8M=",json);
        System.out.println(str);
    }*/
}
