package com.ly.adp.xapi.api.base.biz;

import java.beans.PropertyDescriptor;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.beanutils.PropertyUtilsBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.baseEntity.ExteriorsRequestEntity;
import com.ly.adp.xapi.api.baseEntity.ExteriorsResponseEntity;
import com.ly.adp.xapi.api.baseEntity.InteriorRequestEntity;
import com.ly.adp.xapi.api.baseEntity.InteriorResponseEntitiy;
import com.ly.adp.xapi.api.baseEntity.OptionResponseEntity;
import com.ly.adp.xapi.api.baseEntity.Pno12RequestEntity;
import com.ly.adp.xapi.api.baseEntity.Pno12ResponseEntity;
import com.ly.adp.xapi.api.baseEntity.Pno18RequestEntity;
import com.ly.adp.xapi.api.baseEntity.Pno18ResponseEntity;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.normal.NormalResult;
import com.ly.mp.component.helper.StringHelper;

@Service
public class PcmsBiz {
    @Autowired
    HandelResDataBiz handelResDataBiz;

    private static final Logger log = LoggerFactory.getLogger(PcmsBiz.class);

    public NormalResult<Pno18ResponseEntity> getPno18(XapiInstance instance, XapiMap<String, Object> context, NormalParam<Pno18RequestEntity> param) {
        Pno18ResponseEntity pno18ResponseEntity = new Pno18ResponseEntity();
        try {
            //物料pno18拉取接口
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + instance.getRemark();
            String client_id = instance.getSystem().getXapiKv("client_id").getValueTee();
            Map<String, String> params = beanToMap(param.getData());
            String token = null;
            //token=PcmsWebUtil.getToken();
            String resp = PcmsWebUtil.getData(url, params, client_id, token);
            //String resp="{\"data\":[{\"vehicletypeDesc\":\"BX11\",\"description\":\"BX11_GEP3\",\"platform\":\"-/BX11\",\"pno181\":\"BX1G5075F151010334\",\"exteriorColor\":\"054\",\"interiorColor\":\"A88\",\"options\":[{\"optionalCode\":\"KE03\"},{\"optionalCode\": \"JB31\"}]}],\"code\":200,\"msg\":\"success\"}";

            pno18ResponseEntity = JSONObject.parseObject(resp, Pno18ResponseEntity.class);
            //插入接口表
            handelResDataBiz.handelPno18Resp(pno18ResponseEntity);
        } catch (Exception e) {
            log.error("获取pno18数据失败", e);

        }
        return NormalResult.createOk(pno18ResponseEntity);
    }


    public NormalResult<Pno12ResponseEntity> getPno12(XapiInstance instance, XapiMap<String, Object> context, NormalParam<Pno12RequestEntity> param) {
        Pno12ResponseEntity pno12ResponseEntity = new Pno12ResponseEntity();
        try {
            //物料pno12拉取接口
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + instance.getRemark();
            String client_id = instance.getSystem().getXapiKv("client_id").getValueTee();
            Map<String, String> params = beanToMap(param.getData());
            String resp = PcmsWebUtil.getData(url, params, client_id, null);
            pno12ResponseEntity = JSONObject.parseObject(resp, Pno12ResponseEntity.class);
        } catch (Exception e) {
            log.error("获取pno18数据失败", e);

        }
        return NormalResult.createOk(pno12ResponseEntity);
    }


    //内饰主数据拉取接口
    public NormalResult<InteriorResponseEntitiy> getInterior(XapiInstance instance, XapiMap<String, Object> context, NormalParam<InteriorRequestEntity> param) {
        InteriorResponseEntitiy interiorResponseEntitiy = new InteriorResponseEntitiy();
        //String url = instance.getSystem().getProvidePath()+instance.getRemark();
        try {
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + instance.getRemark();
            String client_id = instance.getSystem().getXapiKv("client_id").getValueTee();
            Map<String, String> params = beanToMap(param.getData());
            String token = null;
            //token=PcmsWebUtil.getToken();
            String resp = PcmsWebUtil.getData(url, params, client_id, token);
            //String resp="{\"data\": [{\"chineseDesc\": \"蓝色\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"interiorCodeId\": \"A73\"},{\"chineseDesc\": \"中配版耀蓝色\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"interiorCodeId\": \"A70\"}],\"code\": 200,\"msg\": \"success\"}";

            interiorResponseEntitiy = JSONObject.parseObject(resp, InteriorResponseEntitiy.class);
            handelResDataBiz.handelInteriorResp(interiorResponseEntitiy);
        } catch (Exception e) {
            log.error("获取内饰主数据失败", e);
        }
        return NormalResult.createOk(interiorResponseEntitiy);
    }

    //外饰主数据拉取接口
    public NormalResult<ExteriorsResponseEntity> getExteriors(XapiInstance instance, XapiMap<String, Object> context, NormalParam<ExteriorsRequestEntity> param) {
        ExteriorsResponseEntity exteriorsResponseEntity = new ExteriorsResponseEntity();
        //String url = instance.getSystem().getProvidePath()+instance.getRemark();
        try {
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + instance.getRemark();
            String client_id = instance.getSystem().getXapiKv("client_id").getValueTee();
            Map<String, String> params = beanToMap(param.getData());
            String token = null;
            //token=PcmsWebUtil.getToken();
            String resp = PcmsWebUtil.getData(url, params, client_id, token);
            //String resp="{\"data\": [{\"chineseDesc\": \"冰晶白\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"exteriorCodeId\": \"043\"},{\"chineseDesc\": \"汽油灰\",\"platform\": \"-/BX11\",\"vehicletypeDesc\": \"BX11\",\"exteriorCodeId\": \"054\"}],\"code\": 200,\"msg\": \"success\"}";

            exteriorsResponseEntity = JSONObject.parseObject(resp, ExteriorsResponseEntity.class);
            handelResDataBiz.handelExteriorsResp(exteriorsResponseEntity);
        } catch (Exception e) {
            log.error("获取外饰主数据失败", e);
        }
        return NormalResult.createOk(exteriorsResponseEntity);
    }


    //选装主数据拉取接口
    public NormalResult<OptionResponseEntity> getOptional(XapiInstance instance, XapiMap<String, Object> context, NormalParam<ExteriorsRequestEntity> param) {
        OptionResponseEntity optionResponseEntity = new OptionResponseEntity();
        //String url = instance.getSystem().getProvidePath()+instance.getRemark();
        try {
            String url = instance.getSystem().getXapiKv("provide_path").getValueTee() + instance.getRemark();
            String client_id = instance.getSystem().getXapiKv("client_id").getValueTee();
            Map<String, String> params = beanToMap(param.getData());
            String token = null;
            //token=PcmsWebUtil.getToken();
            String resp = PcmsWebUtil.getData(url, params, client_id, token);

            //String resp="{\"data\": [{\"optionalDesc\": \"N CONTRAST\",\"optionalDescCn\": \"无对比颜色\",\"optionCodeId\": \"MF01\"},{\"optionalDesc\": \"N SS\",,\"optionalDescCn\": \"无启/停系统\",\"optionCodeId\": \"BK01\"}],\"code\": 200,\"msg\": \"success\"}";
            optionResponseEntity = JSONObject.parseObject(resp, OptionResponseEntity.class);
            handelResDataBiz.handelOptionalResp(optionResponseEntity);
        } catch (Exception e) {
            log.error("获取外饰主数据失败", e);
        }
        return NormalResult.createOk(optionResponseEntity);
    }


    public static Map<String, String> beanToMap(Object obj) {
        Map<String, String> params = new HashMap<String, String>();
        try {
            PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean();
            PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(obj);
            for (int i = 0; i < descriptors.length; i++) {
                String name = descriptors[i].getName();
                if (!"class".equals(name) && !StringHelper.IsEmptyOrNull(propertyUtilsBean.getNestedProperty(obj, name))) {
                    params.put(name, propertyUtilsBean.getNestedProperty(obj, name) + "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return params;
    }
}
