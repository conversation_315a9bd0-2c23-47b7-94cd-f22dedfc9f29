package com.ly.adp.xapi.api.base.biz;

import com.alibaba.fastjson.JSONObject;
import com.ly.mp.component.helper.StringHelper;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.net.ssl.SSLContext;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

public class PcmsWebUtil {

    private static final Logger log = LoggerFactory.getLogger(PcmsWebUtil.class);

    public static String getData(String url, Map<String, String> param, String client_id, String token) {
        String firstUrl = null;
        String endUrl = null;

        if (!StringHelper.IsEmptyOrNull(param)) {
            firstUrl = url + "?" + formatKv(param, true);
            endUrl = firstUrl.replaceAll("\\s", "+");
        }

        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(endUrl);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add("client_id", client_id);
            if (!StringHelper.IsEmptyOrNull(token)) {
                httpHeaders.add("Authorization", "Bearer" + " " + token);
            }
            //httpHeaders.add("Authorizations","Bearer");
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
            responseEntity = getRestTemplate().exchange(uri, HttpMethod.GET, entity, String.class);

        } catch (URISyntaxException | RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("Pcms接口请求异常", e);
            throw new RuntimeException(e);
        }

        return responseEntity.getBody().toString();
    }

    public static String getToken() {
        /**外网
         * sit: https://pcmsapi-sit-test.smartchina.com.cn
         * uat:https://owspcmsgscapi2bapig-uat-test.smartchina.com.cn
         */
        String endUrl = "https://pcmsapi-sit-test.smartchina.com.cn/auth/api/pcms/1.0/auth/gettoken";
        String ClientId = "68217c92-b6ef-4456-89bf-00532c1d80df";
        String clientSecret = "089dd22e818bfcd843f4b3958d4db49b";
        String UserName = "ADP";
        String Password = getEncrtyPasswd();
        Map<String, String> param = new HashMap<>();
        param.put("ClientId", ClientId);
        param.put("clientSecret", clientSecret);
        param.put("UserName", UserName);
        param.put("Password", Password);
        ResponseEntity<String> responseEntity = null;
        URI uri;
        try {
            uri = new URI(endUrl);
            HttpHeaders httpHeaders = new HttpHeaders();
            //httpHeaders.add("client_id",client_id);
            //httpHeaders.add("Authorizations","Bearer");
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> entity = new HttpEntity<>(param, httpHeaders);
            responseEntity = getRestTemplate().exchange(uri, HttpMethod.POST, entity, String.class);

        } catch (URISyntaxException e) {
            log.error("Pcms接口请求异常", e);
            throw new RuntimeException(e);
        } catch (RestClientException | KeyManagementException | KeyStoreException | NoSuchAlgorithmException e) {
            log.error("Pcms接口请求异常", e);
            throw new RuntimeException(e);
        }
        String res = responseEntity.getBody().toString();
        String token = null;
        Map<String, Object> map = JSONObject.parseObject(res, Map.class);
        if (map.containsKey("code") && "200".equals(map.get("code").toString())) {
            Map<String, Object> dataMap = (Map<String, Object>) map.get("data");
            token = dataMap.get("token").toString();


        } else {
            throw new RuntimeException("获取token失败");
        }
        return token;
    }

    private static String getEncrtyPasswd() {
        return "smart@" + "2021";
    }


    public static RestTemplate getRestTemplate() throws KeyStoreException, NoSuchAlgorithmException, KeyManagementException {
        TrustStrategy acceptingTrustStrategy = (X509Certificate[] chain, String authType) -> true;

        SSLContext sslContext = org.apache.http.ssl.SSLContexts.custom()
                .loadTrustMaterial(null, acceptingTrustStrategy)
                .build();

        SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sslContext);

        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(csf)
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory =
                new HttpComponentsClientHttpRequestFactory();

        requestFactory.setHttpClient(httpClient);
        RestTemplate restTemplate = new RestTemplate(requestFactory);
        return restTemplate;
    }

    public static String formatKv(Map<String, String> messageData, boolean urlEncode) {
        return formatKv(messageData, "&", "=", false, "UTF-8", false);
    }

    public static String formatKv(Map<String, String> messageData, String joiner, String linker,
                                  boolean urlEncode, String encoding, boolean onlyValue) {

        Map<String, String> messageDataSort = new TreeMap<>(messageData);

        StringBuilder message = new StringBuilder();
        for (Map.Entry<String, String> entry : messageDataSort.entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            log.debug("add kv data {}:{}", name, value);

            if (urlEncode) {
                try {
                    name = URLEncoder.encode(entry.getKey(), encoding);
                    value = URLEncoder.encode(entry.getValue(), encoding);
                } catch (UnsupportedEncodingException e) {
                    throw new IllegalArgumentException("url encode failed, encoding is unsupported");
                }
                log.debug("add encoded kv data {}:{}", name, value);
            }

            if (onlyValue) {
                message.append(value).append(joiner);
            } else {

                message.append(name).append(linker).append(value).append(joiner);
            }

        }

        if (message.length() > 0 && joiner.length() > 0) {
            message.delete(message.length() - joiner.length(), message.length());
        }

        return message.toString();
    }
}
