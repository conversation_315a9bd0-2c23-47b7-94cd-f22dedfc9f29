package com.ly.adp.xapi.api.base;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.CspWebUtil;
import com.ly.adp.xapi.api.base.biz.SapWebUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.mp.component.helper.StringHelper;

@SendService("ADP_CSP")
public class CspSendService extends SendDataBase<String>  {
	private static final Logger log = LoggerFactory.getLogger(SapSendService.class);
    
	public Object convertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        //XapiTable mainTable = (XapiTable)context.getValue("mainTable");
        List<Map<String,Object>> list = data.getDataEx();
        List<Map<String,Object>> newList = new ArrayList<Map<String,Object>>();
        
			for(int i = 0;i<list.size();i++)
			{
				Map<String,Object> map = list.get(i);
				if(instance.getObjectId().equals("csp_ifs_complain_order") & !map.containsKey("AddInformation"))
				{
					map.put("AddInformation",new String[] {});
				}
				newList.add(map);
			}
        
        
        String json = JSONObject.toJSONString(newList);
        return json;
    }
	
	 @Override
	 public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object param) throws Exception {
		 
		 String url = xapiInstance.getSystem().getProvidePath() + xapiInstance.getService();
		 Map<String,String> headG =  new HashMap<>();
	     headG.put("sysname",xapiInstance.getSystem().getProvideAccount());
	     headG.put("key",xapiInstance.getSystem().getProvidePassword());
		 return CspWebUtil.postData(url, MediaType.APPLICATION_JSON, headG, param);
	    }

	@Override
	public XapiResult<String> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object data) {
		 XapiResult<String> result = XapiResult.creat();
	        try {
	            String respJson = String.valueOf(data);
	            @SuppressWarnings("unchecked")
				List<Map<String,Object>> list = JSONObject.parseObject(respJson,List.class);
	            Map<String, Object> resp = list.get(0);
	            if (resp.containsKey("success")) {
	             
	                    if("true".equals(String.valueOf(resp.get("success")))){
	                        return result;
	                    }else{
	                        result.setMessage(String.valueOf(resp.get("message")));
	                    }
	                }
	            
	            result.setStatus(1);
	        } catch (Exception e) {
	            log.error("sapSendService解析反馈报文：", e);
	            result.setStatus(1);
	            result.setMessage(e.getMessage());
	        }
	        return result;
	}

}
