package com.ly.adp.xapi.api.util;

import com.ly.adp.xapi.api.baseEntity.EcPolicyStrRequest;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * map转对象工具类
 * <AUTHOR>
 */
public class MapToObjectConverterUtil {

    private static final Logger log = LoggerFactory.getLogger(MapToObjectConverterUtil.class);

    /**
     * 转换实体 EcPolicyStrRequest
     * @param map
     * @return
     */
    public static EcPolicyStrRequest convert(Map<String, Object> map) {
        EcPolicyStrRequest pricing = new EcPolicyStrRequest();

        pricing.setPersonalFleetPrice(getValueFromMap(map, "personalFleetPrice"));
        pricing.setEnterpriseFleetPrice(getValueFromMap(map, "enterpriseFleetPrice"));
        pricing.setCompanyMsrp(getValueFromMap(map, "companyMsrp"));
        pricing.setPersonMsrp(getValueFromMap(map, "personMsrp"));
//        pricing.setCarDiscountPrice(getValueFromMap(map, "carDiscountPrice"));
        pricing.setUserRightsDiscount(getValueFromMap(map, "userRightsDiscount"));
        pricing.setCompanySapDiscount(getValueFromMap(map, "companySapDiscount"));
        pricing.setPersonSapDiscount(getValueFromMap(map, "personSapDiscount"));
        pricing.setCompanyStateSubsidies(getValueFromMap(map, "companyStateSubsidies"));
        pricing.setPersonStateSubsidies(getValueFromMap(map, "personStateSubsidies"));
        pricing.setCompanyDiscountTotalPrice(getValueFromMap(map, "companyDiscountTotalPrice"));
        pricing.setPersonDiscountTotalPrice(getValueFromMap(map, "personDiscountTotalPrice"));
        pricing.setCompanyVehicleAmount(getValueFromMap(map, "companyVehicleAmount"));
        pricing.setPersonVehicleAmount(getValueFromMap(map, "personVehicleAmount"));
        return pricing;
    }

    /**
     * 从map中获取值
     * @param map
     * @param key
     * @return
     */
    private static String getValueFromMap(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (StringHelper.IsEmptyOrNull(value)) {
            return null;
        }
        try {
            return String.valueOf(value.toString());
        } catch (Exception e) {
            // 处理非法数值格式
            log.error("从map获取值异常",e);
            return null;
        }
    }
}
