package com.ly.adp.xapi.api.baseEntity;

/**
 * <AUTHOR>
 * @description: 销售状态
 * @date 2024/11/19
 */
public enum SalesStatusEnum {

    SALES(0, "可售"),

    SALES_ING(1, "售卖中"),

    NO_SALES(2, "不可售"),

    SALESED(3, "已售");

    private Integer code;

    private String desc;
    ;

    SalesStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
