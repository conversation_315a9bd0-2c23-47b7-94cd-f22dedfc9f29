package com.ly.adp.xapi.api.bpm;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/10/26
 */
public class SendDataBaseDecorator<T> extends SendDataBase<Object> {
    @Override
    public Object sendData(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) throws Exception {
        // 需要子类去实现
        return null;
    }

    @Override
    public XapiResult<Object> manageResp(XapiInstance xapiInstance, XapiMap<String, Object> stringObjectXapiMap, Object o) {
        // 需要子类去实现
        return null;
    }

    @Override
    public XapiData improveColumn(XapiInstance instance, XapiMap<String, Object> context, XapiData xData) {
        // 将发送的logs_id保存起来，用于产生错误的时候做标记
        List<String> errorIds = new ArrayList<>();
        for (XapiData.XapiDataRow datum : xData.getData()) {
            errorIds.add(datum.getMainData().get("logs_id").toString());
        }
        context.put("errorIds", errorIds);

        XapiData xapiDataNew = XapiData.creat();
        XapiTable mainTable = (XapiTable) context.getValue("mainTable");
        xData.getData().forEach((datarow) -> {
            XapiData.XapiDataRow newRow = XapiData.XapiDataRow.creat();
            newRow.setMainData(this.fieldToColumn(datarow.getMainData(), mainTable));
            datarow.getChildData().keySet().forEach((childKey) -> {
                newRow.addChild(childKey, this.fieldToColumn((List) datarow.getChildData().get(childKey), childKey));
            });
            xapiDataNew.add(newRow);
        });
        xapiDataNew.getData().forEach((dr) -> {
            Map<String, Object> maindt = (Map) ((HashMap) dr.getMainData()).clone();
            dr.getChildData().keySet().forEach((childKey) -> {
                maindt.put(childKey.getSubFlag(), dr.getChildData().get(childKey));
            });
            xapiDataNew.getDataEx().add(maindt);
        });

        return xapiDataNew;
    }
}
