package com.ly.adp.xapi.api.boot;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.otd.OtdAdpStockCarService;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.ly.mp.component.helper.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Api(tags = "*自定义接口")
@RestController
@RequestMapping("")
public class OtdReceiveController {

    @Resource
    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage passiveServiceManage;

    @Resource
    WhiteListInterceptor whiteListInterceptor;

    @Autowired
    OtdAdpStockCarService otdAdpStockCarService;

    @Autowired
    IXapiLogBiz xapiLogBiz;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @ApiOperation("OTD接收")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "data", value = "数据", required = false, dataType = "String", paramType = "body")}
    )
    @PostMapping("receivesys/otd")
    public Map<String, Object> receiveSys(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();
        result.put("message", "");
        result.put("isSuccess", "N");
        try {
            // 判断数据是否为空
            if (!CollectionUtils.isEmpty(map)) {
                String data = JSONObject.toJSONString(map);
                Map<String, Object> header = (Map<String, Object>) map.get("head");
                String service = Objects.toString(header.get("service"), "");
                XapiInstance instance = xServiceHolder.getInstance(service);
                // 判断是否有对应的配置
                if (instance != null) {
                    AtomicReference<String> respStr = new AtomicReference<>();
                    passiveServiceManage.handle(instance, data, m -> respStr.set((String) m));
                    result = JSON.parseObject(respStr.get(), Map.class);
                } else {
                    String msg = String.format("接口%s不存在", service);
                    logger.error(msg);
                    result.put("isSuccess", "N");
                    result.put("message", msg);
                    dataList.add(result);
                }
            } else {
                result.put("message", "请求数据不能为空");
                result.put("isSuccess", "N");
                dataList.add(result);
            }
        } catch (Exception e) {
            logger.error("接收otd数据发生异常：{}", e.getMessage(), e);
          /*  XapiResponse resp=new XapiResponse();
            resp.setCode("-1");
            resp.setMsg(e.getMessage());
            result = JSON.parseObject(JSONObject.toJSONString(resp),Map.class);*/
            result.put("message", e.getMessage());
            result.put("isSuccess", "N");
            dataList.add(result);
        } finally {
            if (!StringHelper.IsEmptyOrNull(dataList)) {
                Map<String, Object> res = new HashMap<>();
                res.put("data", dataList);
                result = res;
            }

        }
        return result;
    }

    @ApiOperation("OTD现车数据接收")
    @PostMapping("/noauth/rest/otd_adp/otd_adp_stock_car")
    public Map<String, Object> otdAdpStockCar(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        try {
            return otdAdpStockCarService.otdAdpStockCar(map);
        } catch (Exception e) {
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            return result;
        }
    }

    @ApiOperation("trade_in佣金接收")
    @PostMapping("/noauth/rest/tradein_adp/tradein_adp_smartAmount")
    public Map<String, Object> smartAmount(@RequestBody Map<String, Object> map) {
        return otdAdpStockCarService.smartAmount(map);
    }

    @ApiOperation("trade_in置换权益申请单状态")
    @PostMapping("/noauth/rest/tradein_adp/tradein_adp_rightStatus")
    public Map<String, Object> tradeinRightStatus(@RequestBody Map<String, Object> map) {
        return otdAdpStockCarService.tradeinRightStatus(map);
    }

    @ApiOperation("cdp下发电话更新记录")
    @PostMapping("/noauth/rest/cdp_adp/cdp_adp_phone_update")
    public Map<String, Object> cdpAdpPhoneUpdate(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        try {
            return otdAdpStockCarService.cdpAdpPhoneUpdate(map);
        } catch (Exception e) {
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            return result;
        }
    }

    @ApiOperation("订单特殊流程申请撤回接口")
    @PostMapping("/noauth/rest/sdc_adp/sdc_adp_order_revocation")
    public Map<String, Object> sdcAdpOrderRevocation(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        try {
            return otdAdpStockCarService.sdcAdpOrderRevocation(map);
        } catch (Exception e) {
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            return result;
        }
    }

    @ApiOperation("现车政策同步")
    @PostMapping("/noauth/rest/ec_adp/ec_adp_policyDiscount")
    public Map<String, Object> ecAdpPolicyDiscount(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        try {
            return otdAdpStockCarService.ecAdpPolicyDiscount(map);
        } catch (Exception e) {
            result.put("success", false);
            result.put("msg", e.getMessage());
            result.put("code","0");
            XapiLog loginfo = new XapiLog();
            loginfo.setLogStatus("0");
            loginfo.setLabel("ec_adp_policyDiscount");
            loginfo.setInJson(JSONObject.toJSONString(map));
            loginfo.setOutJson(JSONObject.toJSONString(result));
            xapiLogBiz.add(loginfo);
            return result;
        }
    }
}