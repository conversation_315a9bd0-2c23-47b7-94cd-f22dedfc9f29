package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSON;
import com.ly.adp.xapi.api.base.mapper.BasePcmsResMapper;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.plugin.ext.normal.XapiNormalInsert;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@NormalService("receive_leads")
public class TransferService extends NormalDataGenericBase {

    private static final Logger log = LoggerFactory.getLogger(TransferService.class);

    @Autowired
    BasePcmsResMapper basePcmsResMapper;
    @Autowired
    XapiNormalInsert xapiNormalInsert;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
        handlers.put("transfer_001", this::normalinsert);//任务注册绑定，编码对应配置中service字段
        //handlers.put("transfer_001", xapiNormalInsert);
    }

    public NormalResult<Object> normalinsert(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        NormalResult<List<Map<String, Object>>> result = new NormalResult<>();
        String data = param.getData();
        //List<Map<String,Object>> info =(List<Map<String, Object>>)JSONArray.parse(data);
        Map<String, Object> map1= (Map)JSON.parseObject(data);
        List<Map<String, Object>> info =(List<Map<String, Object>>)map1.get("param");
        if (StringHelper.IsEmptyOrNull(info) && info.size() == 0) {
            BusicenException.create("参数不能为空");
        }
        List<Map<String, Object>> errList = new ArrayList<>();
        int i = 0;
        for (Map<String, Object> map : info) {
            Map<String, Object> transferMap = new HashMap<>();
            if (!StringHelper.IsEmptyOrNull(map.get("custName"))) {
                transferMap.put("custName", map.get("custName"));
            }
            if (!StringHelper.IsEmptyOrNull(map.get("applyDesc"))) {
                transferMap.put("applyDesc", map.get("applyDesc"));
            }
            if (!StringHelper.IsEmptyOrNull(map.get("applyPerson"))) {
                transferMap.put("applyPerson", map.get("applyPerson"));
            }
            transferMap.put("phone", map.get("phone"));
            transferMap.put("applyDate", map.get("applyDate"));
            transferMap.put("inDlrCode", map.get("inDlrCode"));
            transferMap.put("inDlrName", map.get("inDlrName"));
            transferMap.put("source", "CEC");
            transferMap.put("logsId", StringHelper.GetGUID());
            transferMap.put("xapibatchno", StringHelper.GetGUID());

            try {
                int i1 = basePcmsResMapper.insertTransfer(transferMap);
                i = i + i1;
            } catch (Exception e) {
                e.printStackTrace();
                log.error("transfer_001" + e);
                errList.add(transferMap);
            }
        }
        result.setMessage("总条数：" + info.size() + "条,成功" + i + "条,失败" + errList.size() + "条");
//        result.setMessage("操作成功");
        result.setSuccess(true);
        result.setData(errList);
        NormalResult<Object> normalResult = NormalResult.createOk(result);
        return normalResult;
    }
}
