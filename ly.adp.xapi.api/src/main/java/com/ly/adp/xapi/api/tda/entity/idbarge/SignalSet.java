package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 录音模式切换事件
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class SignalSet implements Serializable {

    private static final long serialVersionUID = 2947846826160318518L;

    /**
     * 触发时间
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 录音模式（omnidirectional：双向，directional：单向）
     */
    @JsonProperty("mode")
    private String mode;

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }
}
