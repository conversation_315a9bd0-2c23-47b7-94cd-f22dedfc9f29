package com.ly.adp.xapi.api.normal;

import com.ly.bucn.component.xapi.plugin.ext.normal.XapiNormalInsert;
import com.ly.bucn.component.xapi.service.normal.NormalDataGenericBase;
import com.ly.bucn.component.xapi.service.normal.NormalFunctionGeneric;
import com.ly.bucn.component.xapi.service.normal.NormalService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@NormalService("ADP_INSERT")
public class AdpInsertNormal extends NormalDataGenericBase {

    @Autowired
    XapiNormalInsert xapiNormalInsert;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> map) {
        map.put("xapinormalinsert", xapiNormalInsert);
    }
}
