package com.ly.adp.xapi.api.otd;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.CdpNormalService;
import com.ly.adp.xapi.api.otd.mapper.OtdResMapper;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.log.db.XapiLogDb;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@NormalService("otd_adp")
public class OtdNormalService extends NormalDataGenericBase {

    private static final Logger log = LoggerFactory.getLogger(CdpNormalService.class);
    @Autowired
    OtdResMapper otdResMapper;
    @Autowired
    XapiLogDb xapiLogDb;
    @Autowired
    OtdAdpStockCarService otdAdpStockCarService;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
        //handlers.put("otd_adp_stock_car", otdAdpStockCarService::otdAdpStockCar);//订单解配申请otd传输adp
    }

    @Transactional(rollbackFor = Exception.class)
    public NormalResult<Map<String, Object>> otdAdpStockCar(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        Map<String, Object> data = new HashMap<>();
        String info = param.getData();
        Map<String, Object> map = JSONObject.parseObject(info, Map.class);
        log.info("otd现车推送入参：" + info);
        int i = 1;
        try {
            List<Map<String, Object>> list = (List<Map<String, Object>>) map.get("data");
            for (Map<String, Object> paramMap : list) {

                if (StringHelper.IsEmptyOrNull(paramMap.get("PON18"))) {
                    throw new BusicenException("PNO18不能为空");
                    //responseMassage("0", result, data, "PNO18不能为空");
                    //return result;
                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("VIN"))) {
                    throw new BusicenException("VIN不能为空");
                    //responseMassage("0", result, data, "VIN不能为空");
                    //return result;
                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("IS_SALES"))) {
                    throw new BusicenException("IS_SALES不能为空");
                    //responseMassage("0", result, data, "IS_SALES不能为空");
                    //return result;
                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("STORE_HOUSE"))) {
                    throw new BusicenException("STORE_HOUSE不能为空");
                    //responseMassage("0", result, data, "STORE_HOUSE不能为空");
                    //return result;
                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("CAR_TYPE"))) {
                    throw new BusicenException("CAR_TYPE不能为空");
                    //responseMassage("0", result, data, "CAR_TYPE不能为空");
                    //return result;
                }
                if (StringHelper.IsEmptyOrNull(paramMap.get("IN_STORE_TIME"))) {
                    throw new BusicenException("IN_STORE_TIME不能为空");
                    //responseMassage("0", result, data, "IN_STORE_TIME不能为空");
                    //return result;
                }

                //查重，判断是否新增（现车表：t_orc_stock_vehicle）
                Map<String, Object> mapCount = new HashMap<>();
                mapCount.put("vin", paramMap.get("VIN"));
                List<Map<String, Object>> stockCarCount = otdResMapper.selectStockCarCount(mapCount);

                String stockCarId = StringHelper.GetGUID();
                Map<String, Object> stockCarMap = new HashMap<>();
                stockCarMap.put("pno18", paramMap.get("PON18"));
                stockCarMap.put("stockDealerCode", paramMap.get("STORE_HOUSE"));
                stockCarMap.put("vin", paramMap.get("VIN"));
                stockCarMap.put("salesStatus", paramMap.get("IS_SALES"));
                stockCarMap.put("stockDate", paramMap.get("IN_STORE_TIME"));
                stockCarMap.put("vehicleType", paramMap.get("CAR_TYPE"));
                if (stockCarCount.size() > 0) {
                    //修改
                    stockCarMap.put("stockCarId", stockCarCount.get(0).get("stockCarId"));
                    otdResMapper.updateStockCar(stockCarMap);
                } else {
                    //新增
                    stockCarMap.put("stockCarId", stockCarId);
                    otdResMapper.indertStockCar(stockCarMap);
                }


                //新增现车售卖范围表(存在则删除再新增：t_orc_stock_vehicle_scope)
                Map<String, Object> scopeMap = new HashMap<>();
                scopeMap.put("stockCarId", stockCarMap.get("stockCarId"));
                scopeMap.put("vin", stockCarMap.get("vin"));
                //scopeMap.put("dealerCode",stockCarMap.get("stockDealerCode"));
                int count = otdResMapper.selectDetailCount(scopeMap);
                if (count > 0) {
                    otdResMapper.deleteDetail(scopeMap);
                }
                if (!"VDC".equals(stockCarMap.get("stockDealerCode"))) {
                    List<String> dlrCodeList = otdResMapper.selectDlrCodeList(stockCarMap);
                    for (String dlrCode : dlrCodeList) {
                        scopeMap.put("dealerCode", dlrCode);
                        otdResMapper.insertDetail(scopeMap);
                    }
                } else {
                    scopeMap.put("dealerCode", stockCarMap.get("stockDealerCode"));
                    otdResMapper.insertDetail(scopeMap);
                }
                i++;
            }
        } catch (Exception e) {
            log.error(e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            responseMassage(false, result, data, "第" + i + "行数据传输失败," + e.getMessage());
            return result;
        }
        responseMassage(true, result, data, "成功");
        return result;
    }

    private void responseMassage(boolean code, NormalResult<Map<String, Object>> result, Map<String, Object> map, String errorMsg) {
        map.put("success", code);
        map.put("errMsg", errorMsg);
        result.setData(map);
        if (!code) {
            result.setSuccess(false);
        } else {
            result.setSuccess(true);
        }
        result.setMessage(errorMsg);
    }

}

