package com.ly.adp.xapi.api.boot;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.config.SignInterceptor;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.config.token.AuthInterceptor;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.ly.bucn.component.xapi.service.normal.NormalParam;
import com.ly.bucn.component.xapi.service.receive.ReceiveController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.concurrent.atomic.AtomicReference;

@Api(tags = "*REST-NOAUTH", value = "REST-NOAUTH", description = "数据接收-")
@Controller
public class NormalExController {

    private Logger logger = LoggerFactory.getLogger(ReceiveController.class);

    @Resource
    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage<NormalParam<String>, String> passiveServiceManage;

    @Resource
    WhiteListInterceptor whiteListInterceptor;

    @ApiOperation("rest")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "system", value = "系统编码", required = false, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "service", value = "接口编码", required = false, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "data", value = "数据", required = false, dataType = "String", paramType = "body")}
    )
    @RequestMapping(value = "/noauth/rest/{system}/{service}", method = RequestMethod.POST)
    public void normal(HttpServletRequest request, HttpServletResponse response, @PathVariable("system") String system, @PathVariable("service") String service) throws Exception {
        AtomicReference<String> respStr = new AtomicReference<>();

        try {
            XapiInstance instance = xServiceHolder.getInstance(service);

            try (BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), "UTF-8"))) {
                StringBuilder responseStrBuilder = new StringBuilder();
                String inputStr;
                while ((inputStr = streamReader.readLine()) != null)
                    responseStrBuilder.append(inputStr);
                String data = responseStrBuilder.toString();
                NormalParam<String> p = new NormalParam<>();
                p.setData(data);
                passiveServiceManage.handle(instance, p, m -> respStr.set(m));
            } catch (Exception e) {
                logger.error("{}-{} 执行异常", system, service, e);
            }

            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/json; charset=utf-8");
            String errResp = String.format("[%s]执行异常,请联系接口负责人", instance.getLabel());
            response.getWriter().print(respStr.get() == null ? errResp : respStr.get());
            response.getWriter().flush();
            response.getWriter().close();
        } catch (Exception e) {
            logger.error("{}-{} 执行异常", system, service, e);
            response.setCharacterEncoding("UTF-8");

            response.getWriter().print(e.getMessage());
            response.getWriter().flush();
            response.getWriter().close();
        } finally {

        }

    }
}
