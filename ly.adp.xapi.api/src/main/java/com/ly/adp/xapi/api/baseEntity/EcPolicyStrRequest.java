package com.ly.adp.xapi.api.baseEntity;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.lang.String;

/**
 * <AUTHOR>
 * @description:ec/otd现车接口查询  ec政策接口字段
 * @date 2023/4/3
 */
public class EcPolicyStrRequest implements Serializable {
    private static final long serialVersionUID = 7646223514161632459L;

    @ApiModelProperty("个人批售金额")
    private String personalFleetPrice;

    @ApiModelProperty("企业批售金额")
    private String enterpriseFleetPrice;

    @ApiModelProperty("企业msrp价格")
    private String companyMsrp;

    @ApiModelProperty("个人msrp价格")
    private String personMsrp;

//    @ApiModelProperty(value = "现车优惠金额")
//    private String carDiscountPrice;

    @ApiModelProperty("大定权益优惠")
    private String userRightsDiscount;

    @ApiModelProperty("企业sap优惠折扣")
    private String companySapDiscount;

    @ApiModelProperty("个人sap优惠折扣")
    private String personSapDiscount;

    @ApiModelProperty("企业国家补贴")
    private String companyStateSubsidies;

    @ApiModelProperty("个人国家补贴")
    private String personStateSubsidies;

    @ApiModelProperty("企业优惠总额")
    private String companyDiscountTotalPrice;

    @ApiModelProperty("个人优惠总额")
    private String personDiscountTotalPrice;

    @ApiModelProperty("企业车辆总价/企业实际车价")
    private String companyVehicleAmount;

    @ApiModelProperty("个人车辆总价/个人实际车价")
    private String personVehicleAmount;

    public String getPersonalFleetPrice() {
        return personalFleetPrice;
    }

    public void setPersonalFleetPrice(String personalFleetPrice) {
        this.personalFleetPrice = personalFleetPrice;
    }

    public String getEnterpriseFleetPrice() {
        return enterpriseFleetPrice;
    }

    public void setEnterpriseFleetPrice(String enterpriseFleetPrice) {
        this.enterpriseFleetPrice = enterpriseFleetPrice;
    }

    public String getCompanyMsrp() {
        return companyMsrp;
    }

    public void setCompanyMsrp(String companyMsrp) {
        this.companyMsrp = companyMsrp;
    }

    public String getPersonMsrp() {
        return personMsrp;
    }

    public void setPersonMsrp(String personMsrp) {
        this.personMsrp = personMsrp;
    }

//    public String getCarDiscountPrice() {
//        return carDiscountPrice;
//    }
//
//    public void setCarDiscountPrice(String carDiscountPrice) {
//        this.carDiscountPrice = carDiscountPrice;
//    }

    public String getUserRightsDiscount() {
        return userRightsDiscount;
    }

    public void setUserRightsDiscount(String userRightsDiscount) {
        this.userRightsDiscount = userRightsDiscount;
    }

    public String getCompanySapDiscount() {
        return companySapDiscount;
    }

    public void setCompanySapDiscount(String companySapDiscount) {
        this.companySapDiscount = companySapDiscount;
    }

    public String getPersonSapDiscount() {
        return personSapDiscount;
    }

    public void setPersonSapDiscount(String personSapDiscount) {
        this.personSapDiscount = personSapDiscount;
    }

    public String getCompanyStateSubsidies() {
        return companyStateSubsidies;
    }

    public void setCompanyStateSubsidies(String companyStateSubsidies) {
        this.companyStateSubsidies = companyStateSubsidies;
    }

    public String getPersonStateSubsidies() {
        return personStateSubsidies;
    }

    public void setPersonStateSubsidies(String personStateSubsidies) {
        this.personStateSubsidies = personStateSubsidies;
    }

    public String getCompanyDiscountTotalPrice() {
        return companyDiscountTotalPrice;
    }

    public void setCompanyDiscountTotalPrice(String companyDiscountTotalPrice) {
        this.companyDiscountTotalPrice = companyDiscountTotalPrice;
    }

    public String getPersonDiscountTotalPrice() {
        return personDiscountTotalPrice;
    }

    public void setPersonDiscountTotalPrice(String personDiscountTotalPrice) {
        this.personDiscountTotalPrice = personDiscountTotalPrice;
    }

    public String getCompanyVehicleAmount() {
        return companyVehicleAmount;
    }

    public void setCompanyVehicleAmount(String companyVehicleAmount) {
        this.companyVehicleAmount = companyVehicleAmount;
    }

    public String getPersonVehicleAmount() {
        return personVehicleAmount;
    }

    public void setPersonVehicleAmount(String personVehicleAmount) {
        this.personVehicleAmount = personVehicleAmount;
    }
}
