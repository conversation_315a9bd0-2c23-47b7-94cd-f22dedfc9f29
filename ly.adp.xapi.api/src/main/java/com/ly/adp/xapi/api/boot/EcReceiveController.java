package com.ly.adp.xapi.api.boot;

import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.config.WhiteListInterceptor;
import com.ly.bucn.component.xapi.kernal.model.XServiceHolder;
import com.ly.bucn.component.xapi.service.common.PassiveServiceManage;
import com.ly.mp.component.helper.StringHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Api(tags = "*自定义接口",description="接收")
@RestController
@RequestMapping("")
public class EcReceiveController {
    @Resource
    XServiceHolder xServiceHolder;

    @Resource
    PassiveServiceManage passiveServiceManage;

    @Resource
    WhiteListInterceptor whiteListInterceptor;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @ApiOperation("EC接收")
    @ApiImplicitParams({
            @ApiImplicitParam(name="data",value="数据",required=false,dataType="String",paramType="body")}
    )
    @PostMapping("receivesys/ec")
    public Map<String,Object> receiveSys(@RequestBody Map<String,Object> map) {
        Map<String,Object> result = new HashMap<>();
        List<Map<String,Object>> dataList=new ArrayList<>();
        result.put("message","");
        result.put("isSuccess","N");
        try {
            // 判断数据是否为空
            if(!CollectionUtils.isEmpty(map)){
                String data = JSONObject.toJSONString(map);
                Map<String, Object> header = (Map<String, Object>) map.get("head");
                String service = Objects.toString(header.get("service"),"");
                XapiInstance instance = xServiceHolder.getInstance(service);
                // 判断是否有对应的配置
                if(instance!=null){
                    AtomicReference<String> respStr = new AtomicReference<>();
                    passiveServiceManage.handle(instance, data, m -> respStr.set((String) m));
                    result = JSON.parseObject(respStr.get(),Map.class);
                }else {
                    String msg = String.format("接口%s不存在",service);
                    logger.error(msg);
                    result.put("isSuccess","N");
                    result.put("message",msg);
                    dataList.add(result);
                }
            }else{
                result.put("message","请求数据不能为空");
                result.put("isSuccess","N");
                dataList.add(result);
            }
        } catch (Exception e) {
            logger.error("接收ec数据发生异常：{}",e.getMessage(),e);
          /*  XapiResponse resp=new XapiResponse();
            resp.setCode("-1");
            resp.setMsg(e.getMessage());
            result = JSON.parseObject(JSONObject.toJSONString(resp),Map.class);*/
            result.put("message",e.getMessage());
            result.put("isSuccess","N");
            dataList.add(result);
        } finally {
            if(!StringHelper.IsEmptyOrNull(dataList)){
                Map<String,Object> res = new HashMap<>();
                res.put("data",dataList);
                result = res;
            }

        }
        return result;
    }
}
