package com.ly.adp.xapi.api.acc;

import com.ly.bucn.component.xapi.kernal.model.XapiReceiveResponse;

import java.util.ArrayList;
import java.util.List;

public class XapiReceiveResponseMessage extends XapiReceiveResponse {

    private List<XapiReceiveResponseMessage.XapiReceiveResponseMessData> message = new ArrayList();
    private List<XapiReceiveResponseMessage.XapiReceiveResponseErrData> errors = new ArrayList();

    public XapiReceiveResponseMessage() {
    }

    public static XapiReceiveResponseMessage creat() {
        return new XapiReceiveResponseMessage();
    }

    public void addSuccess(final Object id,final Object adpActivityId, final String errorMsg) {
        this.message.add(new XapiReceiveResponseMessage.XapiReceiveResponseMessData() {
            {
                this.setId(id);
                this.setAdpActivityId(adpActivityId);
                this.setErrmsg(errorMsg);
            }
        });
    }

    public void addErrors(final Object id, final String errorMsg) {
        this.errors.add(new XapiReceiveResponseMessage.XapiReceiveResponseErrData() {
            {
                this.setId(id);
                this.setErrmsg(errorMsg);
            }
        });
    }

    public List<XapiReceiveResponseMessage.XapiReceiveResponseMessData> getSuccMessage() {
        return this.message;
    }

    public void setSuccMessage(List<XapiReceiveResponseMessage.XapiReceiveResponseMessData> message) {
        this.message = message;
    }

    public List<XapiReceiveResponseMessage.XapiReceiveResponseErrData> getErrors() {
        return this.errors;
    }

    public void setErrors(List<XapiReceiveResponseMessage.XapiReceiveResponseErrData> errors) {
        this.errors = errors;
    }


    public class XapiReceiveResponseMessData {
        private Object id;
        private Object adpActivityId;
        private String errmsg;

        public XapiReceiveResponseMessData() {
        }

        public Object getId() {
            return id;
        }

        public void setId(Object id) {
            this.id = id;
        }

        public Object getAdpActivityId() {
            return adpActivityId;
        }

        public void setAdpActivityId(Object adpActivityId) {
            this.adpActivityId = adpActivityId;
        }

        public String getErrmsg() {
            return errmsg;
        }

        public void setErrmsg(String errmsg) {
            this.errmsg = errmsg;
        }
    }
}
