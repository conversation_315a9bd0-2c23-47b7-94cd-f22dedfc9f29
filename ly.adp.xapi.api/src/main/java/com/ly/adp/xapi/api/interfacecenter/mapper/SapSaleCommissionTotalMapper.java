package com.ly.adp.xapi.api.interfacecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ly.adp.xapi.api.interfacecenter.entities.SapSaleCommissionTotal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/7/18
 * @Version 1.0.0
 **/
@Mapper
public interface SapSaleCommissionTotalMapper extends BaseMapper<SapSaleCommissionTotal> {
    int batchInsertCommissionTotal(@Param("list") List<SapSaleCommissionTotal> sapSaleCommissionTotalList);
}
