package com.ly.adp.xapi.api.ms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.common.ms.RabbitMQPool;
import com.ly.bucn.component.ms.MsContextSimple;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.busicen.common.util.EntityResultBuilder;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.helper.StringHelper;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.BuiltinExchangeType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

@NormalService("ADP_MS_PUBLIC")
public class AdpNormalMsPublic extends NormalDataGenericBase {
    Logger logger = LoggerFactory.getLogger(AdpNormalMsPublic.class);

    @Autowired
    MsContextSimple msContextSimple;

    @Autowired
    RabbitMQPool rabbitMQPool;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> map) {
        map.put("ms", this::accept);
    }

    public NormalResult<EntityResult<List<String>>> accept(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        List<String> mqs = new ArrayList<>();
        List<String> mqse = new ArrayList<>();
        if (StringHelper.IsEmptyOrNull(instance.getRemark())) {
            logger.error("xapi [{}] adp ms exchange is absent", instance.getLabel());
            EntityResult<List<String>> entityResult = EntityResultBuilder.<List<String>>create().result("0").msg("exchange is absent").build();
            NormalResult<EntityResult<List<String>>> result = NormalResult.createOk(entityResult);
            result.setSuccess(false);
            result.setMessage("exchange is absent");
            result.setData(entityResult);
            return result;
        }
        String[] mqConfig = instance.getRemark().split("\\|");
        String group = mqConfig[0];//分组
        String exchange = mqConfig[1];//绑定的交换机

        try {
            //json转map
            Map<String, Object>  jsonMap= JSON.parseObject(param.getData());
            Map<String, Object> header = (Map<String, Object>) jsonMap.get("head");
            Map<String, Object> body = (Map<String, Object>) jsonMap.get("body");

            String mqBody= JSONObject.toJSONString(body);

            rabbitMQPool.publishTemplate(group, channel -> {
                AMQP.BasicProperties rabbitProperties = new AMQP.BasicProperties().builder()
                        .messageId(UUID.randomUUID().toString())
                        .contentEncoding("UTF-8")
                        .headers(header)
                        .deliveryMode(2)
                        .contentType("json").build();

                //long start = System.currentTimeMillis();
                //fanout模式
                channel.exchangeDeclare(exchange, BuiltinExchangeType.FANOUT, true);
                channel.basicPublish(exchange, "", rabbitProperties, mqBody.getBytes("UTF-8"));
            });
            mqs.add(instance.getRemark());
        } catch (Exception e) {
            mqse.add(instance.getRemark());
            logger.error("adp mq send [{}][{}] error", instance.getLabel(), instance.getRemark(), e);
        }

        EntityResult<List<String>> entityResult = EntityResultBuilder.<List<String>>creatOk().rows(mqs).build();
        NormalResult<EntityResult<List<String>>> result = NormalResult.createOk(entityResult);
        if (!mqse.isEmpty()) {
            entityResult.setResult("0");
            result.setSuccess(false);
        }
        return result;
    }
}
