package com.ly.adp.xapi.api.tda;

import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.biz.CaseUtil;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiReceiveRequest;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.receive.ReceiveDataBase;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/9
 */
public class ReceiveDataBaseDecorator<T> extends ReceiveDataBase<String> {

    /**
     * 在错误信息里增加ID属性
     * 用于标记第几条错误
     * ID属性值来自于调用方
     */
    @Override
    public XapiResult<String> insertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        XapiResult<String> xapiResult = new XapiResult<String>();
        data.getData().forEach((d) -> {
            XapiResult.XapiResultData<String> resultData = this.Insert(instance, context, d);
            if (!resultData.isSuccess()) {
                Object id = d.getMainData().get("ID");
                if (id != null) {
                    resultData.setId(id.toString());
                }

                xapiResult.addErrorData(resultData);
            }

        });
        return xapiResult;
    }

    /**
     * 将请求的参数统一转化为大写
     * 达到兼容小写的目的
     */
    @Override
    public XapiData convertData(XapiInstance instance, XapiMap<String, Object> context, Object data) {
        String json = (String) data;
        List<XapiTable> childTables = context.getValue("childTables");
        XapiData xData = XapiData.creat();
        XapiReceiveRequest requestObj = JSONObject.parseObject(json, XapiReceiveRequest.class);
        if (requestObj.getHead().containsKey("serialnum")) {
            String serialNum = String.valueOf(requestObj.getHead().get("serialnum"));
            XapiLog logInfo = context.getValue("loginfo");
            logInfo.setSerialNum(serialNum);
        }

        List<Map<String, Object>> body = requestObj.getBody();
        body.forEach((m) -> {
            XapiData.XapiDataRow dr = XapiData.XapiDataRow.creat();
            // 转大写
            dr.setMainData(CaseUtil.transMapKeyToUpperCase(m));
            childTables.forEach((n) -> {
                String firstFlag = n.getSubFlag();
                List<Map<String, Object>> childData = (List) m.get(firstFlag);
                m.remove(firstFlag);
                if (CollectionUtils.isEmpty(childData)) {
                    return;
                }

                List<Map<String, Object>> childDataIgnoreCase = new ArrayList<>(childData.size());
                for (Map<String, Object> childDatum : childData) {
                    childDataIgnoreCase.add(CaseUtil.transMapKeyToUpperCase(childDatum));
                }

                dr.addChild(n, childDataIgnoreCase);
            });
            xData.add(dr);
        });

        return xData;
    }

    /**
     * 兼容小写问题
     * 全局将ID加到参数中(如果调用方请求了的话)
     */
    @Override
    public Map<String, Object> fieldToColumn(Map<String, Object> data, XapiTable xTable) {
        Map<String, Object> result = new HashMap<>();
        xTable.getFields().stream().filter((m) -> {
            return "1".equals(m.getState());
        }).forEach((field) -> {
            String viewFieldUperCase = field.getViewField().trim().toUpperCase();
            if (data.containsKey(viewFieldUperCase)) {
                result.put(field.getSqlParaName().trim(), data.get(viewFieldUperCase));
            } else {
                result.put(field.getSqlParaName().trim(), (Object) null);
            }

        });

        Object id = data.get("ID");
        if (id != null) {
            result.put("ID", id.toString());
        }

        return result;
    }
}