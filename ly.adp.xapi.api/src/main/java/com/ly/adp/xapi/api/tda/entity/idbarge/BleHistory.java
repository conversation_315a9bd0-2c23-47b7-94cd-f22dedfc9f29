package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 蓝牙操作历史
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class BleHistory implements Serializable {

    private static final long serialVersionUID = 3900111453781468918L;

    /**
     * 连接时间
     */
    @JsonProperty("start_ts")
    private long startTs;

    /**
     * 断开时间
     */
    @JsonProperty("stop_ts")
    private long stopTs;

    public long getStartTs() {
        return startTs;
    }

    public void setStartTs(long startTs) {
        this.startTs = startTs;
    }

    public long getStopTs() {
        return stopTs;
    }

    public void setStopTs(long stopTs) {
        this.stopTs = stopTs;
    }
}
