package com.ly.adp.xapi.api.base;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.bucn.component.valid.field.ValidStringUtil;
import com.ly.bucn.component.valid.field.ValidsContextSimple;
import com.ly.bucn.component.valid.field.ValidsDatasAdapter;
import com.ly.bucn.component.valid.field.ValidsResult;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.entity.XapiTable;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiReceiveRequest;
import com.ly.bucn.component.xapi.plugin.service.reactiveplus.*;
import com.ly.mp.busicen.common.context.SwitchDbInvoke;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@ReactivePlusService("adp_common")
public class CommonReactiveService extends ReactivePlusDataBase {
    //分页
   /* @Override
    public XapiReactiveRequestPage<Map<String, Object>> wrapParam(XapiInstance arg0, XapiMap<String, Object> arg1,
                                                                  XapiReactiveRequestPage<Map<String, Object>> arg2) {
        IPage<Map<String,Object>> __page = new Page<>(arg2.getPageIndex(),arg2.getPageSize());
        arg2.getBody().put("__page", __page);
        arg1.put("__page", __page);
        return arg2;
    }

    @Override
    public XapiReactiveResponsePage<Object> wrapResult(XapiInstance arg0, XapiMap<String, Object> arg1,
                                                       XapiReactiveResponsePage<Object> arg2) {
        IPage<Map<String,Object>> __page = arg1.getValue("__page");
        arg2.setPages(new Long(__page.getPages()).intValue());
        arg2.setTotal(__page.getTotal());
        return arg2;
    }*/
    //不分页
    @Override
    public XapiReactiveRequestPage<Map<String, Object>> wrapParam(XapiInstance arg0, XapiMap<String, Object> arg1,
                                                                  XapiReactiveRequestPage<Map<String, Object>> arg2) {
        // TODO Auto-generated method stub
        return arg2;
    }

    @Override
    public XapiReactiveResponsePage<Object> wrapResult(XapiInstance arg0, XapiMap<String, Object> arg1,
                                                       XapiReactiveResponsePage<Object> arg2) {
        // TODO Auto-generated method stub
        return arg2;
    }

    @Override
    public XapiReactiveResponsePage<Object> queryData(XapiInstance instance, XapiMap<String, Object> context,
                                                      XapiReactiveRequestPage<Map<String, Object>> param) {

        return SwitchDbInvoke.invoke("base", () -> {

            return super.queryData(instance, context, param);
        });
    }

    public ReactivePlusCheckResult checkParam(XapiInstance instance, XapiMap<String, Object> xapiMap, XapiReactiveRequestPage<Map<String, Object>> data) {
        if (data.getBody() != null) {
            ((Map)data.getBody()).put("__pageIndex", data.getPageIndex());
            ((Map)data.getBody()).put("__pageSize", data.getPageSize());
        }

        ReactivePlusCheckResult checkResult = new ReactivePlusCheckResult();
        checkResult.setChecked(true);

        if(data.getHead().containsKey("system")){
            try {
                if(!Flux.fromArray(instance.getSystem().getXapiKv("system").getValueTee().split(",")).any(m->data.getHead().get("system").equals(m)).block()){
                  throw new RuntimeException("系统编码不存在");
                }
            } catch (Exception e) {
                checkResult.setChecked(false);
                checkResult.setErrMsg("系统编码不存在");
                return checkResult;
            }
        }else{
            checkResult.setChecked(false);
            checkResult.setErrMsg("系统编码不存在");
            return checkResult;
        }

        XapiTable paramTable = (XapiTable)xapiMap.getValue("paramTable");
        if (paramTable.getFields() == null) {
            return checkResult;
        } else {
            ValidsDatasAdapter adapter = ValidsDatasAdapter.adapter();
            paramTable.getFields().stream().filter((n) -> {
                return "1".equals(n.getState());
            }).forEach((f) -> {
                String field = f.getViewField();
                String[] valids = ValidStringUtil.splitToArray(f.getSqlParaName(), "##");
                adapter.validsData(field, valids);
            });
            ValidsResult validsResult = ValidsContextSimple.ctx().valid(data.getBody(), adapter.build());
            checkResult.setChecked(validsResult.isValid());
            checkResult.setErrMsg(validsResult.getMessage());
            return checkResult;
        }
    }

}
