package com.ly.adp.xapi.api.ms;

import com.ly.adp.common.entity.ParamBase;
import com.ly.adp.common.entity.ParamPage;
import com.ly.adp.common.util.BucnInvoker;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("ly/adp/xapi/mslog")
public class AdpMsLogController {

    @Autowired
    AdpMsLogBiz adpMsLogBiz;

    @GetMapping("pull.do")
    public EntityResult<String> pullMessage(String queue){
        return BucnInvoker.doEntity(()->adpMsLogBiz.pullMessage(queue));
    }

    @PostMapping("apilabel.do")
    public ListResult<Map<String, Object>> apiLabel(@RequestBody ParamPage<Map<String, Object>> paramPage) {
        return BucnInvoker.doList(() -> adpMsLogBiz.apiLabel(paramPage));
    }

    @PostMapping("log.do")
    public ListResult<XapiLog> msLog(@RequestBody ParamPage<XapiLog> paramPage) {
        return BucnInvoker.doList(() -> adpMsLogBiz.msLog(paramPage));
    }

    @PostMapping("resendByIds.do")
    public OptResult resendByIds(@RequestBody ParamBase<List<String>> paramBase) {
        return BucnInvoker.doOpt(() -> adpMsLogBiz.resendByIds(paramBase));
    }

    @PostMapping("resendByParam.do")
    public OptResult resendByParam(@RequestBody ParamPage<XapiLog> paramPage) {
        return BucnInvoker.doOpt(() -> adpMsLogBiz.resendByParam(paramPage));
    }

    @PostMapping("send.todo")
    public EntityResult<String> sendMessage(String group, String exchange , @RequestBody String json){
        return BucnInvoker.doEntity(()->adpMsLogBiz.sendMessage(group, exchange, json));
    }

}
