package com.ly.adp.xapi.api.ms;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.common.ms.RabbitMQPool;
import com.ly.bucn.component.ms.MsContextSimple;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.busicen.common.util.EntityResultBuilder;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.helper.StringHelper;
import com.rabbitmq.client.AMQP;
import com.rabbitmq.client.BuiltinExchangeType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import java.util.*;

@NormalService("ADP_MS")
public class AdpNormalMs extends NormalDataGenericBase {

    Logger logger = LoggerFactory.getLogger(AdpNormalMs.class);

    @Value("${adp.rabbitmq.direct.exchange:smart.ex.adp.public}")
    private String exchangeName;

    @Autowired
    MsContextSimple msContextSimple;

    @Autowired
    RabbitMQPool rabbitMQPool;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> map) {
        map.put("ms", this::accept);
    }

    public NormalResult<EntityResult<List<String>>> accept(XapiInstance instance, XapiMap<String, Object> contxt,
                                                           NormalParam<String> param) {
        logger.info("消息发送参数值：{}，{}", JSONObject.toJSONString(instance), JSONObject.toJSONString(param));
        List<String> mqs = new ArrayList<>();
        List<String> mqse = new ArrayList<>();
        if (StringHelper.IsEmptyOrNull(instance.getRemark())) {
            logger.error("xapi [{}] adp ms exchange is absent", instance.getLabel());
            EntityResult<List<String>> entityResult = EntityResultBuilder.<List<String>>create().result("0").msg("exchange is absent").build();
            NormalResult<EntityResult<List<String>>> result = NormalResult.createOk(entityResult);
            result.setSuccess(false);
            result.setMessage("exchange is absent");
            result.setData(entityResult);
            return result;
        }

        try {
            //msContextSimple.send(instance.getRemark(), param.getData());
            Map<String, Object> header = new HashMap<>();

            //json转map

            Map<String, Object> jsonMap = JSON.parseObject(param.getData());
            if (jsonMap.containsKey("tag")) {
                header.put("tag", jsonMap.get("tag"));
                jsonMap.remove("tag");
            }
            if (jsonMap.containsKey("action")) {
                header.put("action", jsonMap.get("action"));
                if (!"adp_ms_base_exhibitionCar".equals(instance.getObjectId())) {
                    jsonMap.remove("action");
                }
            }

            String mqBody = JSONObject.toJSONString(jsonMap);

            rabbitMQPool.publishTemplate(channel -> {
                AMQP.BasicProperties rabbitProperties = new AMQP.BasicProperties().builder()
                        .messageId(UUID.randomUUID().toString())
                        .contentEncoding("UTF-8")
                        .headers(header)
                        .deliveryMode(2)
                        .contentType("json").build();
                logger.info("发送对象：{}", JSONObject.toJSONString(rabbitProperties));
                //long start = System.currentTimeMillis();
                //fanout模式
                channel.exchangeDeclare(instance.getRemark(), BuiltinExchangeType.FANOUT, true);
                channel.basicPublish(instance.getRemark(), "", rabbitProperties, mqBody.getBytes("UTF-8"));
            });
            mqs.add(instance.getRemark());
        } catch (Exception e) {
            mqse.add(instance.getRemark());
            logger.error("adp mq send [{}][{}] error", instance.getLabel(), instance.getRemark(), e);
        }

//        instance.getTables().stream().filter(t -> "1".equals(t.getState())).forEach(t -> {
//            try {
//                msContextSimple.send(t.getTableName(), param.getData());
//                mqs.add(t.getTableName());
//            } catch (Exception e) {
//                mqse.add(t.getTableName());
//                logger.error("adp mq send [{}][{}] error", instance.getLabel(), t.getTableName(), e);
//            }
//        });
        EntityResult<List<String>> entityResult = EntityResultBuilder.<List<String>>creatOk().rows(mqs).build();
        NormalResult<EntityResult<List<String>>> result = NormalResult.createOk(entityResult);
        if (!mqse.isEmpty()) {
            entityResult.setResult("0");
            result.setSuccess(false);
        }
        logger.info("消息发送结果：{}", JSONObject.toJSONString(mqse));
        return result;
    }
}
