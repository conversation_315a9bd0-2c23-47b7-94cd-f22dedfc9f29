package com.ly.adp.xapi.api.boot;

import com.ly.adp.xapi.api.zt.ZtNormalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/10/30
 */
@Api(tags = "*自定义接口")
@RestController
@RequestMapping("")
public class ZtReceiveController {


    final ZtNormalService ztNormalService;

    public ZtReceiveController(ZtNormalService ztNormalService) {

        this.ztNormalService = ztNormalService;
    }

    @ApiOperation("ZT查询线索产品专家门店")
    @PostMapping("/noauth/rest/zt_adp/zt_adp_clue_employee")
    public Map<String, Object> ztAdpClueInfoDlr(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        try {
            return ztNormalService.ztAdpClueInfoDlr(map);
        } catch (Exception e) {
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            return result;
        }
    }

    @ApiOperation("ZT查询线索门店产品专家")
    @PostMapping("/noauth/rest/zt_adp/zt_adp_dlr_employee")
    public Map<String, Object> ztAdpDlrEmpCode(@RequestBody Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>();
        try {
            return ztNormalService.ztAdpDlrEmpCode(map);
        } catch (Exception e) {
            result.put("success", false);
            result.put("errMsg", e.getMessage());
            return result;
        }
    }
}
