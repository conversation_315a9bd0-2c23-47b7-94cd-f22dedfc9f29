package com.ly.adp.xapi.api.base.entities;

import javax.validation.constraints.AssertTrue;
import java.util.Arrays;

/**
 * 邮件内容实体
 */
public class MailContentDTO {
    /**
     * 邮件内容
     * 必填
     */
    private String content;

    /**
     * 邮件内容类型
     * 必填
     * 支持类型：
     * - TEXT：文本类型
     * - HTML：html标签格式类型
     */
    private String contentType;

    /**
     * 验证内容类型是否合法
     */
    @AssertTrue(message = "不支持的邮件内容类型")
    public boolean isValidContentType() {
        return Arrays.stream(MailContentTypeEnum.values())
                .map(MailContentTypeEnum::getCode)
                .anyMatch(code -> code.equals(contentType));
    }

    public MailContentDTO() {
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    @Override
    public String toString() {
        return "MailContentDTO{" +
                "content='" + content + '\'' +
                ", contentType='" + contentType + '\'' +
                '}';
    }
}