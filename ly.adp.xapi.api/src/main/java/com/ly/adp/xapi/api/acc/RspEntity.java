package com.ly.adp.xapi.api.acc;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/11/20
 */
public class RspEntity {

    private String code;
    private String message;
    private String data;

    public RspEntity(String data) {
        this.code = "";
        this.message = "";
        this.data = data;
    }

    public RspEntity(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public boolean isSuccess() {
        return StringUtils.isEmpty(this.code);
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }
}
