package com.ly.adp.xapi.api.tda.entity.enums;

/**
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public enum UploadReasonEnum {
    POWERON("poweron", "开机上传"),
    POWEROFF("poweroff", "关机上传"),
    AUTO("auto", "2小时自动上传");

    private final String code;
    private final String message;

    UploadReasonEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "UploadReason{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
