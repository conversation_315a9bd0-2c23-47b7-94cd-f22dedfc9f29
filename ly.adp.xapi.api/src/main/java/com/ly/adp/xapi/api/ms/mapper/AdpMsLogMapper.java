package com.ly.adp.xapi.api.ms.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Map;

public interface AdpMsLogMapper {

    @Select("<script>select * from t_xapi_object_config t where t.system_id = 'ADP_MS' AND T.OBJECT_TYPE = 'normal' and t.service='ms' " +
            "<if test='param.objectName!=null and param.objectName!=\"\"'> and instr(t.object_name,#{param.objectName}) </if>" +
            "<if test='param.apiLabel!=null and param.apiLabel!=\"\"'> and instr(t.api_label,#{param.apiLabel}) </if>" +
            "</script>")
    List<Map<String, Object>> apiLabel(Page<Map<String, Object>> page, @Param("param") Map<String, Object> param);

    @Update("<script>update t_xapi_log_info set log_status = #{param.logStatus} where log_id = #{param.logId}</script>")
    int logUpdate(@Param("param") Map<String,Object> map);

}
