package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * OTA升级信息
 * <AUTHOR>
 * @Date 2024/7/3
 * @Version 1.0.0
 **/
public class OtaInfo implements Serializable {

    private static final long serialVersionUID = 8548761189925528524L;

    /**
     * 类别：esp、dsp
     */
    @JsonProperty("type")
    private String type;

    /**
     * 时间
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 耗时（秒）
     */
    @JsonProperty("cost")
    private int cost;

    /**
     * 升级前版本
     */
    @JsonProperty("old_version")
    private String oldVersion;

    /**
     * 升级后版本
     */
    @JsonProperty("new_version")
    private String newVersion;

    /**
     * 升级结果
     */
    @JsonProperty("result")
    private String result;


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public String getOldVersion() {
        return oldVersion;
    }

    public void setOldVersion(String oldVersion) {
        this.oldVersion = oldVersion;
    }

    public String getNewVersion() {
        return newVersion;
    }

    public void setNewVersion(String newVersion) {
        this.newVersion = newVersion;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }
}
