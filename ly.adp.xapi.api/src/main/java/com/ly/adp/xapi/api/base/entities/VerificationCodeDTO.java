package com.ly.adp.xapi.api.base.entities;

import com.ly.mp.component.helper.StringHelper;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2025/1/9
 * @Version 1.0.0
 **/
public class VerificationCodeDTO {
    @ApiModelProperty(value = "邮件标题")
    private String mailTitle;

    @ApiModelProperty(value = "内容")
    private String content;

    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 邮箱模板，需要找 黄旭东 申请
     */
    @ApiModelProperty(value = "模板编码")
    private String templateCode;

    public VerificationCodeDTO() {
    }

    public VerificationCodeDTO(String mailTitle, String content, String email, String templateCode) {
        this.mailTitle = mailTitle;
        this.content = content;
        this.email = email;
        this.templateCode = templateCode;
    }

    public String getMailTitle() {
        return mailTitle;
    }

    public void setMailTitle(String mailTitle) {
        this.mailTitle = mailTitle;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    @Override
    public String toString() {
        return "VerificationCodeDTO{" +
                "mailTitle='" + mailTitle + '\'' +
                ", content='" + content + '\'' +
                ", email='" + email + '\'' +
                ", templateCode='" + templateCode + '\'' +
                '}';
    }

    public MailSendDTO toMailSendDTO() {
        return MailSendDTO.createTextMail(this.mailTitle, this.content, this.email, "ADP", this.templateCode, StringHelper.GetGUID());
    }
}
