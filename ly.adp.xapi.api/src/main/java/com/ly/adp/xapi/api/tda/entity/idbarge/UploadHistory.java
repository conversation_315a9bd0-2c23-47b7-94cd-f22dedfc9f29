package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 文件上传历史
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class UploadHistory implements Serializable {

    private static final long serialVersionUID = 808276513843601954L;

    /**
     * 文件名
     */
    @JsonProperty("filename")
    private String filename;

    /**
     * 文件大小
     */
    @JsonProperty("size")
    private int size;

    /**
     * 耗时（毫秒）
     */
    @JsonProperty("cost")
    private int cost;

    /**
     * 上传速率（KB/s）
     */
    @JsonProperty("speed")
    private int speed;

    /**
     * 开始录音时间
     */
    @JsonProperty("record_ts")
    private long recordTs;

    /**
     * 开始上传时间
     */
    @JsonProperty("upload_ts")
    private long uploadTs;

    /**
     * 上传原因：- poweron：开机上传；- poweroff：关机上传；- auto：2小时自动上传
     */
    @JsonProperty("upload_reason")
    private String uploadReason;

    /**
     * 上传结果
     */
    @JsonProperty("result")
    private String result;

    /**
     * 开始录音模式
     */
    @JsonProperty("start_mode")
    private String startMode;

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    public long getRecordTs() {
        return recordTs;
    }

    public void setRecordTs(long recordTs) {
        this.recordTs = recordTs;
    }

    public long getUploadTs() {
        return uploadTs;
    }

    public void setUploadTs(long uploadTs) {
        this.uploadTs = uploadTs;
    }

    public String getUploadReason() {
        return uploadReason;
    }

    public void setUploadReason(String uploadReason) {
        this.uploadReason = uploadReason;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getStartMode() {
        return startMode;
    }

    public void setStartMode(String startMode) {
        this.startMode = startMode;
    }
}
