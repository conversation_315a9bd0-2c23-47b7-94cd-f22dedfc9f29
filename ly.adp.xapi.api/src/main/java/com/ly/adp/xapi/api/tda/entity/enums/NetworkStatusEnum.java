package com.ly.adp.xapi.api.tda.entity.enums;

/**
 * 联网状态
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public enum NetworkStatusEnum {
    CONFIG_OK("CONFIG_OK", "配网成功"),
    CONFIG_FAIL("CONFIG_FAIL", "配网失败"),
    CONNECT_OK("CONNECT_OK", "联网成功"),
    CONNECT_FAIL("CONNECT_FAIL", "联网失败");

    private String code;
    private String message;

    NetworkStatusEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    @Override
    public String toString() {
        return "NetworkStatus{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
