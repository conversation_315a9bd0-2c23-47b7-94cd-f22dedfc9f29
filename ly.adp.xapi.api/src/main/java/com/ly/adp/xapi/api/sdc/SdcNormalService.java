package com.ly.adp.xapi.api.sdc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.adp.xapi.api.base.CdpNormalService;
import com.ly.adp.xapi.api.sdc.mapper.SdcResMapper;
import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.log.db.XapiLogDb;
import com.ly.bucn.component.xapi.service.normal.*;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.HttpUtil;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.helper.StringHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@NormalService("sdc_adp")
public class SdcNormalService extends NormalDataGenericBase {
    private static final Logger log = LoggerFactory.getLogger(CdpNormalService.class);
    @Autowired
    SdcResMapper sdcResMapper;
    @Autowired
    XapiLogDb xapiLogDb;

    @Override
    public void addHadlerGeneric(Map<String, NormalFunctionGeneric> handlers) {
        handlers.put("sdc_adp_order_dissociate", this::sdcOrderDissociate);//订单解配申请sdc传输adp
    }

    @Transactional(rollbackFor = Exception.class)
    public NormalResult<Map<String, Object>> sdcOrderDissociate(XapiInstance instance, XapiMap<String, Object> context, NormalParam<String> param) {
        NormalResult<Map<String, Object>> result = new NormalResult<>();
        Map<String, Object> data = new HashMap<>();
        String info = param.getData();
        Map<String, Object> paramMap = JSONObject.parseObject(info, Map.class);
        log.info("sdc解配申请入参：" + info);
        try {
            if (StringHelper.IsEmptyOrNull(paramMap)) {
                responseMassage("0", result, data, "参数不能为空");
                return result;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("dissociateCode"))) {
                responseMassage("0", result, data, "dissociateCode不能为空");
                return result;

            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("saleOrderCode"))) {
                responseMassage("0", result, data, "saleOrderCode不能为空");
                return result;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("pno18"))) {
                responseMassage("0", result, data, "pno18不能为空");
                return result;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("isAdpAudit"))) {
                responseMassage("0", result, data, "isAdpAudit不能为空");
                return result;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("initiateSys"))) {
                responseMassage("0", result, data, "initiateSys不能为空");
                return result;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("applyType"))) {
                responseMassage("0", result, data, "applyType不能为空");
                return result;
            }
            if ("3".equals(paramMap.get("applyType").toString()) || "4".equals(paramMap.get("applyType").toString()) || "5".equals(paramMap.get("applyType").toString())) {
                if (StringHelper.IsEmptyOrNull(paramMap.get("vin"))) {
                    responseMassage("0", result, data, "vin不能为空");
                    return result;
                }
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("applyTime"))) {
                responseMassage("0", result, data, "applyTime不能为空");
                return result;
            }
            if (StringHelper.IsEmptyOrNull(paramMap.get("applyReason"))) {
                responseMassage("0", result, data, "applyReason不能为空");
                return result;
            }
            if (!"6".equals(paramMap.get("applyType")) &&
                    !"7".equals(paramMap.get("applyType"))) {

                if (StringHelper.IsEmptyOrNull(paramMap.get("fileList"))) {
                    responseMassage("0", result, data, "fileList不能为空");
                    return result;
                }

            }

            //查询此订单是否存在审批中的申请单据
            Map<String, Object> map = new HashMap<>();
            map.put("saleOrderCode", paramMap.get("saleOrderCode"));
            map.put("applyType", paramMap.get("applyType"));
            int i = sdcResMapper.queryDissociateApply(map);
            if (i > 0) {
                responseMassage("0", result, data, "此订单存在审批中的申请数据，无法再次提交");
                return result;
            }

            //根据isAdpAudit判断是否跳过adp审批(1是，0否)
            if ("1".equals(String.valueOf(paramMap.get("isAdpAudit")))) {
                paramMap.put("applyStatus", "5");
            } else {
                paramMap.put("applyStatus", "0");
            }

            //根据isAdpAudit判断是否跳过adp审批(1是，0否),跳过审批直接推送数据到otd
//            if ("1".equals(String.valueOf(paramMap.get("isAdpAudit")))) {
//
//                Map<String, Object> lookupMap = new HashMap<>();
//                lookupMap.put("lookupTypeCode", "VE1096");
//                List<Map<String, Object>> mapList = sdcResMapper.queryLookUpValue(lookupMap);
//                for (Map<String, Object> urlMap : mapList) {
//                    if ("1".equals(String.valueOf(urlMap.get("lookupValueCode")))) {
//                        lookupMap.put("url", urlMap.get("lookupValueName"));
//                    }
//                }
//
//                //调用otd解配查询接口
//                String url = String.valueOf(lookupMap.get("url"));
//                Map<String, Object> selectMap = new HashMap<>();
//                selectMap.put("RETAILNO", paramMap.get("saleOrderCode"));
//                selectMap.put("VIN", paramMap.get("vin"));
//                selectMap.put("SEND_TYPE", "SDC");
//                selectMap.put("SEND_TIME", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//                selectMap.put("INTERFACE_TYPE", "select");
//                EntityResult<Map<String, Object>> resultOtd = sendOtd(selectMap, url);
//                if ("0".equals(resultOtd.getResult())) {
//                    responseMassage("0", result, data, "解配申请调用otd校验无法申请：" + resultOtd.getMsg());
//                    return result;
//                }
//
//                //调用otd解配申请推送接口
//                Map<String, Object> updateMap = new HashMap<>();
//                updateMap.put("RETAILNO", paramMap.get("saleOrderCode"));
//                updateMap.put("VIN", paramMap.get("vin"));
//                updateMap.put("SEND_TYPE", "SDC");
//                updateMap.put("SEND_TIME", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
//                updateMap.put("INTERFACE_TYPE", "update");
//                EntityResult<Map<String, Object>> resultUpdate = sendOtd(updateMap, url);
//                if ("1".equals(resultUpdate.getResult())) {
//                    paramMap.put("column1", "1");//解配成功
//                } else {
//                    paramMap.put("column1", "0");//解配失败
//                }
//            }


            //新增主数据
            paramMap.put("dissociateId", StringHelper.GetGUID());
            //paramMap.put("column1","3");
            sdcResMapper.orderDissociateApply(paramMap);

            //存日志表
            paramMap.put("logsId", StringHelper.GetGUID());
            sdcResMapper.insertOrderDissociate(paramMap);

            List<Map<String, Object>> fileList = (List<Map<String, Object>>) paramMap.get("fileList");
            if (!CollectionUtils.isEmpty(fileList)) {
                for (Map<String, Object> fileMap : fileList) {
                    fileMap.put("logsId", paramMap.get("logsId"));
                    if (StringHelper.IsEmptyOrNull(fileMap.get("fileUrl"))) {
                        responseMassage("0", result, data, "fileUrl不能为空");
                        return result;
                    }
                    if (StringHelper.IsEmptyOrNull(fileMap.get("fileName"))) {
                        responseMassage("0", result, data, "fileName不能为空");
                        return result;
                    }

                    //新增明细表数据
                    fileMap.put("dissociateId", paramMap.get("dissociateId"));
                    sdcResMapper.dissociatefileSave(fileMap);

                    //新增附件数据
                    sdcResMapper.insertOrderDissociateFiles(fileMap);
                }
            }


        } catch (Exception e) {
            e.printStackTrace();
            responseMassage("0", result, data, "失败" + e.getMessage());
            return result;
        }
        responseMassage("1", result, data, "成功");
        return result;
    }

    private void responseMassage(String code, NormalResult<Map<String, Object>> result, Map<String, Object> map, String errorMsg) {
        map.put("code", code);
        map.put("msg", errorMsg);
        result.setData(map);
        if ("0".equals(code)) {
            result.setSuccess(false);
        } else {
            result.setSuccess(true);
        }
        result.setMessage(errorMsg);
    }

    public EntityResult<Map<String, Object>> sendOtd(Map<String, Object> map, String url) {
        EntityResult<Map<String, Object>> result = new EntityResult<>();
        XapiLog xapiLog = new XapiLog();
        xapiLog.setLogTime(new Date());
        xapiLog.setLabel("otd_sendDate");
        try {
            //解配申请调用otd查询接口
            xapiLog.setOutJson("url->" + url + ",map->" + JSON.toJSONString(map));
            HashMap<String, String> headers = new HashMap<>();
            headers.put("content-type", "application/json");
            String resultJson = HttpUtil.post(url, map, headers, 30000, 30000, "UTF-8");
            xapiLog.setInJson(resultJson);
            Map<String, Object> resp = JSONObject.parseObject(resultJson, Map.class);
            if (resp.containsKey("success") && "true".equals(String.valueOf(resp.get("success")))) {
                result.setResult("1");
                result.setRows(resp);
            } else {
                throw BusicenException.create("调用otd接口失败:" + resultJson);
            }
        } catch (Exception e) {
            log.error("解配申请发送otd失败：", e);
            StringWriter sw = new StringWriter();
            e.printStackTrace(new PrintWriter(sw));
            xapiLog.setLogStatus("1");
            xapiLog.setLogMessage(sw.toString());
            result.setResult("0");
            result.setRows(new HashMap<>());
            result.setMsg("发送otd失败" + String.valueOf(e.getMessage()));
        } finally {
            xapiLogDb.add(xapiLog);
        }
        return result;
    }

}

