package com.ly.adp.xapi.api.base;

import com.ly.bucn.component.xapi.entity.XapiInstance;
import com.ly.bucn.component.xapi.kernal.model.XapiData;
import com.ly.bucn.component.xapi.kernal.model.XapiMap;
import com.ly.bucn.component.xapi.kernal.model.XapiResult;
import com.ly.bucn.component.xapi.service.send.SendDataBase;
import com.ly.bucn.component.xapi.service.send.SendService;
import com.ly.bucn.component.xapi.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.naming.ldap.LdapName;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

@SendService("ADP_AD")
public class AdpADSendService extends SendDataBase<String> {

    Logger logger = LoggerFactory.getLogger(AdpADSendService.class);

    @Autowired
    LdapService ldapService;

    @Override
    public Object convertData(XapiInstance instance, XapiMap<String, Object> context, XapiData data) {
        Map<String, Object> dmap = data.getDataEx().get(0);
        Map<String, String> map = new HashMap<>();
        dmap.forEach((k, v) -> {
            if (v != null) {
                map.put(k, String.valueOf(v));
            }
        });
        return map;
    }

    @Override
    public Object sendData(XapiInstance instance, XapiMap<String, Object> stringObjectXapiMap, Object o) throws Exception {
        try {
            Map<String, String> map = (Map<String, String>) o;
            if (map.get("tag") == null) {
                throw new RuntimeException("用户操作类型tag为空");
            }
            if (map.get("cn") == null) {
                throw new RuntimeException("用户主键cn为空");
            }
            if (map.get("ous") == null) {
                throw new RuntimeException("用户ous为空");
            }
            String[] ous = instance.getSystem().getXapiKv(map.get("ous")).getValueTee().split(",");
            map.remove("ous");
            if ("d".equals(map.get("tag"))) {
                ldapService.deleteUser(ous, map.get("cn"));
            } else {
                map.remove("tag");
                AtomicReference<LdapName> atomicReference = new AtomicReference<>();
                boolean flag = ldapService.userExit(ous, map.get("cn"), atomicReference);
                if (flag) {
                    ldapService.update(atomicReference.get(), map);
                } else {
                    ldapService.addUser(atomicReference.get(), map);
                }
            }
        } catch (Exception e) {
            logger.error("adp ad send error", e);
            return e.getMessage();
        }
        return null;
    }

    @Override
    public XapiResult<String> manageResp(XapiInstance instance, XapiMap<String, Object> stringObjectXapiMap, Object o) {
        XapiResult<String> result = XapiResult.creat();
        if (o != null) {
            result.setStatus(1);
            result.setMessage(StringUtil.subStr(String.valueOf(o), 200));
        }
        return result;
    }
}
