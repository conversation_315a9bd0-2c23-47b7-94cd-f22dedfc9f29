package com.ly.adp.xapi.api.tda.entity.idbarge;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 开机事件记录
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public class PowerOn implements Serializable {

    private static final long serialVersionUID = 2147577611248287449L;

    /**
     * 时间戳
     */
    @JsonProperty("ts")
    private long ts;

    /**
     * 电量
     */
    @JsonProperty("power")
    private int power;

    /**
     * 开机原因
     */
    @JsonProperty("reason")
    private String reason;


    public long getTs() {
        return ts;
    }

    public void setTs(long ts) {
        this.ts = ts;
    }

    public int getPower() {
        return power;
    }

    public void setPower(int power) {
        this.power = power;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}
