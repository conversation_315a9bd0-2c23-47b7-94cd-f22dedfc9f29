package com.ly.adp.xapi.api.clue.feign;

import java.io.Serializable;

/**
 * <AUTHOR> created on 2020/2/28 15:07.
 * 为interfaces(用户界面层) 提供组件配置
 */
public class RespBody<T> implements Serializable {
    /**
     * 自定义业务码
     */
    private String code;
    /**
     * 自定义业务提示说明
     */
    private String message;
    /**
     * 自定义返回 数据结果集
     */
    private T body;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }

    public RespBody() {

    }

    public RespBody(String code) {
        this.code = code;
    }

    public RespBody(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public RespBody(String code, String message, T body) {
        this.code = code;
        this.message = message;
        this.body = body;
    }

    /**
     * 以上所有构建均调用此底层方法
     *
     * @param stateCode 状态值
     * @param message   返回消息
     * @param body      返回数据体
     */
    public static <T> RespBody<T> build(String stateCode, String message, T body) {
        return new RespBody<>(stateCode, message, body);
    }

}
