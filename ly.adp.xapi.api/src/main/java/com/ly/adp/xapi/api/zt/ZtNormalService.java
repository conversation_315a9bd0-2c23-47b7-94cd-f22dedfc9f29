package com.ly.adp.xapi.api.zt;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.ly.adp.xapi.api.base.CdpNormalService;

import com.ly.adp.xapi.api.zt.mapper.IZtNormalMapper;
import com.ly.bucn.component.xapi.entity.XapiLog;
import com.ly.bucn.component.xapi.log.IXapiLogBiz;

import com.ly.mp.component.helper.StringHelper;
import org.apache.commons.lang.math.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ZtNormalService {

    private static final Logger log = LoggerFactory.getLogger(CdpNormalService.class);


    final IXapiLogBiz xapiLogBiz;

    final IZtNormalMapper ztNormalMapper;

    public ZtNormalService(IXapiLogBiz xapiLogBiz,
                           IZtNormalMapper ztNormalMapper) {

        this.xapiLogBiz = xapiLogBiz;
        this.ztNormalMapper = ztNormalMapper;

    }

    public Map<String, Object> ztAdpClueInfoDlr(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>(3);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("zt_adp_clue_employee");
        if (StringHelper.IsEmptyOrNull(map)) {
            result.put("code", "0");
            result.put("msg", "请求参数不能为空");
            extracted(map, result, loginfo);
            return result;
        }
        if (StringHelper.IsEmptyOrNull(map.get("phone"))) {
            result.put("code", "0");
            result.put("msg", "手机号不能为空");
            extracted(map, result, loginfo);
            return result;
        }
        Map<String, Object> clueInfoDlr = ztNormalMapper.findClueInfoDlr(map);
        if (StringHelper.IsEmptyOrNull(clueInfoDlr)) {
            HashMap<String, Object> hashMap = Maps.newHashMap();
            hashMap.put("isEmployee", "0");
            result.put("data", hashMap);
            result.put("code", "1");
            result.put("msg", "success");
            extracted(map, result, loginfo);
            return result;
        }
        if (StringHelper.IsEmptyOrNull(clueInfoDlr.get("empName")) ||
                !"1".equals(clueInfoDlr.get("userStatus"))) {
            clueInfoDlr.put("dlrCode", clueInfoDlr.get("infoDlrCode"));
            Object reviewId = clueInfoDlr.get("reviewId");
            Integer dlrEmpSum = ztNormalMapper.findDlrEmpSum(clueInfoDlr);
            if (dlrEmpSum.compareTo(0) <= 0) {
                result.put("code", "1");
                result.put("msg", "success");
                extracted(map, result, loginfo);
                return result;
            }
            int i = RandomUtils.nextInt(dlrEmpSum);
            List<Map<String, Object>> dlrEmpList = ztNormalMapper.findDlrEmp(clueInfoDlr);
            clueInfoDlr = dlrEmpList.get(i);

            int res = ztNormalMapper.updateInfoDlr(clueInfoDlr, reviewId);
            if (res == 0) {
                ztNormalMapper.updateInfoDlrCsc(clueInfoDlr, reviewId);
            }

            ztNormalMapper.updateReview(clueInfoDlr, reviewId);
        }
        clueInfoDlr.put("isEmployee", "1");
        return getHeadPortrait(map, result, loginfo, clueInfoDlr);
    }


    private void extracted(Map<String, Object> map, Map<String, Object> result, XapiLog loginfo) {
        loginfo.setLogStatus("0");
        loginfo.setInJson(JSONObject.toJSONString(map));
        loginfo.setOutJson(JSONObject.toJSONString(result));
        xapiLogBiz.add(loginfo);
    }

    public Map<String, Object> ztAdpDlrEmpCode(Map<String, Object> map) {

        Map<String, Object> result = new HashMap<>(3);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("zt_adp_dlr_employee");
        if (StringHelper.IsEmptyOrNull(map)) {
            result.put("code", "0");
            result.put("msg", "请求参数不能为空");
            extracted(map, result, loginfo);
            return result;
        }
        if (StringHelper.IsEmptyOrNull(map.get("dlrCode"))) {
            result.put("code", "0");
            result.put("msg", "门店编码不能为空");
            extracted(map, result, loginfo);
            return result;
        }
        Integer count = ztNormalMapper.findDlrEmpSum(map);
        if (count.compareTo(0) <= 0) {
            result.put("code", "0");
            result.put("msg", "该门店暂无产品专家");
            extracted(map, result, loginfo);
            return result;
        }
        int i = RandomUtils.nextInt(count);
        List<Map<String, Object>> dlrEmpList = ztNormalMapper.findDlrEmp(map);
        Map<String, Object> dlrEmpMap = dlrEmpList.get(i);

        return getHeadPortrait(map, result, loginfo, dlrEmpMap);

    }

    private Map<String, Object> getHeadPortrait(Map<String, Object> map, Map<String, Object> result, XapiLog loginfo, Map<String, Object> dlrEmpMap) {
        if (!StringHelper.IsEmptyOrNull(dlrEmpMap.get("headPortrait"))) {
            String headPortrait = dlrEmpMap.get("headPortrait").toString();
            String url;
            if (headPortrait.contains("downloadOSS")) {
                url = ztNormalMapper.finddownloadOSS();
            } else {
                url = ztNormalMapper.finddownload();
            }
            dlrEmpMap.put("headPortrait", url + headPortrait);

        }
        result.put("data", dlrEmpMap);
        result.put("code", "1");
        result.put("msg", "success");
        extracted(map, result, loginfo);
        return result;
    }

    public Map<String, Object> ecAdpPolicyDiscount(Map<String, Object> map) {
        Map<String, Object> result = new HashMap<>(3);
        XapiLog loginfo = new XapiLog();
        loginfo.setLabel("ec_adp_policyDiscount");
        return result;
    }
}
