#FROM swr.cn-east-3.myhuaweicloud.com/adp/alpine-oraclejdk8:v2
FROM swr.cn-east-3.myhuaweicloud.com/smart/skywalking-java-agent:8.5.0-jdk8

ENV app_name=ly.adp.xapi.api
ENV configDir /home/<USER>/config

# ADD cert/ad-cacerts /home/<USER>/ad-cacerts
# ADD cert/ldp_ssl.pfx /home/<USER>/ad-cacerts
ADD cert/ldp_dc03.pfx /home/<USER>/ad-cacerts
ADD $app_name/build/libs/*.jar /home/<USER>/app.jar

ENV env dev
ENV TZ=Asia/Shanghai

# ENV JAVA_OPTS="-server -Xmx1g -Xms1g -Xmn512m"

#skywalking
# ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES="skywalking-oap.skywalking.svc:11800"
# ENV SW_OPTS="-javaagent:/skywalking/agent/skywalking-agent.jar"


USER root
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add --update ttf-dejavu fontconfig curl && \
		rm -rf /var/cache/apk/*

#USER mpjava
WORKDIR /home/<USER>
#ENTRYPOINT
# ENTRYPOINT  java $JAVA_OPTS  -Djavax.net.ssl.trustStore=/home/<USER>/ad-cacerts -Djavax.net.ssl.trustStorePassword=636smart -Djavax.net.ssl.trustStoreType=PKCS12 -Dcom.sun.jndi.ldap.object.disableEndpointIdentification=true -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
ENTRYPOINT  java $JAVA_OPTS  -Djavax.net.ssl.trustStore=/home/<USER>/ad-cacerts -Djavax.net.ssl.trustStorePassword=636smart -Djavax.net.ssl.trustStoreType=PKCS12 -Dcom.sun.jndi.ldap.object.disableEndpointIdentification=true -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
# ENTRYPOINT  java $JAVA_OPTS  -Djavax.net.ssl.trustStore=/home/<USER>/ad-cacerts -Dcom.sun.jndi.ldap.object.disableEndpointIdentification=true -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd  java $JAVA_OPTS $SW_OPTS -Djavax.net.ssl.trustStore=/home/<USER>/ad-cacerts -Dcom.sun.jndi.ldap.object.disableEndpointIdentification=true -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd echo 'java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar /home/<USER>/app.jar' >start.sh && chmod 750 start.sh 
