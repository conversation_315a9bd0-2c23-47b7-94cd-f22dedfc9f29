#FROM swr.cn-east-3.myhuaweicloud.com/adp/alpine-oraclejdk8:v2
FROM swr.cn-east-3.myhuaweicloud.com/smart/skywalking-java-agent:8.5.0-jdk8

ENV app_name=ly.adp.xapi.api
ENV configDir /home/<USER>/config

ADD cert/ad-cacerts /home/<USER>/ad-cacerts
ADD $app_name/build/libs/*.jar /home/<USER>/app.jar

ENV env dev
ENV TZ=Asia/Shanghai

ENV JAVA_OPTS="-server -Xmx1g -Xms1g -Xmn512m"

#skywalking
ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES="skywalking-oap.skywalking.svc:11800"
ENV SW_OPTS="-javaagent:/skywalking/agent/skywalking-agent.jar"


USER root
Run mkdir /opt/xmiast && wget -P /opt/xmiast https://obs-public-download.obs.cn-east-3.myhuaweicloud.com/sec_agent/agent.jar
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add --update ttf-dejavu fontconfig && \
		rm -rf /var/cache/apk/*

#USER mpjava
WORKDIR /home/<USER>

#ENTRYPOINT
cmd  java -javaagent:/opt/xmiast/agent.jar -Dxmiast.ip=************ -Dxmiast.port=9090 -Dxmiast.projectname=ADP -Dxmiast.nodename=ADP_xapi -Dxmiast.region= -Dxmiast.token=puzWfJyx9qVZxk19Xb5b5LQ -Dxmiast.app_version= -XX:+UnlockExperimentalVMOptions -Dxmiast.Writeconfig=false -XX:+UseCGroupMemoryLimitForHeap $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd  java -javaagent:/opt/xmiast/agent.jar -Dxmiast.ip=************ -Dxmiast.port=9090 -Dxmiast.projectname=ADP -Dxmiast.nodename=ADP_xapi -Dxmiast.region= -Dxmiast.token=puzWfJyx9qVZxk19Xb5b5LQ -Dxmiast.app_version= -XX:+UnlockExperimentalVMOptions -Dxmiast.Writeconfig=false -XX:+UseCGroupMemoryLimitForHeap $JAVA_OPTS $SW_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar app.jar
#cmd echo 'java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar /home/<USER>/app.jar' >start.sh && chmod 750 start.sh && ./start.sh#cmd echo 'java $JAVA_OPTS -Duser.timezone=GMT+8  -Dspring.config.additional-location=$configDir/application-addition.properties -Dspring.application.name=$app_name -Djava.security.egd=file:/dev/./urandom -jar /home/<USER>/app.jar' >start.sh && chmod 750 start.sh && ./start.sh