<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ly.adp.xapi.api.interfacecenter.mapper.SapSaleCommissionTotalMapper">

    <insert id="batchInsertCommissionTotal">
        INSERT INTO interfacecenter.t_ifs_base_sap_sale_commission_total (LOGS_ID, AGENT_CODE, AGENT_NAME, BIG_AREA,
        COMPANY_CODE, COMPANY_NANE, DLR_CODE, DLR_NAME, DLR_TYPE, SETTLEMENT_MONTH, COMMISSION_CODE, COMMISSION_AMOUNT,
        COMMISSION_TYPE, INSERT_DATE)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.logsId}, #{item.agentCode}, #{item.agentName}, #{item.bigArea}, #{item.companyCode}, #{item.companyName},
            #{item.dlrCode}, #{item.dlrName}, #{item.dlrType}, #{item.settlementMonth}, #{item.commissionCode}, #{item.commissionAmount},
            #{item.commissionType}, #{item.insertDate}
            )
        </foreach>
    </insert>
</mapper>