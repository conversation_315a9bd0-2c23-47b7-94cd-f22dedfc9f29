<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.base.mapper.IEcNormalMapper">
    <select id="ecStockVehicleQuery" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleBo">
        select a.VIN,
               a.SALES_STATUS,
               a.PNO18,
               a.VEHICLE_TYPE,
               a.IS_OVERLAY,

               a.DISCOUNT_AMOUNT,

               DATEDIFF(now(), a.STOCK_DATE) inventoryDays
        from orc.t_orc_stock_vehicle a

        where
          a.vin = #{vin}
    </select>
    <select id="otdStockVehicleQuery" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleResponse">
        select
        a.vin,
        a.SALES_STATUS
        from orc.t_orc_stock_vehicle a
        <trim prefix="WHERE" prefixOverrides="AND |OR ">
            <if test="vins !=null and vins.size()>0">
                a.VIN in
                <foreach collection="vins" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="findPolicyDetail" resultType="java.math.BigDecimal">
        select DISCOUNT_AMOUNT
        from orc.t_orc_stock_vehicle_policy_detail
        where MIN_STOCK &lt;= #{inventoryDays}
          and MAX_STOCK >= #{inventoryDays}
          and POLICY_ID = #{policyId}
          and IS_ENABLE = '1'
    </select>

    <select id="findStockVehicle" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleBo">
        select VIN,
               SALES_STATUS
        from orc.t_orc_stock_vehicle a
        where vin = #{vin}
    </select>

    <update id="updateStockVehicle">
        update
        orc.t_orc_stock_vehicle
        set SALES_STATUS=#{saleNode},
        MODIFIER='ec_stock_vehicle_update',
        LAST_UPDATED_DATE=now(),
        <if test="saleOrder!=null and saleOrder !=''">
            SALE_ORDER_CODE=#{saleOrder},
        </if>
        UPDATE_CONTROL_ID=uuid()
        where vin=#{vin}
    </update>

    <select id="ecStockVehicleCodeQuery" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleCodeBo">
        SELECT e.VIN,
               e.SALES_STATUS,
               e.PNO18,
               e.VEHICLE_TYPE,
               e.IS_OVERLAY,
               e.DISCOUNT_AMOUNT,
               DATEDIFF(now(), e.STOCK_DATE) inventoryDays,
               ifnull(a.IS_FLEET, '0')   isFleet,
               d.WHOLESALE_COMPANY           companyName,
               d.WHOLESALE_COMPANY_CODE      companyCode,
               d.GUNO                        taxNum,
               d.ADDR                        addr,
               d.PHONE                       phone,
               d.bank,
               d.ACCOUNT_NUMBER              bankNum,
               d.EMAIL                       mail
        FROM
            orc.t_orc_stock_vehicle_qcode a
           left join   orc.t_orc_stock_vehicle e on a.vin=e.vin

                 LEFT JOIN orc.t_orc_wholesalecompany d ON d.WHOLESALE_COMPANY_ID = a.WHOLESALE_COMPANY_ID
        WHERE
            a.QCODE_ID = #{code}
    </select>

    <select id="findPolicyDetailCode" resultType="java.math.BigDecimal">
        select DISCOUNT_AMOUNT
        from orc.t_orc_stock_vehicle_policy_detail
        where MIN_STOCK &lt;= #{inventoryDays}
          and MAX_STOCK >= #{inventoryDays}
          and POLICY_ID = #{policyId}
          and IS_ENABLE = '1'
    </select>

    <select id="findPno18Amount" resultType="java.math.BigDecimal">
        select DISCOUNT_AMOUNT
        from orc.t_orc_stock_vehicle_policy_config a
                 left join orc.t_orc_stock_vehicle_policy_detail b on a.POLICY_ID = b.POLICY_ID
                 left join orc.t_orc_stock_vehicle_policy c on a.POLICY_ID = c.POLICY_ID

        where c.POLICY_TYPE = '3'
          and c.AUDIT_STATUS = '2'
          and b.MIN_STOCK &lt;= #{inventoryDays}
          and b.MAX_STOCK >= #{inventoryDays}
            and a.PNO18=#{pno18}
          and c.IS_ENABLE ='1'
    </select>

    <select id="findPno18" resultType="java.math.BigDecimal">
        select DISCOUNT_AMOUNT
        from orc.t_orc_stock_vehicle_policy_config a
                 left join orc.t_orc_stock_vehicle_policy_detail b on a.POLICY_ID = b.POLICY_ID
                 left join orc.t_orc_stock_vehicle_policy c on a.POLICY_ID = c.POLICY_ID

        where c.POLICY_TYPE = '3'
          and c.AUDIT_STATUS = '2'
          and b.MIN_STOCK &lt;= #{inventoryDays}
          and b.MAX_STOCK >= #{inventoryDays}
          and a.PNO18=#{pno18}
          and c.IS_ENABLE ='1'
    </select>

    <select id="findPno18Policy" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleBo">
        select a.VIN,
               a.SALES_STATUS,
               a.PNO18,
               a.VEHICLE_TYPE,
               c.IS_OVERLAY,
               c.POLICY_TYPE,
               bu.DISCOUNT_AMOUNT,
               b.POLICY_ID,
               DATEDIFF(now(), a.STOCK_DATE) inventoryDays
        from orc.t_orc_stock_vehicle a
                 LEFT JOIN orc.t_orc_stock_vehicle_policy_config B ON A.PNO18 = B.PNO18
                 left JOIN orc.t_orc_stock_vehicle_policy c ON b.POLICY_ID = c.POLICY_ID
                 LEFT JOIN orc.t_orc_stock_vehicle_policy_detail bu ON c.POLICY_ID = bu.POLICY_ID

        where c.AUDIT_STATUS = '2'
          and b.IS_ENABLE = '1'
          and c.IS_ENABLE = '1'
          and a.VEHICLE_TYPE = '0'
          and c.POLICY_END_DATE >= CURDATE()
          and c.POLICY_START_DATE &lt;= now()
          and bu.MIN_STOCK &lt;=DATEDIFF(now(),a.STOCK_DATE)
          and bu.MAX_STOCK >=DATEDIFF(now(),a.STOCK_DATE)
          and a.vin = #{vin}
    </select>

    <select id="ecStockVehicleCodePno18" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleCodeBo">
        SELECT e.VIN,
               e.SALES_STATUS,
               e.PNO18,
               e.VEHICLE_TYPE,
               c.IS_OVERLAY,
               c.POLICY_TYPE,
               bu.DISCOUNT_AMOUNT,
               b.POLICY_ID,
               DATEDIFF(now(), e.STOCK_DATE) inventoryDays,
               ifnull(a.IS_FLEET, '0')   isFleet,
               d.WHOLESALE_COMPANY           companyName,
               d.WHOLESALE_COMPANY_CODE      companyCode,
               d.GUNO                        taxNum,
               d.ADDR                        addr,
               d.PHONE                       phone,
               d.bank,
               d.ACCOUNT_NUMBER              bankNum,
               d.EMAIL                       mail
        FROM
            orc.t_orc_stock_vehicle_qcode a
                left join   orc.t_orc_stock_vehicle e on a.vin=e.vin
                LEFT JOIN orc.t_orc_stock_vehicle_policy_config B ON e.PNO18 = B.PNO18
                LEFT JOIN orc.t_orc_stock_vehicle_policy c ON b.POLICY_ID = c.POLICY_ID
                LEFT JOIN orc.t_orc_stock_vehicle_policy_detail bu ON c.POLICY_ID = bu.POLICY_ID
                LEFT JOIN orc.t_orc_wholesalecompany d ON d.WHOLESALE_COMPANY_ID = a.WHOLESALE_COMPANY_ID
        WHERE c.AUDIT_STATUS = '2'
          AND b.IS_ENABLE = '1'
          AND c.IS_ENABLE = '1'
          AND c.POLICY_END_DATE >= CURDATE()
          AND c.POLICY_START_DATE &lt;= now()
          AND d.EFFECTIVE_END >= now()
          AND d.EFFECTIVE_START &lt;= now()
          and bu.MIN_STOCK &lt;=DATEDIFF(now(),e.STOCK_DATE)
          and bu.MAX_STOCK >=DATEDIFF(now(),e.STOCK_DATE)
          and a.QCODE_ID = #{code}
    </select>
    <select id="queryecStockVehicleCode" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleCodeBo">
        SELECT
            e.VIN,
            e.SALES_STATUS,
            e.PNO18,
            e.VEHICLE_TYPE,
            0 isOverlay,
            IFNULL(e.DISCOUNT_AMOUNT,0.00) discountAmount,
            IFNULL(e.RETAIL_PRICE,0.00) retailPrice,
            DATEDIFF( now(), e.STOCK_DATE ) inventoryDays,
            1 isFleet,
            a.COMPANY_NAME companyName,
            a.COMPANY_CODE companyCode,
            a.TAX_NUM taxNum,
            a.ADDR addr,
            a.PHONE phone,
            a.BANK bank,
            a.BANK_NUM bankNum,
            a.EMAIL mail
        FROM
            orc.t_orc_stock_vehicle_qcode a
                LEFT JOIN orc.t_orc_stock_vehicle e ON a.vin = e.vin
        WHERE
            a.QCODE_ID = #{code}
    </select>
    <select id="ecStockVehicleQueryByVin" resultType="com.ly.adp.xapi.api.baseEntity.EcStockVehicleBo">
        select a.VIN,
               a.SALES_STATUS,
               a.PNO18,
               a.VEHICLE_TYPE,
               a.IS_OVERLAY,
               IFNULL(a.DISCOUNT_AMOUNT,0.00) discountAmount,
               IFNULL(a.RETAIL_PRICE,0.00) retailPrice,
               DATEDIFF(now(), a.STOCK_DATE) inventoryDays,
               a.BATCH_SALE_SHELF_STATUS batchSaleShelfStatus
        from orc.t_orc_stock_vehicle a
        where
            a.vin = #{vin}
    </select>
</mapper>