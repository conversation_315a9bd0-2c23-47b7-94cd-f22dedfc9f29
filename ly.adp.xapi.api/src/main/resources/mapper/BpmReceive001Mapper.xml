<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.bpm.mapper.IBpmReceive001Mapper">
    <insert id="inserBpmReceive001His">
    </insert>

    <select id="findSubsidy" resultType="java.util.Map">
        SELECT a.DLR_BUILD_SUBSIDY_ID,
               a.AUDIT_STATUS                                   auditStatus,
               a.DLR_BUILD_SUBSIDY_ID                           dlrBuildSubsidyId,
               c.PAY_STATUS                                     payStatus,
               c.BATCH_TOTAL                                    batchTotal,
               c.DLR_CODE                                       dlrCode,
               a.ADJUST_AMOUNT                                  adjustAmount,
               CONCAT(d.DLR_CODE, '第', a.ORDER_NO, '笔', '建店支持') remark
        FROM mp.t_usc_bu_dlr_build_subsidy_d a
                 LEFT JOIN mp.t_usc_bu_dlr_build_subsidy c ON c.DLR_BUILD_SUBSIDY_ID = a.DLR_BUILD_SUBSIDY_ID
                 LEFT JOIN mp.t_usc_mdm_org_employee e ON a.CREATOR = e.USER_ID
                 LEFT JOIN mp.t_usc_mdm_org_dlr d ON c.DLR_CODE = d.DLR_CODE
                 LEFT JOIN mp.t_usc_mdm_agent_company co ON d.COMPANY_ID = co.AGENT_COMPANY_ID
        where a.DLR_BUILD_SUBSIDY_D_ID = #{requestCode}
    </select>

    <insert id="insRApproval">
        insert into t_ifr_base_bpm_approval_his(LOGS_ID,
                                                REQUEST_CODE,
                                                APPROVAL_STATUS,
                                                APPLICATION_TIME,
                                                APPLICATION_PERSON,
                                                REASON_REJECTION,
                                                HANDLE_STATUS)
        values (uuid(),
                #{requestCode},
                #{approvalStatus},
                #{applicationTime},
                #{applicationPerson},
                #{reasonRejection},
                '1')
    </insert>

    <update id="updateStatus">
        update t_usc_bu_dlr_build_subsidy_d
        set
        <if test='status =="1"'>
            AUDIT_STATUS='5',
            BPM_AUDITING_USER_NAME=#{applicationPerson},
            BPM_AUDITING_DATE=#{applicationTime},
        </if>
        <if test='status =="0"'>
            AUDIT_STATUS='6',
            REJECT_USER_NAME=#{applicationPerson},
            REJECT_DATE=#{applicationTime},
            REASON_REJECTION=#{reasonRejection},
        </if>
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        where DLR_BUILD_SUBSIDY_D_ID=#{requestCode}
    </update>

    <select id="findSubsidyD" resultType="java.util.Map">
        SELECT a.RENT_SUPPORT_DETAIL_ID,
               a.APPROVAL_STATUS approvalStatus,
               a.RENT_SUPPORT_ID rentSupportId,
               d.DLR_CODE        dlrCode,
               a.SUBSIDY_AMOUNT  subsidyAmount,
               c.AGENT_COMPANY_NAME,
               b.DLR_NAME,
               a.ORDER_NO,
               a.SUBSIDY_AMOUNT  subsidyAmount,
               CONCAT(
                       d.DLR_CODE,
                       '第',
                       a.ORDER_NO,
                       '笔',
                       '租金支持'
                   )             remark
        FROM mp.t_usc_bu_dlr_rent_support_detail a
                 LEFT JOIN mp.t_usc_bu_dlr_rent_support b ON a.RENT_SUPPORT_ID = b.RENT_SUPPORT_ID
                 LEFT JOIN mp.t_usc_mdm_org_employee e ON a.CREATOR = e.USER_ID
                 LEFT JOIN mp.t_usc_mdm_org_dlr d ON b.DLR_CODE = d.DLR_CODE
                 LEFT JOIN mp.t_usc_mdm_agent_company c ON d.COMPANY_ID = c.AGENT_COMPANY_ID
        where a.RENT_SUPPORT_DETAIL_ID = #{requestCode}
    </select>

    <update id="updateSubsidy">
        update t_usc_bu_dlr_build_subsidy
        set
        <if test='status =="1"'>
            APPROVAL_STATUS='5',
        </if>
        <if test='status =="0"'>
            APPROVAL_STATUS='6',
        </if>
        <if test='status =="2"'>
            PAY_STATUS='0',
            APPROVAL_STATUS='5',
        </if>
        LAST_UPDATED_DATE=#{applicationTime},
        UPDATE_CONTROL_ID=uuid()
        where DLR_BUILD_SUBSIDY_ID=#{dlrBuildSubsidyId}
    </update>

    <update id="updateSupportDetail">
        update t_usc_bu_dlr_rent_support_detail
        set
        <if test='status =="1"'>
            APPROVAL_STATUS='5',
            BPM_AUDITING_USER_NAME=#{applicationPerson},
            BPM_AUDITING_DATE=#{applicationTime},
        </if>
        <if test='status =="0"'>
            APPROVAL_STATUS='6',
            REJECT_USER_NAME=#{applicationPerson},
            REJECT_DATE=#{applicationTime},
            REASON_REJECTION=#{reasonRejection},
        </if>
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        where RENT_SUPPORT_DETAIL_ID=#{requestCode}
    </update>

    <update id="updateSupport">
        update t_usc_bu_dlr_rent_support
        set
        <if test='status =="1"'>
            APPROVAL_STATUS='5',
        </if>
        <if test='status =="0"'>
            APPROVAL_STATUS='6',
        </if>
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        where RENT_SUPPORT_ID=#{rentSupportId}
    </update>

    <insert id="insSapApproval">
        insert into t_ifs_base_sap_approval(LOGS_ID,
                                            ID,
                                            ZTYPE,
                                            LIFNR,
                                            AEDAT,
                                            LLIEF,
                                            BSART,
                                            EKORG,
                                            EKGRP,
                                            BUKRS,
                                            INSERT_DATE,
                                            SEND_FLAG)
        values (#{logsId},
                #{requestCode},
                #{ztype},
                #{lifnr},
                DATE_FORMAT(now(), '%Y-%m-%d'),
                #{llief},
                'NK',
                'PP01',
                'Z02',
                'SC02',
                now(),
                '0')
    </insert>

    <insert id="insSapApprovalData">
        insert into t_ifs_bsae_sap_approvaldata(LOGS_ID,
                                                ID,
                                                KNTTP,
                                                TXZ01,
                                                MENGE,
                                                MEINS,
                                                NETPR,
                                                MATKL,
                                                WERKS,
                                                SAKTO,
                                                KOSTL,
                                                INSERT_DATE,
                                                SEND_FLAG)
        values (#{logsId},
                '1',
                'K',
                #{txz01},
                1,
                'EA',
                #{amount},
                'M211',
                'C021',
                '6001090053',
                'SC02210301',
                now(),
                '0')
    </insert>

    <select id="findVirtualPhone" resultType="java.util.Map">
        select VIRTUAL_STATUS
        from mp.t_usc_mdm_virtual_phone
        where VIRTUAL_PHONE = #{virtualPhone}
    </select>

    <update id="updateVirtual">
        update mp.t_usc_mdm_virtual_phone
        set PROVINCE_ID=#{provinceId},
            CITY_ID=#{cityId},
            ORGANIZATION_NAME=#{organizationName},
            ORGANIZATION_NO=#{organizationNo},
            VIRTUAL_STATUS='0',
            MODIFIER='VIRTUAL_R_001',
            MODIFIER_NAME='VIRTUAL_R_001',
            LAST_UPDATED_DATE=now(),
            UPDATE_CONTROL_ID=uuid()
        where VIRTUAL_PHONE = #{virtualPhone}
    </update>

    <insert id="insertVirtual">
        insert into mp.t_usc_mdm_virtual_phone(VIRTUAL_ID,
                                               VIRTUAL_PHONE,
                                               CITY_ID,
                                               PROVINCE_ID,
                                               ORGANIZATION_NAME,
                                               ORGANIZATION_NO,
                                               VIRTUAL_STATUS,
                                               CREATOR,
                                               CREATED_NAME,
                                               CREATED_DATE,
                                               MODIFIER,
                                               MODIFIER_NAME,
                                               LAST_UPDATED_DATE,
                                               IS_ENABLE,
                                               UPDATE_CONTROL_ID)
        values (uuid(),
                #{virtualPhone},
                #{cityId},
                #{provinceId},
                #{organizationName},
                #{organizationNo},
                '0',
                'VIRTUAL_R_001',
                'VIRTUAL_R_001',
                now(),
                'VIRTUAL_R_001',
                'VIRTUAL_R_001',
                now(),
                '1',
                uuid())
    </insert>

    <select id="findEmpMsg" resultType="java.util.Map">
        select
            USER_ID,
            EMP_CODE
            from
                mp.t_usc_mdm_org_employee a
        where
            a.MOBILE=#{bindMobile}
        and a.USER_STATUS in ('1','3')

    </select>

    <select id="findOnecustInfo" resultType="java.util.Map">
        select
            CUST_ID,
            CUST_NAME,
            PHONE
        from
            csc.t_sac_onecust_info
        where PHONE=#{consumerMobile}
        limit 1
    </select>

    <insert id="insertVirtualRecord">
        insert into mp.t_usc_mdm_virtual_record(
            RECORD_ID,
            VIRTUAL_MOBILE,
            BIND_MOBILE,
            EMP_CODE,
            CONSUMER_MOBILE,
            CUST_ID,
            STATUS,
            CALL_TYPE,
            CALL_START_TIME,
            CALL_END_TIME,
            CALL_RECORD_URL,
            CALL_TIME,
            CREATOR,
            CREATED_DATE,
            MODIFIER,
            LAST_UPDATED_DATE,
            IS_ENABLE,
            UPDATE_CONTROL_ID,
            CALL_ID
        )values (
              uuid(),
            #{virtualMobile},
            #{bindMobile},
            #{empCode},
            #{consumerMobile},
            #{custId},
            #{status},
            #{callType},
            #{callStartTime},
            #{callEndTime},
            #{callRecordUrl},
            #{callTime},
            'VIRTUAL_R_002',
            now(),
              'VIRTUAL_R_002',
            now(),
            '1',
            uuid(),
            #{callId}
                        )
    </insert>

    <select id="findLookUpValue" resultType="com.ly.adp.xapi.api.tda.entity.LookupValue">
        select
            LOOKUP_VALUE_CODE,
            LOOKUP_VALUE_NAME
        from mp.t_prc_mds_lookup_value
        where  LOOKUP_TYPE_CODE='VE1116'
    </select>
</mapper>