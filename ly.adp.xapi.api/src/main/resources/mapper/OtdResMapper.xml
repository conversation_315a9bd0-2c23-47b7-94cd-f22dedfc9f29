<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.otd.mapper.OtdResMapper">
    <insert id="indertStockCar">
        INSERT INTO orc.t_orc_stock_vehicle (
        STOCK_CAR_ID,
        PNO18,
        STOCK_DEALER_CODE,
        VIN,
        SALES_STATUS,
        STOCK_DATE,
        VEHICLE_TYPE,
        CREATOR,
        CREATED_DATE,
        MODIFIER,
        LAST_UPDATED_DATE,
        IS_ENABLE,
        SDP_USER_ID,
        SDP_ORG_ID,
        UPDATE_CONTROL_ID,
        STOCK_STATUS,
        POLICY_CODE,
        POLICY_NAME,
        DISCOUNT_AMOUNT,
        IS_OVERLAY,
        SHELVE,
        BUSINESS_TYPE,
        EC_POLICY_STR,
        SALE_ORDER_CODE
        <if test="param.vehicleType !='' and param.vehicleType !=null and param.vehicleType == '4'.toString() ">
            ,MANUAL_SETTING,
            MANUAL_SETTING_PEOPLE,BATCH_SALE_SHELF_STATUS
        </if>
        )VALUES(
        #{param.stockCarId},
        #{param.pno18},
        #{param.stockDealerCode},
        #{param.vin},
        #{param.salesStatus},
        <choose>
            <when test="param.stockDate != null and param.stockDate != ''">
                #{param.stockDate},
            </when>
            <otherwise>
                null,
            </otherwise>
        </choose>
        #{param.vehicleType},
        'otd',
        now(),
        'otd',
        now(),
        '1',
        '88888',
        '2',
        uuid(),
        #{param.stockStatus},
        #{param.policyCode},
        #{param.policyName},
        #{param.carDiscountPrice},
        #{param.isOverlay},
        #{param.policyStatus},
        #{param.businessType},
        #{param.ecPolicyStr},
        #{param.retailNo}
        <if test="param.vehicleType !='' and param.vehicleType !=null and param.vehicleType == '4'.toString() ">
            , '1', 'sys',0
        </if>
        )
    </insert>
    <insert id="insertDetail">
        INSERT INTO orc.t_orc_stock_vehicle_scope (ID,
                                                   STOCK_CAR_ID,
                                                   VIN,
                                                   DEALER_CODE,
                                                   CREATOR,
                                                   CREATED_DATE,
                                                   MODIFIER,
                                                   LAST_UPDATED_DATE,
                                                   IS_ENABLE,
                                                   SDP_USER_ID,
                                                   SDP_ORG_ID,
                                                   UPDATE_CONTROL_ID)
        VALUES (uuid(),
                #{param.stockCarId},
                #{param.vin},
                #{param.dealerCode},
                'otd',
                now(),
                'otd',
                now(),
                '1',
                '88888',
                '2',
                uuid())
    </insert>


    <insert id="insertCdpAdpPhoneUpdate">
        INSERT INTO `interfacecenter`.`t_ifr_csc_cdp_phone`
        (`logs_id`,
        `smart_id`,
        `oldPhone`,
        `newPhone`,
        `receive_date`)
        VALUES
        <foreach collection="list" item="param" index="index" separator=",">
            (
            UUID(),
            #{param.smartId},
            #{param.oldPhone},
            #{param.newPhone},
            now()
            )
        </foreach>
    </insert>

    <update id="updateStockCar">
        update orc.t_orc_stock_vehicle
        set
        <if test="param.pno18 !='' and param.pno18 !=null ">
            PNO18=#{param.pno18},
        </if>
        <if test="param.stockDealerCode !='' and param.stockDealerCode !=null ">
            STOCK_DEALER_CODE=#{param.stockDealerCode},
        </if>
        <if test="param.vin !='' and param.vin !=null ">
            VIN=#{param.vin},
        </if>
        <if test="param.salesStatus !='' and param.salesStatus !=null ">
            SALES_STATUS=#{param.salesStatus},
        </if>
        <if test="param.stockDate !=null ">
            STOCK_DATE=IF(#{param.stockDate}='',null,#{param.stockDate}),
        </if>
        <if test="param.vehicleType !='' and param.vehicleType !=null ">
            VEHICLE_TYPE=#{param.vehicleType},
        </if>

            SALE_ORDER_CODE=#{param.retailNo},

        <if test="param.stockStatus !='' and param.stockStatus !=null ">
            STOCK_STATUS=#{param.stockStatus},
        </if>
        <if test="param.businessType !='' and param.businessType !=null ">
            BUSINESS_TYPE=#{param.businessType},
        </if>
        <if test="param.batchSaleShelfStatus !=null ">
            BATCH_SALE_SHELF_STATUS=#{param.batchSaleShelfStatus},
        </if>
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        where STOCK_CAR_ID=#{param.stockCarId}
    </update>
    <delete id="deleteDetail">
        delete
        from orc.t_orc_stock_vehicle_scope
        where STOCK_CAR_ID = #{param.stockCarId}
    </delete>
    <select id="selectStockCarCount" resultType="java.util.Map">
        SELECT *
        FROM orc.t_orc_stock_vehicle
        where VIN = #{param.vin}
    </select>
    <select id="selectDetailCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM orc.t_orc_stock_vehicle_scope
        where STOCK_CAR_ID = #{param.stockCarId}
    </select>
    <select id="selectDlrCodeList" resultType="java.lang.String">
        SELECT DLR_CODE
        FROM mp.t_usc_mdm_org_dlr
        WHERE DLR_TYPE IN ('BC', 'POS')
          and ONLINE_FLAG = '1'
          AND COMPANY_ID IN (SELECT c.AGENT_COMPANY_ID
                             FROM mp.t_usc_mdm_agent_company c
                                      INNER JOIN mp.t_usc_mdm_org_dlr d ON d.COMPANY_ID = c.AGENT_COMPANY_ID
                             WHERE DLR_CODE = #{param.stockDealerCode})
    </select>


    <select id="selectOrderIsExists" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT DISSOCIATE_CODE,
               SALE_ORDER_CODE,
               APPLY_TYPE,
               APPLY_STATUS,
               COLUMN1
        FROM orc.t_orc_ve_bu_order_dissociate
        where DISSOCIATE_CODE = #{param.dissociateCode}
          and SALE_ORDER_CODE = #{param.saleOrderCode}
    </select>

    <update id="updateOrderByRevocation">
        update orc.t_orc_ve_bu_order_dissociate
        set APPLY_STATUS      = '6',
            MODIFIER          = 'SDC',
            MODIFY_NAME       = 'SDC',
            LAST_UPDATED_DATE = now(),
            UPDATE_CONTROL_ID = uuid()
        where DISSOCIATE_CODE = #{param.dissociateCode}
          and SALE_ORDER_CODE = #{param.saleOrderCode}
    </update>

    <select id="findSalesRange" resultType="java.util.Map">
        select SALES_RANGE
        from orc.t_orc_stock_vehicle_salesrange
        where PNO18 = #{PON18}
          and IS_ENABLE = '1'
          and VEHICLE_TYPE = #{CAR_TYPE}
          and STOCK_DEALER_CODE = #{STORE_HOUSE}
    </select>

    <select id="findNationwideDlr" resultType="java.lang.String">
        SELECT DLR_CODE
        FROM mp.t_usc_mdm_org_dlr
        WHERE DLR_TYPE IN ('BC', 'POS')
          and ONLINE_FLAG = '1'
    </select>

    <insert id="insertNationwideDetail">
        INSERT INTO orc.t_orc_stock_vehicle_scope (ID,
        STOCK_CAR_ID,
        VIN,
        DEALER_CODE,
        CREATOR,
        CREATED_DATE,
        MODIFIER,
        LAST_UPDATED_DATE,
        IS_ENABLE,
        SDP_USER_ID,
        SDP_ORG_ID,
        UPDATE_CONTROL_ID)
        VALUES
        <foreach collection="list" separator="," item="item">
            (uuid(),
            #{param.stockCarId},
            #{param.vin},
            #{item},
            'otd',
            now(),
            'otd',
            now(),
            '1',
            '88888',
            '2',
            uuid())
        </foreach>
    </insert>

    <insert id="insertDlrDetail">
        INSERT INTO orc.t_orc_stock_vehicle_scope (ID,
        STOCK_CAR_ID,
        VIN,
        DEALER_CODE,
        CREATOR,
        CREATED_DATE,
        MODIFIER,
        LAST_UPDATED_DATE,
        IS_ENABLE,
        SDP_USER_ID,
        SDP_ORG_ID,
        UPDATE_CONTROL_ID)
        VALUES
        <foreach collection="salesRange.split(',')" separator="," item="item">
            (uuid(),
            #{param.stockCarId},
            #{param.vin},
            #{item},
            'otd',
            now(),
            'otd',
            now(),
            '1',
            '88888',
            '2',
            uuid())
        </foreach>
    </insert>

    <select id="findRegionDlr" resultType="java.lang.String">
        SELECT t2.dlr_code
        from mp.t_usc_mdm_org_dlr t2

                 inner JOIN mp.t_usc_mdm_agent_area t12 ON t12.AGENT_ID = t2.DLR_ID AND t12.AREA_TYPE = '0' /*门店对应省份信息*/
                 inner JOIN mp.t_usc_area_relation t13
                            ON t12.AREA_ID = t13.REL_OBJ_ID AND t13.REL_OBJ_TYPE = '1' /*大区-省份 关联关系*/
                 inner JOIN mp.t_usc_area_info t14
                            ON t14.AREA_ID = t13.AREA_ID AND t14.AREA_TYPE = '1' /*大区信息: 大区ID、大区名称*/
        where t14.AREA_ID = #{region}
          and ONLINE_FLAG = '1'
    </select>

    <select id="findRegion" resultType="java.lang.String">
        SELECT t14.AREA_ID
        FROM mp.t_usc_mdm_org_dlr t2
                 INNER JOIN mp.t_usc_mdm_agent_area t12 ON t12.AGENT_ID = t2.DLR_ID
                 INNER JOIN mp.t_usc_area_relation t13 ON t12.AREA_ID = t13.REL_OBJ_ID
            AND t13.REL_OBJ_TYPE = '1'
                 INNER JOIN mp.t_usc_area_info t14 ON t14.AREA_ID = t13.AREA_ID
            AND t14.AREA_TYPE = '1'

        WHERE t12.AREA_TYPE = '0'
          and t2.dlr_code = #{STORE_HOUSE}
    </select>

    <select id="findAgent" resultType="java.lang.String">
        SELECT t2.DLR_CODE
        FROM mp.t_usc_mdm_org_dlr t2
                 inner join mp.t_usc_mdm_agent_company t10 ON t2.COMPANY_ID = t10.AGENT_COMPANY_ID
        WHERE t2.DLR_TYPE IN ('BC', 'POS')
          and t2.ONLINE_FLAG = '1'
          AND t10.AGENT_ID IN (SELECT t10.AGENT_ID
                               FROM mp.t_usc_mdm_org_dlr c
                                        INNER JOIN mp.t_usc_mdm_agent_company t10 ON c.COMPANY_ID = t10.AGENT_COMPANY_ID
                               WHERE DLR_CODE = #{STORE_HOUSE})
    </select>

    <select id="findDCRange" resultType="java.util.Map">
        select SALES_RANGE
        from orc.t_orc_stock_vehicle_salesrange
        where PNO18 = #{PON18}
          and IS_ENABLE = '1'
          and VEHICLE_TYPE = #{CAR_TYPE}
          and STOCK_DEALER_CODE = 'DC'
    </select>

    <update id="updateOrderToTradeinright">
        update
        orc.t_orc_ve_bu_order_to_tradeinright
        set APPLY_STATUS=#{rightStatus},
        <if test="passTime !=null and passTime !=''">
            PASS_TIME=#{passTime},
        </if>
        <if test="activePreferential !=null and activePreferential !=''">
            ACTIVE_PREFERENTIAL=#{activePreferential},
        </if>
        REJECT_REASON=#{rejectReason},
        MODIFIER='tradein_adp_rightStatus',
        LAST_UPDATED_DATE=now(),
        UPDATE_CONTROL_ID=uuid()
        where
        SALE_ORDER_CODE = #{saleOrderCode}
        and REPLACE_ORDER_NUM=#{replaceOrderNum}
    </update>

    <update id="updateStockVehicle">
        update orc.t_orc_stock_vehicle
        set
        <if test="policyStatus !=null ">
            SHELVE=#{policyStatus},
        </if>
        <if test="isOverlay !=null ">
            IS_OVERLAY=#{isOverlay},
        </if>
        <if test="carDiscountPrice !=null ">
            DISCOUNT_AMOUNT=#{carDiscountPrice},
        </if>
        <if test="policyName !=null and policyName !=''">
            POLICY_NAME=#{policyName},
        </if>
        <if test="policyCode !=null and policyCode !=''">
            POLICY_CODE=#{policyCode},
        </if>
        <if test="ecPolicyStr !=null and ecPolicyStr !=''">
            EC_POLICY_STR=#{ecPolicyStr},
        </if>
        MODIFIER='ec_adp_policyDiscount',
        LAST_UPDATED_DATE=now()
        where
        vin=#{vin}
    </update>

    <select id="findStockVehicle" resultType="java.util.Map">
        SELECT
            MANUAL_SETTING,
            STOCK_CAR_ID,
            VEHICLE_TYPE,
            PNO18 pno18
            FROM orc.t_orc_stock_vehicle
        WHERE
            VIN=#{vin}
    </select>

    <select id="findRange" resultType="java.util.Map">
        select
            SALES_RANGE
        from orc.t_orc_stock_vehicle_salesrange
        where
            PNO18 = #{param.pno18}
          and IS_ENABLE = '1'
          and VEHICLE_TYPE = #{param.vehicleType}
          and STOCK_DEALER_CODE = #{storeHouse}
    </select>

    <select id="selecDlrCodeList" resultType="java.lang.String">
        SELECT
            DLR_CODE
        FROM mp.t_usc_mdm_org_dlr
        WHERE DLR_TYPE IN ('BC', 'POS')
          and ONLINE_FLAG = '1'
          AND COMPANY_ID IN (SELECT c.AGENT_COMPANY_ID
                             FROM mp.t_usc_mdm_agent_company c
                                      INNER JOIN mp.t_usc_mdm_org_dlr d ON d.COMPANY_ID = c.AGENT_COMPANY_ID
                             WHERE DLR_CODE = #{param.storeHouse})
    </select>

    <select id="findEcRegion" resultType="java.lang.String">
        SELECT
            t14.AREA_ID
        FROM mp.t_usc_mdm_org_dlr t2
                 INNER JOIN mp.t_usc_mdm_agent_area t12 ON t12.AGENT_ID = t2.DLR_ID
                 INNER JOIN mp.t_usc_area_relation t13 ON t12.AREA_ID = t13.REL_OBJ_ID
            AND t13.REL_OBJ_TYPE = '1'
                 INNER JOIN mp.t_usc_area_info t14 ON t14.AREA_ID = t13.AREA_ID
            AND t14.AREA_TYPE = '1'

        WHERE t12.AREA_TYPE = '0'
          and t2.dlr_code = #{storeHouse}
    </select>

    <select id="findecAgent" resultType="java.lang.String">

        SELECT t2.DLR_CODE
        FROM mp.t_usc_mdm_org_dlr t2
                 inner join mp.t_usc_mdm_agent_company t10 ON t2.COMPANY_ID = t10.AGENT_COMPANY_ID
        WHERE t2.DLR_TYPE IN ('BC', 'POS')
          and t2.ONLINE_FLAG = '1'
          AND t10.AGENT_ID IN (SELECT t10.AGENT_ID
                               FROM mp.t_usc_mdm_org_dlr c
                                        INNER JOIN mp.t_usc_mdm_agent_company t10 ON c.COMPANY_ID = t10.AGENT_COMPANY_ID
                               WHERE DLR_CODE = #{storeHouse})
    </select>

    <select id="findecUrl" resultType="java.lang.String">

        select
            LOOKUP_VALUE_NAME
            from
                mp.t_prc_mds_lookup_value
            where LOOKUP_TYPE_CODE='VE1133'
    </select>

    <select id="findEcDCRange" resultType="java.util.Map">
        select SALES_RANGE
        from orc.t_orc_stock_vehicle_salesrange
        where PNO18 = #{pno18}
          and IS_ENABLE = '1'
          and VEHICLE_TYPE = #{vehicleType}
          and STOCK_DEALER_CODE = 'DC'
    </select>

    <select id="queryVinExists" resultType="int">
        select
        exists
        (select 1 from orc.t_orc_stock_vehicle where vin = #{vin}) as count
    </select>

    <select id="queryAgentCityDlr" resultType="java.lang.String">
        SELECT
            t2.DLR_CODE
        FROM
            mp.t_usc_mdm_org_dlr t2
                INNER JOIN mp.t_usc_mdm_agent_company t10 ON t2.COMPANY_ID = t10.AGENT_COMPANY_ID
        WHERE
            t2.DLR_TYPE IN ('BC', 'POS')
          AND t2.ONLINE_FLAG = '1'
          AND t10.AGENT_ID = (
            SELECT t10_inner.AGENT_ID
            FROM mp.t_usc_mdm_org_dlr c_inner
                     INNER JOIN mp.t_usc_mdm_agent_company t10_inner ON c_inner.COMPANY_ID = t10_inner.AGENT_COMPANY_ID
            WHERE c_inner.DLR_CODE = #{storeHouse}
        )
          AND t2.CITY_ID = (
            SELECT c.CITY_ID
            FROM mp.t_usc_mdm_org_dlr c
            WHERE c.DLR_CODE = #{storeHouse}
        )
    </select>
</mapper>