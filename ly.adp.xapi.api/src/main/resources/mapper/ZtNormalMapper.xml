<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.zt.mapper.IZtNormalMapper">
    <select id="findClueInfoDlr" resultType="java.util.Map">
        SELECT b.EMP_NAME,
               b.EMP_CODE,
               c.DLR_CODE,
               c.DLR_SHORT_NAME,
               b.HEAD_PORTRAIT,
               a.DLR_CODE infoDlrCode,
               a.REVIEW_ID,
            b.USER_STATUS
        FROM csc.t_sac_clue_info_dlr a
                 LEFT JOIN mp.t_usc_mdm_org_employee b ON a.REVIEW_PERSON_ID = b.USER_ID
                 LEFT JOIN mp.t_usc_mdm_org_dlr c ON b.DLR_CODE = c.DLR_CODE
        WHERE a.PHONE = #{phone}
          and a.STATUS_CODE !='10'
    </select>

    <select id="finddownloadOSS" resultType="java.lang.String">
        select LOOKUP_VALUE_NAME
        from mp.t_prc_mds_lookup_value
        where LOOKUP_TYPE_CODE = 'VE1114'
          and LOOKUP_VALUE_CODE = '1'
    </select>

    <select id="finddownload" resultType="java.lang.String">
        select LOOKUP_VALUE_NAME
        from mp.t_prc_mds_lookup_value
        where LOOKUP_TYPE_CODE = 'VE1114'
          and LOOKUP_VALUE_CODE = '2'
    </select>

    <select id="findDlrEmpSum" resultType="java.lang.Integer">
        SELECT count(1)
        FROM mp.t_usc_mdm_org_employee b
            LEFT JOIN mp.t_usc_mdm_org_dlr c ON b.DLR_CODE = c.DLR_CODE
        WHERE b.DLR_CODE = #{dlrCode}
          and b.USER_STATUS = '1'
          and b.STATION_ID in ('smart_bm_0007', 'smart_bm_0018', 'smart_bm_0061', 'smart_bm_0064')
    </select>

    <select id="findDlrEmp" resultType="java.util.Map">
        SELECT b.EMP_NAME,
               b.EMP_CODE,
               c.DLR_CODE,
               c.DLR_SHORT_NAME,
               b.HEAD_PORTRAIT,
               b.USER_ID
        FROM mp.t_usc_mdm_org_employee b
                 LEFT JOIN mp.t_usc_mdm_org_dlr c ON b.DLR_CODE = c.DLR_CODE
        WHERE b.DLR_CODE = #{dlrCode}
          and b.USER_STATUS = '1'
          and b.STATION_ID in ('smart_bm_0007', 'smart_bm_0018', 'smart_bm_0061', 'smart_bm_0064')
    </select>

    <update id="updateInfoDlr">
        update
            adp_leads.t_sac_clue_info_dlr
        set REVIEW_PERSON_NAME=#{param.empName},
            REVIEW_PERSON_ID=#{param.userId}
        where REVIEW_ID = #{reviewId}
    </update>

    <update id="updateInfoDlrCsc">
        update
        csc.t_sac_clue_info_dlr
        set REVIEW_PERSON_NAME=#{param.empName},
        REVIEW_PERSON_ID=#{param.userId}
        where REVIEW_ID = #{reviewId}
    </update>

    <update id="updateReview">
        update
            csc.t_sac_review
        set ASSIGN_STATUS='1',
            ASSIGN_STATUS_NAME='已分配',
            ASSIGN_TIME=now(),
            REVIEW_PERSON_ID=#{param.userId},
            REVIEW_PERSON_NAME=#{param.empName}
        where REVIEW_ID = #{reviewId}
    </update>
</mapper>