<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.sdc.mapper.SdcResMapper">
    <insert id="insertOrderDissociate">
        INSERT INTO t_ifr_base_sdc_order_dissociate_his (
            logs_id,
            dissociate_code,
            sale_order_code,
            pno18,
            vin,
            is_adp_audit,
            initiate_sys,
            apply_type,
            apply_time,
            apply_reason,
            xapibatchno,
            insert_date,
            receive_flag,
            err_log,
            receive_date

        )VALUES(
            #{logsId},
            #{dissociateCode},
            #{saleOrderCode},
            #{pno18},
            #{vin},
            #{isAdpAudit},
            #{initiateSys},
            #{applyType},
            #{applyTime},
            #{applyReason},
            uuid(),
            now(),
            '0',
            null,
            now()
        )
    </insert>

    <insert id="insertOrderDissociateFiles">
        INSERT INTO t_ifr_base_sdc_order_dissociate_files_his (
            logs_id_d,
            logs_id,
            file_url,
            file_name,
            xapibatchno,
            insert_date,
            receive_flag,
            err_log,
            receive_date
        )VALUES(
                   uuid(),
                   #{logsId},
                   #{fileUrl},
                   #{fileName},
                   uuid(),
                   now(),
                   '0',
                   null,
                   now()
               )
    </insert>
    <insert id="orderDissociateApply">
        INSERT INTO orc.t_orc_ve_bu_order_dissociate(
            DISSOCIATE_ID,
            DISSOCIATE_CODE,
            SALE_ORDER_CODE,
            APPLY_TYPE,
            APPLY_STATUS,
            PNO18,
            VIN,
            IS_ADP_AUDIT,
            INITIATE_SYS,
            APPLY_TIME,
            APPLY_REASON,
            REMARK,
            COLUMN1,
            COLUMN2,
            COLUMN3,
            COLUMN4,
            COLUMN5,
            COLUMN6,
            COLUMN7,
            COLUMN8,
            COLUMN9,
            COLUMN10,
            IS_ENABLE,
            CREATOR,
            CREATED_NAME,
            CREATED_DATE,
            MODIFIER,
            MODIFY_NAME,
            LAST_UPDATED_DATE,
            UPDATE_CONTROL_ID
        )
        VALUES (
                   #{param.dissociateId},
                   #{param.dissociateCode},
                   #{param.saleOrderCode},
                   #{param.applyType},
                   #{param.applyStatus},
                   #{param.pno18},
                   #{param.vin},
                   #{param.isAdpAudit},
                   #{param.initiateSys},
                   #{param.applyTime},
                   #{param.applyReason},
                   #{param.remark},
                   #{param.column1},
                   #{param.column2},
                   #{param.column3},
                   #{param.column4},
                   #{param.column5},
                   #{param.column6},
                   #{param.column7},
                   #{param.column8},
                   #{param.column9},
                   #{param.column10},
                   '1',
                   'sdc',
                   'sdc',
                   now(),
                   'sdc',
                   'sdc',
                   now(),
                   uuid()
               )
    </insert>
    <insert id="dissociatefileSave">
        INSERT INTO orc.t_orc_ve_bu_order_dissociate_files(
            FILE_ID,
            DISSOCIATE_ID,
            FILE_URL,
            FILE_NAME,
            UPLOADER,
            UPLOAD_DATE
        )
        VALUES (
                   uuid(),
                   #{param.dissociateId},
                   #{param.fileUrl},
                   #{param.fileName},
                   'sdc',
                   now()
               )
    </insert>
    <select id="queryDissociateApply" resultType="java.lang.Integer">
        SELECT count(*) FROM  orc.t_orc_ve_bu_order_dissociate WHERE SALE_ORDER_CODE=#{saleOrderCode} and APPLY_TYPE=#{applyType} and APPLY_STATUS in ('0','1','2')
    </select>
    <select id="queryLookUpValue" resultType="java.util.Map">
        SELECT
            LOOKUP_VALUE_CODE,LOOKUP_VALUE_NAME,ATTRIBUTE1,REMARK
        FROM
            mp.t_prc_mds_lookup_value
        WHERE
            LOOKUP_TYPE_CODE=#{param.lookupTypeCode}
    </select>

</mapper>