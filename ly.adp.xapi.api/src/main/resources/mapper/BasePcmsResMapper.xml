<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.base.mapper.BasePcmsResMapper">
  	<insert id="insertPno18" parameterType="java.util.HashMap">			
			INSERT INTO `t_ifr_base_pcms_pno18` (
				`logs_id`,
				`pno18`,
				`vehicletypeDesc`,
				`platform`,
				`exteriorColor`,
				`interiorColor`,
				`descripTion`,
				`chineseDescripTion`,
				`insert_date`,
				`receive_flag`,
				`err_log`,
				`receive_date`,
				`xapibatchno`
			)
			VALUES
				(
					#{item.id} ,
					#{item.pno181} ,
					#{item.vehicletypeDesc} ,
					#{item.platform} ,
					#{item.exteriorColor} ,
					#{item.interiorColor} ,
					#{item.description} ,
					#{item.chineseDescription} ,
					NOW() ,
					'0' ,
					'',
					NOW(),
					#{item.xapibatchno}
				);
	</insert>	
	
		<insert id="insertOptional" parameterType="java.util.HashMap">			
			INSERT INTO `t_ifr_base_pcms_pno18andoptions` (
						`logs_id`,
						`optionalCode`,
						`insert_date`,
						`receive_flag`,
						`err_log`,
						`receive_date`,
						`xapibatchno`
					)
					VALUES
						(
							#{item.id} ,
							#{item.optionalCode} ,
							NOW(),
							'0',
							'',
							NOW(),
							#{item.xapibatchno}
						);
	</insert>
	
	<insert id="insertExteriors" parameterType="java.util.HashMap">			
			
		INSERT INTO `t_ifr_base_pcms_exteriors` (
			`logs_id`,
			`exteriorCodeId`,
			`vehicletypeDesc`,
			`platform`,
			`salesDesc`,
			`chineseDesc`,
			`insert_date`,
			`receive_flag`,
			`err_log`,
			`receive_date`
		)
		VALUES
			(
				#{item.id} ,
				#{item.exteriorCodeId} ,
				#{item.vehicletypeDesc} ,
				#{item.platform} ,
				#{item.salesDesc} ,
				#{item.chineseDesc} ,
				NOW(),
				'0',
				'',
				NOW()
			);
	</insert>
	
	<insert id="insertInteriors" parameterType="java.util.HashMap">			
			INSERT INTO `t_ifr_base_pcms_interior` (
					`logs_id`,
					`interiorCodeId`,
					`vehicletypeDesc`,
					`platform`,
					`salesDesc`,
					`chineseDesc`,
					`insert_date`,
					`receive_flag`,
					`err_log`,
					`receive_date`
				)
				VALUES
					(
						#{item.id} ,
						#{item.interiorCodeId} ,
						#{item.vehicletypeDesc} ,
						#{item.platform} ,
						#{item.salesDesc} ,
						#{item.chineseDesc} ,
						NOW(),
						'0',
						'',
						NOW()
					);
							
	</insert>
	
	<insert id="insertOptionalM" parameterType="java.util.HashMap">			
			INSERT INTO `t_ifr_base_pcms_optional` (
				`logs_id`,
				`optionCodeId`,
				`optionalDesc`,
				`optionalDescCN`,
				`insert_date`,
				`receive_flag`,
				`err_log`,
				`receive_date`
			)
			VALUES
				(
				#{item.id} ,
				#{item.optionCodeId} ,
				#{item.optionalDesc} ,
				#{item.optionalDescCn} ,
				NOW(),
				'0',
				'',
				NOW()
				);
	</insert>
	<insert id="insertTransfer">
		insert into t_ifr_csc_cec_transfer (
			applyDate,
			applyDesc,
			applyPerson,
			custName,
			err_log,
			inDlrCode,
			inDlrName,
			insert_date,
			logs_id,
			phone,
			receive_date,
			receive_flag,
			source,
			xapibatchno
		) values (
		          #{param.applyDate},
		          #{param.applyDesc},
		          #{param.applyPerson},
		          #{param.custName},
		          null,
		          #{param.inDlrCode},
		          #{param.inDlrName},
		          now(),
				  #{param.logsId},
		          #{param.phone},
		          now(),
		          '0',
		          #{param.source},
		          #{param.xapibatchno}
						 )
	</insert>
</mapper>
