<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.adp.xapi.api.base.mapper.CdpResMapper">
    <insert id="insertOnecustLogout">
        INSERT INTO ${table} (logs_id,
                              smartId,
                              mobile,
                              eventTime,
                              xapibatchno,
                              insert_date,
                              receive_flag,
                              err_log,
                              receive_date)
        VALUES (uuid(),
                #{smartId},
                #{mobile},
                #{eventTime},
                uuid(),
                now(),
                '0',
                null,
                now())
    </insert>
    <update id="updateClueInfo">
        UPDATE csc.t_sac_clue_info_dlr
        set COLUMN10=null,
            COLUMN20 = '1',
            FIRST_REVIEW_TIME=case when FIRST_REVIEW_TIME is null then now() else FIRST_REVIEW_TIME end,
            LAST_REVIEW_TIME=now(),
            LAST_UPDATED_DATE=now(),
            STATUS_CODE='10',
            STATUS_NAME='战败'
        where COLUMN10 = #{smartId}
    </update>
    <update id="updateOnecustInfo">
        UPDATE csc.t_sac_onecust_info
        set SMART_ID=null
        where SMART_ID = #{smartId}
    </update>
    <delete id="deleteReviewInfo">
        DELETE
        from csc.t_sac_review
        where REVIEW_ID in (SELECT REVIEW_ID from csc.t_sac_clue_info_dlr where COLUMN10 = #{smartId})
    </delete>
    <select id="queryClueInfo" resultType="java.util.Map">
        select *
        from csc.t_sac_clue_info_dlr
        where COLUMN10 = #{smartId}
    </select>

    <update id="updateCdpCustomers">
        update
            t_ifr_base_cdp_customers
        set receive_flag='88',
            err_log='注销线索不解析客户信息'
        where smartId = #{smartId}
    </update>

    <select id="findCustInfo" resultType="java.util.Map">
        select c.EMP_ID reviewPersonId,
               a.DLR_CODE,
               b.CUST_NAME,
               a.REVIEW_PERSON_NAME,
               a.cust_id
        from csc.t_sac_clue_info_dlr a
                 left join mp.t_usc_mdm_org_employee c on c.USER_ID = a.REVIEW_PERSON_ID
                 left join csc.t_sac_onecust_info b on a.CUST_ID = b.CUST_ID
        where a.COLUMN10 = #{smartId};
    </select>

    <select id="queryStoreManager" resultType="java.util.Map">
       SELECT
           employee.EMP_ID,employee.DLR_CODE
       FROM
           mp.t_usc_mdm_org_employee employee
            INNER JOIN mp.t_usc_mdm_org_station station ON employee.station_id = station.station_id
       WHERE
           employee.DLR_CODE = #{dlrCode} AND employee.USER_STATUS = '1' AND station.STATION_CODE = 'Store Manager'
    </select>

    <select id="findCustInfoByPhone" resultType="java.util.Map">
        select c.EMP_ID reviewPersonId,
               a.DLR_CODE,
               b.CUST_NAME
        from csc.t_sac_clue_info_dlr a
                 left join mp.t_usc_mdm_org_employee c on c.USER_ID = a.REVIEW_PERSON_ID
                 left join csc.t_sac_onecust_info b on a.CUST_ID = b.CUST_ID
        where a.PHONE = #{phone};
    </select>

    <insert id="createMsgRecord">
        INSERT INTO csc.t_sac_clue_msg_record (`MESSAGE_ID`,
                                               `IS_READ`,
                                               `DLR_CODE`,
                                               `PHONE`,
                                               `MESSAGE_TYPE`,
                                               `BUSI_KEYVALUE`,
                                               `RECEIVE_EMP_ID`,
                                               `MESSAGE_CONTENT`,
                                               `RELATION_BILL_ID`,
                                               `EXTEND_JSON`,
                                               `COLUMN1`,
                                               `OEM_ID`,
                                               `GROUP_ID`,
                                               `OEM_CODE`,
                                               `GROUP_CODE`,
                                               `CREATOR`,
                                               `CREATED_NAME`,
                                               `CREATED_DATE`,
                                               `MODIFIER`,
                                               `MODIFY_NAME`,
                                               `LAST_UPDATED_DATE`,
                                               `IS_ENABLE`,
                                               `SDP_USER_ID`,
                                               `SDP_ORG_ID`,
                                               `UPDATE_CONTROL_ID`)
        VALUES (#{param.messageId},
                '0',
                #{param.dlrCode},
                #{param.phone},
                '1',
                '1',
                #{param.reviewPersonId},
                #{param.messageContent},
                #{param.relationBillId},
                #{param.extendsJson},
                #{param.column1},
                '1',
                '1',
                '88888',
                '88888',
                'cdp_leads_event',
                'cdp_leads_event',
                #{param.createdDate},
                'cdp_leads_event',
                'cdp_leads_event',
                #{param.createdDate},
                '1',
                '1',
                '1',
                uuid())
    </insert>

    <delete id="deleteNewClueDB">
        DELETE FROM adp_leads.t_sac_clue_info_dlr
        WHERE COLUMN10 = #{param.smartId}
    </delete>

    <select id="isDccClueInfo" resultType="boolean">
        SELECT EXISTS (
            SELECT 1
            FROM csc.t_sac_clue_info_dlr
            WHERE phone = #{mobile}
              AND column19 = '1'
        )
    </select>
</mapper>