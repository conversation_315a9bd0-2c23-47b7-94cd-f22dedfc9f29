spring.application.name=ly-adp-xapi-api

#Ldap??
#spring.ldap.urls=ldaps://*************:389
spring.ldap.urls=ldaps://10.123.456.31:389
spring.ldap.base=dc=smart,dc=com
spring.ldap.username=<EMAIL>
spring.ldap.password=+LGzxCEzO~6@#
#MQ??

bucn.rabbitmq.init=true

bucn.rabbitmq.master=master

bucn.rabbitmq.pool.master.host=*************

bucn.rabbitmq.pool.master.port=5672

bucn.rabbitmq.pool.master.username=adp

bucn.rabbitmq.pool.master.password=adp@smart2021

bucn.rabbitmq.pool.master.virtualhost=/adp/public

bucn.rabbitmq.pool.ec.host=*************

bucn.rabbitmq.pool.ec.port=5672

bucn.rabbitmq.pool.ec.username=adp

bucn.rabbitmq.pool.ec.password=adp@smart2021

bucn.rabbitmq.pool.ec.virtualhost=/ec/business

bucn.rabbitmq.pool.otd.host=*************

bucn.rabbitmq.pool.otd.port=5672

bucn.rabbitmq.pool.otd.username=adp

bucn.rabbitmq.pool.otd.password=adp@smart2021

bucn.rabbitmq.pool.otd.virtualhost=/csp/business

bucn.rabbitmq.pool.tradein.host=*************

bucn.rabbitmq.pool.tradein.port=5672

bucn.rabbitmq.pool.tradein.username=adp

bucn.rabbitmq.pool.tradein.password=adp@smart2021

bucn.rabbitmq.pool.tradein.virtualhost=/tradein/public

bucn.rabbitmq.pool.cdp.host=*************

bucn.rabbitmq.pool.cdp.port=5672

bucn.rabbitmq.pool.cdp.username=adp

bucn.rabbitmq.pool.cdp.password=adp@smart2021

bucn.rabbitmq.pool.cdp.virtualhost=/cdp/business

bucn.rabbitmq.config.minIdle=8

bucn.rabbitmq.config.maxIdle=200

bucn.rabbitmq.config.maxTotal=230

seata.service.default.grouplist=adp-seata-server.adp-sit.svc:8091

#?????? ?????? ???????? ??????????ip??????

xapi.mc.clientUri=xapi/mc/server

#????? ????

xapi.mc.monitorUrl=http://adp-java-xapi-monitor.adp-sit.svc:8080/xapi/mc/server

#????? ????? ???????

xapi.mc.clientName=xapi-api

#????? ???? ? ???5

xapi.mc.heartSeconds=10

#?????? ???? ????????false

xapi.mc.isClient=true

#??????

server.port=8085

#??servers???(",")??,?????redis???IP,???redis??IP

#sentinel?????masterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,??mymaster?**************:63793,**************:63794

#redis.session.servers=**************:6379,**************:6379,**************:6379

redis.session.servers=**************:6379

#redis??,??redis????????

redis.session.password=adb@smart2021

#redis.servers=**************:6379,**************:6379,**************:6379

redis.servers=**************:6379

redis.password=adb@smart2021

#???????

redis.pool.maxActive=10000

#??????(??:?)

redis.pool.timeout=3000

#????(??:?)

redis.pool.expires=86400

#?????jedis??????????alidate??????true?????jedis????????

redis.pool.testOnBorrow=true

#?return?pool????????validate???

redis.pool.testOnReturn=true

#session time out (??:?)

session.timeout=1800

#redisson??????

#???????? true:?? false:???,?????, ????????? ????

redisson.redis.enable=true

#??????????????

redisson.redis.servers=**************:6379

redisson.redis.password=adb@smart2021

# ?????????????????????????????????????30000

redisson.redis.lockWatchdogTimeout=20000

# ????????????????????? 1000

redisson.redis.scanInterval=1000

# ??????????? ???????????????????????????????64

redisson.redis.masterConnectionPoolSize=64

# ??????????? ?????????????? ????????????????????????????????????64

redisson.redis.slaveConnectionPoolSize=64

#????AMQ(true,false)

mp.component.amqOpen=false

# MQ?? 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (????1:RabbitMQ, ??????amqType, ?????????ActiveMQ)

mp.component.amqType=3

mp.component.amqUrl=172.26.165.86:9876;172.26.165.93:9876

# MQ?????RabbitMQ??

mp.component.amqPort=5672

mp.component.amqUser=rocketadmin

mp.component.amqPwd=Mp@2020

#????????:???:????;???:???:????????????????????1????????????????????

mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue

#????????, ???false, ?????????, ????

mp.component.pendingMsg=true

#????????, ???false, ?????????, ????

mp.component.noticeMsg=true

#????CC??, ???false, ?????CC??, ????

mp.component.ccMsg=false

# ????????

mp.component.form.fileDir=/mpjava/form/files

#????????????????DAL?,???","????;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver

write.mp.jdbc.packagescan=com.ly.mp.**.mysql

#mp????(??)???? ??: normal ??(jta) : jta  ???:tcc

write.mp.jdbc.transactionPolicy=gtsx

#?????????????,??????????????????.?????????true:key????,false:key????

write.mp.jdbc.upperCaseColumn=true

# url,username,password???????????

write.mp.jdbc.name=mp_write

write.mp.jdbc.url=*************************************************************************************************************************************************

write.mp.jdbc.username=adpuser

write.mp.jdbc.password=Adp@smart2024

other.write.mp.jdbc.name[0]=base

other.write.mp.jdbc.url[0]=*************************************************************************************************************

other.write.mp.jdbc.username[0]=adpuser

other.write.mp.jdbc.password[0]=Adp@smart2024

other.write.mp.jdbc.name[1]=tidb

other.write.mp.jdbc.url[1]=********************************************************************************************************************

other.write.mp.jdbc.username[1]=appuser

other.write.mp.jdbc.password[1]=app@user!!

#read.jdbc.name[mp_write#1]=default_mp_read

#read.jdbc.url[mp_write#1]=********************************************

#read.jdbc.username[mp_write#1]=mp24

#read.jdbc.password[mp_write#1]=mp24

mp.read.db.size=0

#druid datasource

#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE

druid.initialSize=30

druid.minIdle=30

druid.maxActive=200

druid.maxWait=60000

druid.timeBetweenEvictionRunsMillis=60000

druid.minEvictableIdleTimeMillis=300000

druid.validationQuery=select 1 from dual

druid.testWhileIdle=true

druid.testOnBorrow=false

druid.testOnReturn=false

druid.poolPreparedStatements=false

druid.maxPoolPreparedStatementPerConnectionSize=20

#druid.keepAlive=true

druid.phyTimeoutMillis=1200000

#wall,slf4j,stat

druid.filters=stat

#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3

#mp2.24????

#mybatis

mybatis-plus.mapperLocations=classpath:/mapper/*Mapper.xml

#???????package?????????

mybatis-plus.typeAliasesPackage=com.ly.mp.meta.dev.entities

mybatis-plus.typeEnumsPackage=com.ly.mp.meta.dev.entities.enums

#???????

#????  AUTO:"???ID??", INPUT:"????ID",ID_WORKER:"????ID (??????ID)", UUID:"????ID UUID";

mybatis-plus.global-config.db-config.id-type=UUID

#???? IGNORED:"????",NOT_NULL:"? NULL ??"),NOT_EMPTY:"????"

mybatis-plus.global-config.db-config.field-strategy=not_empty

#???????

mybatis-plus.global-config.db-config.column-underline=true

#??????????

#capital-mode: true

#??????

mybatis-plus.global-config.db-config.logic-delete-value=0

mybatis-plus.global-config.db-config.logic-not-delete-value=1

#mybatis-plus.global-config.db-config.db-type=sqlserver

#??mapper ????

mybatis-plus.global-config.refresh=true

# ????

mybatis-plus.configuration.map-underscore-to-camel-case=true

mybatis-plus.configuration.cache-enabled=false

mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
# tda ??
refer.tda.url=http://tda-uat.smart.cn
refer.tda.token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************.gvoRLtH_jbItAKSpLagaVFnOW760fgKfXSJSsFL8SW4
refer.tda.offset=10
refer.tda.page=1
refer.tda.menu=%2Fsystem%2Faccount%2Fuser

refer.agent-clue.url = http://10.170.250.73:28083