#??????????????????????1
active.handle.expire.count=3
#\u662F\u5426\u5F00\u542F\u5B9A\u65F6\u4EFB\u52A1\uFF0C\u591A\u670D\u52A1\u90E8\u7F72\u65F6 \u8BBE\u7F6E\u4E3Afalse\uFF0C\u624B\u5DE5\u542F\u52A8\u4E00\u53F0
active.quartz.status=true
#\u4E3B\u52A8\u4EFB\u52A1\u5E76\u53D1\u9501\u673A\u5236 vm \u672C\u5730\u9501
active.handle.type=db
#\u53D1\u9001
active.send.system=sap_adp,ADP_AD,cdp_adp,activity_adp,ec_adp,otd_adp,ADP_CSP,BPM_ADP
#dbjob
active.dbjob.system=
#\u5B9A\u65F6\u83B7\u53D6
active.pull.system=PCMS_ADP,sap_adp,cdp_adp,activity_adp,TRAIN_ADP
#\u63A5\u6536
passive.receive.system=
#\u54CD\u5E94
passive.reactive.system=acc_adp
#\u63A5\u6536\u767D\u540D\u5355
passive.receive.noauth.system=activity_adp
passive.reactive.noauth.system=adp_common,activity_adp,adp_mmd
passive.normal.noauth.system=ADP_MS,ADP_INSERT,sdc_adp,otd_adp
#\u8BC1\u4E66\u5730\u5740
keystore.path=/xapi
#\u65E5\u5FD7\u8BB0\u5F55\u65B9\u5F0F mysql;mongo;oracle
log.dbtype=db
#\u8C03\u7528\u5F02\u5E38\u901A\u77E5\u6A21\u5F0F \uFF1Aemail-\u90AE\u4EF6\u901A\u77E5
error.listen=db
#xapi\u6CE8\u89E3\u626B\u63CF\u5305\u8DEF\u5F84
xapi.business.package=com.ly.adp.xapi
#\u8868\u540D\u79F0\u914D\u7F6E
xapi.conf.table.system=t_xapi_system_config
xapi.conf.table.kv=t_xapi_kv_config
xapi.conf.table.object=t_xapi_object_config
xapi.conf.table.table=t_xapi_table_config
xapi.conf.table.field=t_xapi_field_config
xapi.conf.table.log=t_xapi_log_info
xapi.conf.table.version=t_xapi_config_version
xapi.conf.table.error=t_xapi_error_info
xapi.conf.table.notice=t_xapi_notice_info
xapi.conf.table.msg=t_xapi_msg_info
#\u76D1\u63A7\u901A\u8BAF\u5730\u5740 \u82E5\u4E3A\u76D1\u63A7\u5E73\u53F0 \u5219\u4E3A\u76D1\u63A7\u670D\u52A1\u5730\u5740 \u5BA2\u6237\u7AEF\u914D\u7F6E\u6539\u670D\u52A1\u534F\u8BAEip\u7AEF\u53E3\u53CA\u8BE5\u8DEF\u5F84
xapi.mc.clientUri=xapi/mc/server


#\u901A\u77E5\u670D\u52A1\u914D\u7F6E
#\u6D88\u606F\u68C0\u6D4B\u53D1\u9001\u9891\u7387 \u79D2 \u9ED8\u8BA460
xapi.notice.rate=60
#\u6D88\u606F\u751F\u6210\u9891\u7387 [level][name][seconds] \u591A\u4E2A,\u5206\u5272;level\u4E3A\u6D88\u606F\u4E3B\u9898;\u5982\u679Cseconds\u5C0F\u4E8E\u7B49\u4E8E0,\u5219\u4E0D\u4F1A\u67E5\u627E\u7EC4\u4EF6\u8C03\u7528
xapi.notice.msg=[xapimonitor][\u7CFB\u7EDF\u76D1\u63A7][0],[xapi_test][\u6837\u4F8B][0],
#token\u8BA4\u8BC1\u4F7F\u7528\u7684aes\u79D8\u94A5\uFF0C\u591A\u670D\u52A1\u90E8\u7F72\u65F6\uFF0C\u4FDD\u6301\u4E00\u81F4\uFF0C\u4E0D\u586B\u4EE5\u4EE3\u7801\u5185\u56FA\u5B9A\u4E3A\u51C6
#token\u5BF9\u5E94aes\u516C\u94A5
token.pubkey.aes=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCCBZuMX1ZqZlL7YFGsqfqHX59TAcsnW2smCZDOMOj67oil9lo8hPE6vyv87keO/uZlTCtGT79PCGEXQ51bkEBBzlRv1Dfc2fO8mqGEoSKj/fULVqDBtpZ33WGbO7Vg7Rjd8teTR16B0xkNvbFZTI4w1lbDu27Ajxh4JOfBa8sGiwIDAQAB
#token\u5BF9\u5E94aes\u79C1\u94A5
token.prikey.aes=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAIIFm4xfVmpmUvtgUayp+odfn1MByydbayYJkM4w6PruiKX2WjyE8Tq/K/zuR47+5mVMK0ZPv08IYRdDnVuQQEHOVG/UN9zZ87yaoYShIqP99QtWoMG2lnfdYZs7tWDtGN3y15NHXoHTGQ29sVlMjjDWVsO7bsCPGHgk58FrywaLAgMBAAECgYAF1rigehuIVaX+Udn5Ff0BHUMrWBZGYy2RnLZa/AlKU3HhegmdVB79E16nUg/2lO3FuMDsvGIL64VopQmo9fZcrbgyQXxUr+5anZ5JOwpEeTyoASH+OuuvpiKl5NThsa0hiJk+9cYaLriWjkIAazmkqBwwMI5SigKtQJ7lerE1EQJBAMRcIcZCbM34v/CX9emeV8q7KShNawxzU11O2Qvlgxk+zunkcn5bS/q1v0T93i926FBntDXpZmq8eB1iuXrMO6MCQQCpg2XUUqv1GXi1cOj64z3j+HLxsku2PFGpXaaoTqV/wGEsX0pGnXDADUDT/OzfJsbvdh+5JWXxdCjMyoYxpzf5AkAlcvjSCgscQUvCBAlXc+aEeKuBzzoSVtBYZFhKx4v4PZ1SyEn5xUm5V/5RlyVP4hqucwjQm2H4C1fRKun6/IaHAkAvqCJjzA5OLSgTkrfdeS+4POxL6T2B8Xlf7VViciHzRanMKMogCjldtRwpvSz3G9bIEeSfT9VWSbsCwFlz8jg5AkEAuXXZ2x07WZU3DziqVyJBXf8kwOqI6yzp46CtJzeRe+lQHNu87pqx3OPZtxxwn4Yf1wOi6M1KLOJ50UH4nwGRIQ==
#\u73AF\u5883\u4FE1\u606F dev,prod;dev \u5141\u8BB8\u5237\u65B0\u914D\u7F6E\uFF0C\u4F7F\u7528ReentrantReadWriteLock\u63A7\u5236\uFF1Bprod \u4E0D\u5141\u8BB8\u5237\u65B0\u914D\u7F6E\uFF0C\u4F7F\u7528UnDoLock\u63A7\u5236\uFF1B
xapi.environment=dev
#\u767D\u540D\u5355 [\u7CFB\u7EDFid] \u4E0D\u914D\u7F6E\u4E3A\u4E0D\u9650\u5236
#whitelist[TEST]= 0:0:0:0:0:0:0:1
whitelist[pur]=127.0.0.1,127.0.0.2