spring.application.name=ly-adp-xapi-api
#Ldap\u914D\u7F6E
spring.ldap.urls=ldaps://*************:636
spring.ldap.base=dc=smart,dc=com
spring.ldap.username=<EMAIL>
spring.ldap.password=+LGzxCEzO~6@#
#MQ\u914D\u7F6E
bucn.rabbitmq.init=true
bucn.rabbitmq.master=master
bucn.rabbitmq.pool.master.host=**************
bucn.rabbitmq.pool.master.port=5672
bucn.rabbitmq.pool.master.username=adp
bucn.rabbitmq.pool.master.password=adp@smart2021
bucn.rabbitmq.pool.master.virtualhost=/adp/public
bucn.rabbitmq.pool.ec.host=**************
bucn.rabbitmq.pool.ec.port=5672
bucn.rabbitmq.pool.ec.username=adp
bucn.rabbitmq.pool.ec.password=adp@smart2021
bucn.rabbitmq.pool.ec.virtualhost=/ec/business
bucn.rabbitmq.pool.otd.host=**************
bucn.rabbitmq.pool.otd.port=5672
bucn.rabbitmq.pool.otd.username=adp
bucn.rabbitmq.pool.otd.password=adp@smart2021
bucn.rabbitmq.pool.otd.virtualhost=/csp/business
bucn.rabbitmq.pool.tradein.host=**************
bucn.rabbitmq.pool.tradein.port=5672
bucn.rabbitmq.pool.tradein.username=adp
bucn.rabbitmq.pool.tradein.password=adp@smart2021
bucn.rabbitmq.pool.tradein.virtualhost=/tradein/public
bucn.rabbitmq.pool.cdp.host=*************
bucn.rabbitmq.pool.cdp.port=5672
bucn.rabbitmq.pool.cdp.username=adp
bucn.rabbitmq.pool.cdp.password=adp@smart2021
bucn.rabbitmq.pool.cdp.virtualhost=/cdp/business
bucn.rabbitmq.config.minIdle=8
bucn.rabbitmq.config.maxIdle=200
bucn.rabbitmq.config.maxTotal=230

seata.enabled=true
seata.application-id=ly-adp-xapi-api
seata.tx-service-group=default_tx_group
seata.scan-packages=com.ly.adp
seata.enable-auto-data-source-proxy=false
seata.data-source-proxy-mode=AT
seata.service.vgroup-mapping.default_tx_group=default
seata.service.default.grouplist=*************:8091
service.vgroupMapping.default_tx_group=*************:8091

#\u76D1\u63A7\u901A\u8BAF\u5730\u5740 \u82E5\u4E3A\u76D1\u63A7\u5E73\u53F0 \u5219\u4E3A\u76D1\u63A7\u670D\u52A1\u5730\u5740 \u5BA2\u6237\u7AEF\u914D\u7F6E\u6539\u670D\u52A1\u534F\u8BAEip\u7AEF\u53E3\u53CA\u8BE5\u8DEF\u5F84
xapi.mc.clientUri=xapi/mc/server
#\u5BA2\u6237\u7AEF\u914D\u7F6E \u76D1\u63A7\u5730\u5740
xapi.mc.monitorUrl=http://adp-java-xapi-monitor.adp-uat.svc:8080/xapi/mc/server
#\u5BA2\u6237\u7AEF\u914D\u7F6E \u5BA2\u6237\u7AEF\u540D\u79F0 \u9ED8\u8BA4\u542F\u52A8\u7C7B\u540D\u79F0
xapi.mc.clientName=xapi-api
#\u5BA2\u6237\u7AEF\u914D\u7F6E \u5FC3\u8DF3\u5468\u671F \u79D2 \u9ED8\u8BA4\u662F5
xapi.mc.heartSeconds=10
#\u662F\u5426\u662F\u5BA2\u6237\u7AEF \u9ED8\u8BA4\u4E3A\u662F \u7BA1\u7406\u5E73\u53F0\u9700\u6307\u5B9A\u4E3Afalse
xapi.mc.isClient=true
#\u5206\u5E03\u5F0F\u7684\u7AEF\u53E3
server.port=8085
# tomcat\u6700\u5927\u7EBF\u7A0B\u6570\uFF0C\u9ED8\u8BA4\u4E3A200
server.tomcat.max-threads=500
#Redis\u914D\u7F6E
#\u591A\u4E2Aservers\u7528\u9017\u53F7(",")\u9694\u5F00,\u4E0D\u9700\u8981\u914D\u7F6Eredis\u4ECE\u673A\u7684IP,\u53EA\u9700\u8981redis\u4E3B\u673AIP
#sentinel\u6A21\u5F0F\u7684\u683C\u5F0FmasterName?sentineIp1:sentinePort,sentineIp2:sentinePort ,\u4F8B\u5982mymaster?**************:63793,**************:63794
redis.session.servers=*************:6379,**************:6379
#redis\u5BC6\u7801,\u6240\u6709redis\u670D\u52A1\u5BC6\u7801\u5FC5\u987B\u4E00\u6837
redis.session.password=adb@smart2021 
redis.servers=*************:6379,**************:6379
redis.password=adb@smart2021
#\u6700\u5927\u8FDE\u63A5\u7EBF\u7A0B\u6570
redis.pool.maxActive=10000
#\u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4(\u5355\u4F4D:\u79D2)
redis.pool.timeout=3000
#\u7F13\u5B58\u65F6\u95F4(\u5355\u4F4D:\u79D2)
redis.pool.expires=86400
#\u5728\u83B7\u53D6\u4E00\u4E2Ajedis\u5B9E\u4F8B\u65F6\uFF0C\u662F\u5426\u63D0\u524D\u8FDB\u884Calidate\u64CD\u4F5C\uFF1B\u5982\u679C\u4E3Atrue\uFF0C\u5219\u5F97\u5230\u7684jedis\u5B9E\u4F8B\u5747\u662F\u53EF\u7528\u7684\uFF1B
redis.pool.testOnBorrow=true
#\u5728return\u7ED9pool\u65F6\uFF0C\u662F\u5426\u63D0\u524D\u8FDB\u884Cvalidate\u64CD\u4F5C\uFF1B
redis.pool.testOnReturn=true
#session time out (\u5355\u4F4D:\u79D2)
session.timeout=1800
#  redisson\u5206\u5E03\u5F0F\u9501\u914D\u7F6E
# \u662F\u5426\u542F\u7528\u5206\u5E03\u5F0F\u9501 true:\u542F\u7528 false:\u4E0D\u542F\u7528,\u9ED8\u8BA4\u4E0D\u542F\u7528, \u5982\u679C\u6CA1\u7528\u5230\u5206\u5E03\u5F0F\u9501 \u4E0D\u8981\u542F\u7528
redisson.redis.enable=true
# \u96C6\u7FA4\u65F6\uFF0C\u9700\u8981\u6240\u6709\u4E3B\u4ECE\u8282\u70B9\u5730\u5740
redisson.redis.servers=*************:6379,**************:6379
redisson.redis.password=adb@smart2021
# \u76D1\u63A7\u9501\u7684\u770B\u95E8\u72D7\u8D85\u65F6\uFF08\u5B95\u673A\u6216\u8FDB\u7A0B\u6302\u4E86\u91CA\u653E\u9501\u7684\u8D85\u65F6\u65F6\u95F4\uFF09\uFF0C\u5355\u4F4D\uFF1A\u6BEB\u79D2\u3002\u9ED8\u8BA4\u503C\uFF1A30000
redisson.redis.lockWatchdogTimeout=20000
# \u96C6\u7FA4\u72B6\u6001\u626B\u63CF\u95F4\u9694\u65F6\u95F4\uFF0C\u5355\u4F4D\u662F\u6BEB\u79D2\u3002\u9ED8\u8BA4\u503C\uFF1A 1000
redisson.redis.scanInterval=1000
# \u591A\u4E3B\u8282\u70B9\u7684\u73AF\u5883\u91CC\uFF0C\u6BCF\u4E2A \u4E3B\u8282\u70B9\u7684\u8FDE\u63A5\u6C60\u6700\u5927\u5BB9\u91CF\u3002\u8FDE\u63A5\u6C60\u7684\u8FDE\u63A5\u6570\u91CF\u81EA\u52A8\u5F39\u6027\u4F38\u7F29\u3002\u9ED8\u8BA4\u503C\uFF1A64
redisson.redis.masterConnectionPoolSize=64
# \u591A\u4ECE\u8282\u70B9\u7684\u73AF\u5883\u91CC\uFF0C\u6BCF\u4E2A \u4ECE\u670D\u52A1\u8282\u70B9\u91CC\u7528\u4E8E\u666E\u901A\u64CD\u4F5C\uFF08\u975E \u53D1\u5E03\u548C\u8BA2\u9605\uFF09\u8FDE\u63A5\u7684\u8FDE\u63A5\u6C60\u6700\u5927\u5BB9\u91CF\u3002\u8FDE\u63A5\u6C60\u7684\u8FDE\u63A5\u6570\u91CF\u81EA\u52A8\u5F39\u6027\u4F38\u7F29\u3002\u9ED8\u8BA4\u503C\uFF1A64
redisson.redis.slaveConnectionPoolSize=64
#\u662F\u5426\u542F\u7528AMQ(true,false)
mp.component.amqOpen=false
# MQ\u7C7B\u578B 1: RabbitMQ 2:ActiveMQ 3: RocketMQ (\u9ED8\u8BA4\u4F7F\u75281:RabbitMQ, \u5982\u679C\u6CA1\u6709\u8BBE\u7F6EamqType, \u4E3A\u517C\u5BB9\u4E4B\u524D\u7248\u672C\u4F7F\u7528ActiveMQ)
mp.component.amqType=1
mp.component.amqUrl=10.170.250.250
# MQ\u7AEF\u53E3\uFF0C\u53EA\u5BF9RabbitMQ\u6709\u7528
mp.component.amqPort=5672
mp.component.amqUser=root
mp.component.amqPwd=adb@smart2021
#\u961F\u5217\uFF0C\u4EE5\u201C\u961F\u5217\u952E:\u961F\u5217\u540D:\u961F\u5217\u6570\u91CF;\u961F\u5217\u952E:\u961F\u5217\u540D:\u961F\u5217\u6570\u91CF\u201D\u4E3A\u683C\u5F0F\uFF0C\u961F\u5217\u6570\u91CF\u672A\u914D\u65F6\uFF0C\u9ED8\u8BA4\u4E3A1\uFF08\u6CE8\uFF1A\u961F\u5217\u952E\u4E0E\u4EE3\u7801\u7ED1\u5B9A\uFF0C\u786E\u5B9A\u540E\u4E0D\u80FD\u4FEE\u6539\uFF09
mp.component.amqQueue=logs.bss.queue.key:logs.bss.queue:1;logs.invoking.queue.key:logs.invoking.queue:1;logs.run.queue.key:logs.run.queue
#\u6839\u636E\u4F7F\u7528\u4E0D\u540C\u7684\u6570\u636E\u5E93\uFF0C\u626B\u63CF\u4E0D\u5230\u7684DAL\u5305,\u591A\u4E2A\u4EE5","\u9017\u53F7\u5206\u9694;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver
write.mp.jdbc.packagescan=com.ly.mp.**.mysql
#mp\u7684\u6570\u636E\u5E93(\u4E3B\u5E93)\u4E8B\u52A1\u7B56\u7565 \u666E\u901A: normal \u591A\u5E93(jta) : jta  \u591A\u670D\u52A1:tcc
write.mp.jdbc.transactionPolicy=gtsx
#\u6CA1\u6709\u914D\u7F6E\u8FD9\u914D\u7F6E\u9879\u4E0D\u5F71\u54CD\u5347\u7EA7,\u4F5C\u7528\u662F\u8FD4\u56DE\u7684\u9ED8\u8BA4\u96C6\u5408\u67E5\u8BE2\u7ED3\u679C\u8F6C\u6210\u5927\u5199.\u6CA1\u6709\u914D\u7F6E\u6216\u8005\u9ED8\u8BA4\u662Ftrue:key\u8F6C\u6210\u5927\u5199,false:key\u4E0D\u4F5C\u8F6C\u6362
write.mp.jdbc.upperCaseColumn=true
# url,username,password\u53EF\u4EE5\u8FDB\u884C\u52A0\u5BC6\uFF0C\u4F7F\u7528\u5BC6\u6587
write.mp.jdbc.name=mp_write
write.mp.jdbc.url=********************************************************************************************************************************************************
write.mp.jdbc.username=adpuser
write.mp.jdbc.password=Adp@smart2024
other.write.mp.jdbc.name[0]=base
other.write.mp.jdbc.url[0]=*******************************************************************************************************************************************
other.write.mp.jdbc.username[0]=adpuser
other.write.mp.jdbc.password[0]=Adp@smart2024
other.write.mp.jdbc.name[1]=tidb
other.write.mp.jdbc.url[1]=**************************************************************************************************************************************************
other.write.mp.jdbc.username[1]=adpuser
other.write.mp.jdbc.password[1]=Adp@smart2024
#read.jdbc.name[mp_write#1]=default_mp_read
#read.jdbc.url[mp_write#1]=********************************************
#read.jdbc.username[mp_write#1]=mp24
#read.jdbc.password[mp_write#1]=mp24
mp.read.db.size=0
#druid datasource
#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE
druid.initialSize=3
druid.minIdle=3
druid.maxActive=20
druid.maxWait=60000
druid.timeBetweenEvictionRunsMillis=60000
druid.minEvictableIdleTimeMillis=300000
druid.validationQuery=select 1 from dual
druid.testWhileIdle=true
druid.testOnBorrow=false
druid.testOnReturn=false
druid.poolPreparedStatements=false
druid.maxPoolPreparedStatementPerConnectionSize=20
#druid.keepAlive=true
druid.phyTimeoutMillis=1200000
#wall,slf4j,stat
druid.filters=stat
#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3
#mp2.24\u589E\u91CF\u914D\u7F6E
#mybatis
mybatis-plus.mapperLocations=classpath:/mapper/*Mapper.xml
#\u5B9E\u4F53\u626B\u63CF\uFF0C\u591A\u4E2Apackage\u7528\u9017\u53F7\u6216\u8005\u5206\u53F7\u5206\u9694
mybatis-plus.typeAliasesPackage=com.ly.mp.meta.dev.entities
mybatis-plus.typeEnumsPackage=com.ly.mp.meta.dev.entities.enums
#\u6570\u636E\u5E93\u76F8\u5173\u914D\u7F6E
#\u4E3B\u952E\u7C7B\u578B  AUTO:"\u6570\u636E\u5E93ID\u81EA\u589E", INPUT:"\u7528\u6237\u8F93\u5165ID",ID_WORKER:"\u5168\u5C40\u552F\u4E00ID (\u6570\u5B57\u7C7B\u578B\u552F\u4E00ID)", UUID:"\u5168\u5C40\u552F\u4E00ID UUID";
mybatis-plus.global-config.db-config.id-type=UUID
#\u5B57\u6BB5\u7B56\u7565 IGNORED:"\u5FFD\u7565\u5224\u65AD",NOT_NULL:"\u975E NULL \u5224\u65AD"),NOT_EMPTY:"\u975E\u7A7A\u5224\u65AD"
mybatis-plus.global-config.db-config.field-strategy=not_empty
#\u9A7C\u5CF0\u4E0B\u5212\u7EBF\u8F6C\u6362
mybatis-plus.global-config.db-config.column-underline=true
#\u6570\u636E\u5E93\u5927\u5199\u4E0B\u5212\u7EBF\u8F6C\u6362
#capital-mode: true
#\u903B\u8F91\u5220\u9664\u914D\u7F6E
mybatis-plus.global-config.db-config.logic-delete-value=0
mybatis-plus.global-config.db-config.logic-not-delete-value=1
#mybatis-plus.global-config.db-config.db-type=sqlserver
#\u5237\u65B0mapper \u8C03\u8BD5\u795E\u5668
mybatis-plus.global-config.refresh=true
# \u539F\u751F\u914D\u7F6E
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# tda \u914D\u7F6E
refer.tda.url=http://tda-uat.smart.cn
refer.tda.token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************.gvoRLtH_jbItAKSpLagaVFnOW760fgKfXSJSsFL8SW4
refer.tda.offset=10
refer.tda.page=1
refer.tda.menu=%2Fsystem%2Faccount%2Fuser