<configuration scan="true" scanPeriod="10 seconds">  
    <!-- 日志文件存放目录 -->
 <property name="log.base" value="/mplogs/${service.name}" />
 
  <!-- 传输给logstash -->
  <!-- <appender name="stash"
	class="net.logstash.logback.appender.LogstashTcpSocketAppender">
	<destination>192.168.31.156:9100</destination>
	<includeCallerData>true</includeCallerData>

	<encoder class="net.logstash.logback.encoder.LogstashEncoder">
		<includeCallerData>true</includeCallerData>
	</encoder>
  </appender> -->
 
  <!-- Simple file output -->  
  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">  
    <!-- encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->  
    <encoder>  
        <pattern>  
            [ %-5level] [%date{yyyy-MM-dd HH:mm:ss.SSS}][%thread]  %logger{96} [%line] - %msg%n  
        </pattern>  
        <charset>UTF-8</charset> <!-- 此处设置字符集 -->  
    </encoder>  
  
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">  
      <!-- rollover daily 配置日志所生成的目录以及生成文件名的规则 -->  
      <fileNamePattern>${log.base}/mylog-%d{yyyy-MM-dd}.%i.log</fileNamePattern> 
      <!-- 保存指定天数的历史日志 -->
      <maxHistory>1</maxHistory>
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
      <timeBasedFileNamingAndTriggeringPolicy  
          class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">  
        <!-- or whenever the file size reaches 64 MB -->  
        <maxFileSize>4 MB</maxFileSize>  
      </timeBasedFileNamingAndTriggeringPolicy>  
    </rollingPolicy>  
  
  
    <!-- <filter class="ch.qos.logback.classic.filter.ThresholdFilter">  
      <level>INFO</level>  
    </filter>   -->
    <!-- Safely log to the same file from multiple JVMs. Degrades performance! -->  
    <prudent>true</prudent>  
  </appender>  
  
  <!-- Simple file output -->  
  <appender name="SEATAFILE" class="ch.qos.logback.core.rolling.RollingFileAppender">  
    <!-- encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->  
    <encoder>  
        <pattern>  
            [ %-5level] [%date{yyyy-MM-dd HH:mm:ss.SSS}][%thread]  %logger{96} [%line] - %msg%n
        </pattern>  
        <charset>UTF-8</charset> <!-- 此处设置字符集 -->  
    </encoder>  
  
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">  
      <!-- rollover daily 配置日志所生成的目录以及生成文件名的规则 -->  
      <fileNamePattern>${log.base}/seata-%d{yyyy-MM-dd}.%i.log</fileNamePattern> 
      <!-- 保存指定天数的历史日志 -->
      <maxHistory>15</maxHistory>
      <cleanHistoryOnStart>true</cleanHistoryOnStart>
      <timeBasedFileNamingAndTriggeringPolicy  
          class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">  
        <!-- or whenever the file size reaches 64 MB -->  
        <maxFileSize>20 MB</maxFileSize>  
      </timeBasedFileNamingAndTriggeringPolicy>  
    </rollingPolicy>  

    <prudent>true</prudent>  
  </appender>  
  
  
  <!-- Console output -->  
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">  
    <!-- encoder defaults to ch.qos.logback.classic.encoder.PatternLayoutEncoder -->  
      <encoder>  
          <pattern>  
              [ %-5level] [%date{yyyy-MM-dd HH:mm:ss.SSS}][%thread]  %logger{96} [%line] - %msg%n  
          </pattern>  
          <charset>UTF-8</charset> <!-- 此处设置字符集 -->  
      </encoder>  
    <!-- Only log level INFO and above -->  
  <!--   <filter class="ch.qos.logback.classic.filter.ThresholdFilter">  
      <level>INFO</level>  
    </filter>  --> 
  </appender>  
  
  <!-- sql输出到文件 -->
	<appender name="SQL_FILEOUT"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.base}/sql-%d{yyyy-MM-dd}-%i.txt
			</fileNamePattern>
			<!-- 保存指定天数的历史日志 -->
      		<maxHistory>15</maxHistory>
      		<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>10MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%date - %msg%n
			</pattern>
		</encoder>
	</appender>
	<appender name="SLOW_SQL_FILEOUT"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.base}/slow_sql-%d{yyyy-MM-dd}-%i.txt
			</fileNamePattern>
			<!-- 保存指定天数的历史日志 -->
      		<maxHistory>15</maxHistory>
      		<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>10MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%date - %msg%n
			</pattern>
		</encoder>
	</appender>
	
	<appender name="MYBATIS_SQL_FILEOUT"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.base}/mybatis-sql-%d{yyyy-MM-dd}-%i.txt
			</fileNamePattern>
			<!-- 保存指定天数的历史日志 -->
      		<maxHistory>15</maxHistory>
      		<cleanHistoryOnStart>true</cleanHistoryOnStart>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>10MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
		<encoder>
			<pattern>%date [%thread] - %msg%n
			</pattern>
		</encoder>
	</appender>
	
	
	<logger name="com.baomidou" level="DEBUG" additivity="false">
	    <appender-ref ref="MYBATIS_SQL_FILEOUT" />
        <appender-ref ref="STDOUT"/>
    </logger>
    
    <logger name="com.ly.mp.mybatis.idal" level="DEBUG" additivity="false">
        <appender-ref ref="MYBATIS_SQL_FILEOUT" />
        <appender-ref ref="STDOUT"/>
    </logger>
	
	<logger name="druid.sql" level="DEBUG" additivity="false">
		<appender-ref ref="SQL_FILEOUT" />
		<appender-ref ref="STDOUT" />
	</logger>
	<logger name="com.alibaba.druid.filter.stat.StatFilter" level="INFO" additivity="false">
		<appender-ref ref="SLOW_SQL_FILEOUT" />
		<appender-ref ref="STDOUT" />
	</logger>
	
    <logger name="org.springframework" level="INFO" additivity="false">
		<appender-ref ref="FILE" />  
    	<appender-ref ref="STDOUT" />
	</logger>
	
   <logger name="io.seata" level="INFO" additivity="false">
        <appender-ref ref="SEATAFILE" />
        <appender-ref ref="STDOUT" />
    </logger>
  
  <!-- Enable FILE and STDOUT appenders for all log messages.  
       By default, only log at level INFO and above. -->  
  <root level="INFO">
    <appender-ref ref="FILE" />  
    <appender-ref ref="STDOUT" />  
    <!-- <appender-ref ref="stash" /> -->
  </root>  
  
  
</configuration>  