<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
        http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd
      ">

    <cache:annotation-driven cache-manager="springcacheManager" error-handler="cacheError"/>
    <bean id="cacheError" class="com.ly.mp.cache.springcache.error.SpringCacheErrorHandler"/>
    <bean class="com.ly.mp.component.helper.SpringContextHolder" lazy-init="false" />
    <bean class="com.ly.mp.conf.cache.CacheConfiguration" lazy-init="false" />
    <!-- <bean class="com.ly.mp.component.check.MpHeartbeatConfig" lazy-init="false" /> -->
     <!-- 扫描日志功能 -->
	<context:component-scan base-package="com.ly.mp.component.log" />
	<!-- 扫描分布式锁功能 -->
	<context:component-scan base-package="com.ly.mp.cache.lock" />
	
	<!-- 熔断功能 -->
	<context:component-scan base-package="com.ly.mp.component.hystrix" />
	
	<!-- 监控注入 -->
	<context:component-scan base-package="com.ly.mp.component.trace.aspect.consumer" />
	<context:component-scan base-package="com.ly.mp.component.trace.collector" />

    <!-- for redis -->
    <!--集群配置-->
     <!--bean id="springcacheManager" class="com.ly.mp.cache.springcache.jedis.JedisClusterCacheManager" primary="true">
        <property name="namedClients">
            <map>
                <entry key="default" value="${redis.servers}" />
            </map>
        </property>
        <property name="cacheStoreJedisHashRouter">
            <bean class="com.ly.mp.cache.springcache.CacheStoreJedisHashRouter" />
        </property>
        <property name="serializer">
            <bean class="com.ly.mp.cache.common.JsonSerializer" />
        </property>
        <property name="password" value="${redis.password}" />
        <property name="sessionTimeout" value="${session.timeout}" />
        <property name="timeout" value="${redis.pool.timeout}" />
        <property name="expires" value="${redis.pool.expires}" />
        <property name="maxActive" value="${redis.pool.maxActive}" />
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}" />
    </bean-->
    
    <!--单机配置-->
    <!-- <bean id="springcacheManager" class="com.ly.mp.cache.springcache.jedis.JedisCacheManager" primary="true">
        <property name="namedClients">
            <map>
                <entry key="default" value="${redis.servers}" />
            </map>
        </property>
        <property name="cacheStoreJedisHashRouter">
            <bean class="com.ly.mp.cache.springcache.CacheStoreJedisHashRouter" />
        </property>
        <property name="serializer">
            <bean class="com.ly.mp.cache.common.JsonSerializer" />
        </property>
        <property name="password" value="${redis.password}" />
        <property name="sessionTimeout" value="${session.timeout}" />
        <property name="timeout" value="${redis.pool.timeout}" />
        <property name="expires" value="${redis.pool.expires}" />
        <property name="maxActive" value="${redis.pool.maxActive}" />
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}" />
    </bean>
    
    <bean id="sessionManager" class="com.ly.mp.cache.springcache.jedis.JedisCacheManager">
        <property name="namedClients">
            <map>
                <entry key="default" value="${redis.servers}" />
            </map>
        </property>
        <property name="cacheStoreJedisHashRouter">
            <bean class="com.ly.mp.cache.springcache.CacheStoreJedisHashRouter" />
        </property>
        <property name="serializer">
            <bean class="com.ly.mp.cache.common.JsonSerializer" />
        </property>
        <property name="password" value="${redis.session.password}" />
        <property name="sessionTimeout" value="${session.timeout}" />
        <property name="timeout" value="${redis.pool.timeout}" />
        <property name="expires" value="${redis.pool.expires}" />
        <property name="maxActive" value="${redis.pool.maxActive}" />
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}" />
    </bean> -->
    

</beans>