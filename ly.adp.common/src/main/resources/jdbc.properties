#æ ¹æ®ä½¿ç¨ä¸åçæ°æ®åºï¼æ«æä¸å°çDALå,å¤ä¸ªä»¥","éå·åé;com.ly.mp.**.oracle,com.ly.mp.**.mysql,com.ly.mp.**.sqlserver
write.mp.jdbc.packagescan = com.ly.mp.**.mysql
#mpçæ°æ®åº(ä¸»åº)äºå¡ç­ç¥ æ®é: normal å¤åº(jta) : jta  å¤æå¡:tcc
write.mp.jdbc.transactionPolicy = normal

#æ²¡æéç½®è¿éç½®é¡¹ä¸å½±ååçº§,ä½ç¨æ¯è¿åçé»è®¤éåæ¥è¯¢ç»æè½¬æå¤§å.æ²¡æéç½®æèé»è®¤æ¯true:keyè½¬æå¤§å,false:keyä¸ä½è½¬æ¢
write.mp.jdbc.upperCaseColumn = true

#write.mp.jdbc.name = mp_write
#write.mp.jdbc.url = ********************************************
#write.mp.jdbc.username = mp232
#write.mp.jdbc.password = mp232

#write.mp.jdbc.name=oracle_mp
#write.mp.jdbc.url=******************************************
#write.mp.jdbc.username=mpjava
#write.mp.jdbc.password=mpjava

#write.mp.jdbc.name=sqlserver_mp
#write.mp.jdbc.url = ************************************************
#write.mp.jdbc.username = sa
#write.mp.jdbc.password = mp2.0@lanyou

write.mp.jdbc.name=mysql_mp
write.mp.jdbc.url=****************************************************************************
write.mp.jdbc.username=mysqladminX
write.mp.jdbc.password=MPadminX

#write.mp.jdbc.name=sqlserver_mp
#write.mp.jdbc.url=*********************************************
#write.mp.jdbc.username=mp23
#write.mp.jdbc.password=mp23

#write.mp.jdbc.name=mysql_mp
#write.mp.jdbc.url=***************************************************************************
#write.mp.jdbc.username=mp22
#write.mp.jdbc.password=mp22

#write.mp.jdbc.name=mysql_mp
#write.mp.jdbc.url=****************************************************************************
#write.mp.jdbc.username=mysqladmin
#write.mp.jdbc.password=MPadmin

#write.mp.jdbc.name = mp_write
#write.mp.jdbc.url = ****************************************************************************
#write.mp.jdbc.username = mp23
#write.mp.jdbc.password = mp23



#write.mp.jdbc.name = mp_write
#write.mp.jdbc.url = *********************************************
#write.mp.jdbc.username=MP3
#write.mp.jdbc.password=MP3

#other.write.mp.jdbc.name[1] = oracle_mp_write1
#other.write.mp.jdbc.url[1] = ******************************************************************************
#other.write.mp.jdbc.username[1] = mpjava2
#other.write.mp.jdbc.password[1] = mpjava2

#other.write.mp.jdbc.name[2] = oracle_mp_write2
#other.write.mp.jdbc.url[2] = ***************************************************************************
#other.write.mp.jdbc.username[2] = mp21
#other.write.mp.jdbc.password[2] = mp21

#read.jdbc.name[mp_write#1] = default_mp_read
#read.jdbc.url[mp_write#1] = ***************************************************************************
#read.jdbc.username[mp_write#1] = mp21
#read.jdbc.password[mp_write#1] = mp21

#read.jdbc.name[mp_write#2] = default_mp_read
#read.jdbc.url[mp_write#2] = ***************************************************************************
#read.jdbc.username[mp_write#2] = mp21
#read.jdbc.password[mp_write#2] = mp21

# url,username,passwordå¯ä»¥è¿è¡å å¯ï¼ä½¿ç¨å¯æ
#read.jdbc.name[mp_write#1] = default_mp_read
#read.jdbc.url[oracle_mp_write1#1] = 85E43B9E164192554BBDFE29B941DD3D1F5CBDFDF477F44342F1F3A5755EA5904E32C366CEEE5AB138F7AE6053D6C7AE16BDB72C5E276A0FE4D41CE65BF794B2C46E01E11BA9252872778D2EC33FF89A
#read.jdbc.username[oracle_mp_write1#1] = 87F273D89CC0839A98487D8EBC41328C29807F09DAB3907E
#read.jdbc.password[oracle_mp_write1#1] = 87F273D89CC0839A98487D8EBC41328C128BC50B6775E8F1D327D7E01AAA0512
#write.mp.jdbc.name=oracle_mp
#write.mp.jdbc.url=********************************************
#write.mp.jdbc.username=mp20
#write.mp.jdbc.password=mp20

#write.mp.jdbc.name=mpjava
#write.mp.jdbc.url=*****************************************************************************
#write.mp.jdbc.username=mpjava
#write.mp.jdbc.password=mpjava

mp.read.db.size = 0

#druid datasource
#https://github.com/alibaba/druid/wiki/%E9%85%8D%E7%BD%AE_DruidDataSource%E5%8F%82%E8%80%83%E9%85%8D%E7%BD%AE
druid.initialSize=3
druid.minIdle=3
druid.maxActive=20
druid.maxWait=60000
druid.timeBetweenEvictionRunsMillis=60000
druid.minEvictableIdleTimeMillis=300000
druid.validationQuery=select 1 from dual
druid.testWhileIdle=true
druid.testOnBorrow=false
druid.testOnReturn=false
druid.poolPreparedStatements=false
druid.maxPoolPreparedStatementPerConnectionSize=20
#druid.keepAlive = true
druid.phyTimeoutMillis = 1200000
#wall,slf4j,stat
druid.filters=stat
#druid.connectionProperties=druid.stat.logSlowSql=true;druid.stat.slowSqlMillis=3


#mp2.24å¢ééç½®
#mybatis
mybatis-plus.mapperLocations=classpath:/mapper/*Mapper.xml
#å®ä½æ«æï¼å¤ä¸ªpackageç¨éå·æèåå·åé
mybatis-plus.typeAliasesPackage=com.ly.mp.test.entity
mybatis-plus.typeEnumsPackage=com.ly.mp.test.entity.enums
#æ°æ®åºç¸å³éç½®
#ä¸»é®ç±»å  AUTO:"æ°æ®åºIDèªå¢", INPUT:"ç¨æ·è¾å¥ID",ID_WORKER:"å¨å±å¯ä¸ID (æ°å­ç±»åå¯ä¸ID)", UUID:"å¨å±å¯ä¸ID UUID";
mybatis-plus.global-config.db-config.id-type=UUID
#å­æ®µç­ç¥ IGNORED:"å¿½ç¥å¤æ­",NOT_NULL:"é NULL å¤æ­"),NOT_EMPTY:"éç©ºå¤æ­"
mybatis-plus.global-config.db-config.field-strategy=not_empty
#é©¼å³°ä¸åçº¿è½¬æ¢
mybatis-plus.global-config.db-config.column-underline=true
#æ°æ®åºå¤§åä¸åçº¿è½¬æ¢
#capital-mode: true
#é»è¾å é¤éç½®
mybatis-plus.global-config.db-config.logic-delete-value=0
mybatis-plus.global-config.db-config.logic-not-delete-value= 1
#mybatis-plus.global-config.db-config.db-type= sqlserver
#å·æ°mapper è°è¯ç¥å¨
mybatis-plus.global-config.refresh=true
# åçéç½®
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.cache-enabled=false
