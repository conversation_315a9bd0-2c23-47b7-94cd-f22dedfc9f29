<?xml version="1.0" encoding="UTF-8"?> 
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jee="http://www.springframework.org/schema/jee" xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:mvc="http://www.springframework.org/schema/mvc" 
	xmlns:cache="http://www.springframework.org/schema/cache"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:mp="http://www.szlanyou.com/schema/mp"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
	http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee.xsd
	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
	http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd
	http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
	http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc.xsd
	http://www.szlanyou.com/schema/mp http://www.szlanyou.com/schema/mp.xsd" 
	default-lazy-init="true">
	<!-- 自定义属性配置文件路径 -->
	<!--
	<bean id="propertyConfigurer" class="com.ly.mp.dal.comm.jdbc.PropertyPlaceholderConfigurerExt">  
			<property name="fileEncoding" value="utf-8" /> 
            <property name="locations">
                <list>
                    <value>classpath:jdbc.properties</value>
					<value>classpath:ly-mp-cache.properties</value>
					<value>classpath:ly-mp-other.properties</value>
                </list>
            </property>
    </bean> 
    -->
    <bean id="mpProperties" class="com.ly.mp.component.conf.MpProperties"></bean>
     <bean id="mpAlipayQrProperties" class="com.ly.mp.component.conf.MpAlipayQrProperties"></bean>
     <bean id="mpAlipayAppProperties" class="com.ly.mp.component.conf.MpAlipayAppProperties"></bean>
     <bean id="mpWeixinQrProperties" class="com.ly.mp.component.conf.MpWeixinQrProperties"></bean>
     <bean id="mpWeixinAppProperties" class="com.ly.mp.component.conf.MpWeixinAppProperties"></bean>
    
	<!-- 自动注入oracle的business bean -->
	<context:component-scan base-package="com.ly.mp.**.biz" />
	<context:component-scan base-package="com.ly.mp.**.service.impl" />
	<context:component-scan base-package="com.ly.mp.dal.comm.config" />
	<context:component-scan base-package="com.ly.mp.dal.comm.mybatis" />
	
	<!-- 事务管理器驱动 -->
	<context:annotation-config/>
	<tx:annotation-driven transaction-manager="transactionManager" proxy-target-class="true" />
	
	<!-- 监控注入 -->
	<context:component-scan base-package="com.ly.mp.component.trace.aspect.provider" />
	<aop:aspectj-autoproxy proxy-target-class="true" />
	
	<!-- wfAction主要是注入服务类 -->  
    <context:component-scan base-package="com.ly.mp.wfengine.biz.action" name-generator="com.ly.mp.component.spring.WfAnnotationBeanNameGenerator" />  
	
    <import resource="classpath:ly-mp-cache-appcontext.xml"/>
    <!--读写分离-->
    <!-- <import resource="classpath:ly-mp-readdb.xml"/> -->
    <!--异步线程-->
    <import resource="classpath:ly-mp-task.xml"/>
    
</beans>