spring.cloud.nacos.config.enabled=false
spring.cloud.nacos.discovery.enabled=false
management.health.redis.enabled=false
# nacoså°å
spring.cloud.nacos.discovery.server-addr=172.26.223.227:8848
spring.cloud.nacos.config.server-addr=172.26.223.227:8848
spring.cloud.nacos.config.file-extension=properties
# å½åç©ºé´
spring.cloud.nacos.discovery.namespace=prj1
spring.cloud.nacos.config.namespace=prj1
spring.cloud.nacos.discovery.username=nacos
spring.cloud.nacos.discovery.password=1
spring.cloud.nacos.config.username=nacos
spring.cloud.nacos.config.password=1
spring.cloud.nacos.config.context-path=/nacos
# å±äº«çéç½®
spring.cloud.nacos.config.shared-dataids=application-cloud.properties
spring.cloud.nacos.config.refreshable-dataids=application-cloud.properties
management.endpoints.web.exposure.include=*
# http headerçsize
server.max-http-header-size=1024000