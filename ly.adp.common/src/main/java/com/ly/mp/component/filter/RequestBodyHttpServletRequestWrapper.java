package com.ly.mp.component.filter;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;
import com.ly.mp.component.conf.MpProperties;
import com.ly.mp.component.helper.JsonUtils;
import com.ly.mp.component.helper.SignHelper;
import com.ly.mp.component.helper.SpringContextHolder;
import com.ly.mp.component.helper.StringHelper;

/**
 * RequestBody过滤处理
 */
public class RequestBodyHttpServletRequestWrapper extends HttpServletRequestWrapper {

    private boolean xssEnable;
    private boolean hasXss;
    Map<String, Object> mapExclude = null;
    String strExpression = null;

    private boolean signEnable;
    private boolean signResult;
    Map<String, Object> mapSignExclude = null;
    String strSignSecret = null;

    private byte[] requestBody;
    private Charset charSet;

    // 读取了输入流
    private boolean isRead;

    private static Logger logger = LoggerFactory.getLogger(RequestBodyHttpServletRequestWrapper.class);

    /**
     * @param request
     */
    public RequestBodyHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
        // 缓存请求body
        try {
            String contentType = StringHelper.TranSeachString(request.getContentType()).toLowerCase();
            if (!contentType.contains("application/json")) {
                requestBody = new byte[0];
                return;
            }

            String requestBodyData = getRequestPostData(request);
            isRead = true;

            if (!StringHelper.IsEmptyOrNull(requestBodyData)) {
                Map<String, Object> paramMap = null;
                try {
                    if (requestBodyData.startsWith("{")) {
                        paramMap = JsonUtils.nonDefaultMapper().fromJson(requestBodyData, Map.class);
                    } else if (requestBodyData.startsWith("[")) {
                        List<Object> vals = JsonUtils.nonDefaultMapper().fromJson(requestBodyData, List.class);
                        if (vals != null) {
                            paramMap = new HashMap<String, Object>();
                            int idx = 0;
                            for (Object val : vals) {
                                paramMap.put(String.valueOf(idx++), val);
                            }
                        }
                    }
                } catch (Exception ex) {
                    logger.error("解析requestBody出错!" + ex.getMessage() + ". content : " + requestBodyData);
                }

                if (paramMap == null) {
                    paramMap = new HashMap<String, Object>();
                }

                MpProperties mpproc = SpringContextHolder.getBean(MpProperties.class);

                String requestUri = request.getRequestURI();
                String checkUri = getCheckUri(requestUri);

                // 判断是否开启拦截XSS
                if ("true".equalsIgnoreCase(StringHelper.TranString(mpproc.getXssprotect().getEnable()))) {
                    this.xssEnable = true;
                    // 判断是否在排除地址之外
                    Map<String, Object> exclude = getExclude(mpproc);
                    if (!exclude.containsKey(checkUri)) {
                        // 检查xss
                        for (Map.Entry<String, Object> entry : paramMap.entrySet()) {
                            Object val = entry.getValue();
                            if (!StringHelper.IsEmptyOrNull(val)) {
                                String expression = getExpression(mpproc);
                                Pattern p = Pattern.compile(expression);
                                Matcher m = p.matcher(val.toString());
                                if (m.find()) {
                                    logger.error("{0}'s parameter {1} ({2}) contain dangerous word.",
                                            request.getRequestURI(), entry.getKey(), val.toString());

                                    this.setHasXss(true);
                                    break;
                                }
                            }
                        }
                    }
                }

                // 判断是否开启拦截Sign
                if ("true".equalsIgnoreCase(StringHelper.TranString(mpproc.getSignprotect().getEnable()))) {
                    this.signEnable = true;
                    // 判断是否在排除地址之外
                    Map<String, Object> exclude = getSignExclude(mpproc);
                    if (!exclude.containsKey(checkUri)) {
                        // 检查Sign
                        String originSign = request.getParameter("sign");
                        if (StringHelper.IsEmptyOrNull(originSign) && paramMap.get("sign") != null) {
                            originSign = paramMap.get("sign").toString();
                        }
                        // 排除sign和token参数
                        paramMap.remove("sign");
                        paramMap.remove("token");

                        this.setSignReslt(SignHelper.checkSign(paramMap, getSignSecret(mpproc), originSign));
                    }
                }

                requestBody = requestBodyData.getBytes(charSet);
            } else {
                requestBody = new byte[0];
            }
        } catch (IOException e) {
            logger.error("requestBody处理出错!", e);
        }
    }

    public String getRequestPostData(HttpServletRequest request) throws IOException {
        String charSetStr = request.getCharacterEncoding();
        if (charSetStr == null) {
            charSetStr = "UTF-8";
        }
        charSet = Charset.forName(charSetStr);

        return StreamUtils.copyToString(request.getInputStream(), charSet);
    }

    public boolean getXssEnable() {
        return xssEnable;
    }

    public boolean getSignEnable() {
        return signEnable;
    }

    public boolean getSignResult() {
        return signResult;
    }

    public void setSignReslt(boolean signResult) {
        this.signResult = signResult;
    }

    public boolean isHasXss() {
        return hasXss;
    }

    public void setHasXss(boolean hasXss) {
        this.hasXss = hasXss;
    }

    private String getSignSecret(MpProperties mpproc) {
        if (this.strSignSecret != null) {
            return strSignSecret;
        }
        this.strSignSecret = mpproc.getSignprotect().getSecret().trim();
        if (!StringHelper.IsEmptyOrNull(strSignSecret)) {
            return this.strSignSecret;
        } else {
            // 默认签名密钥
            this.strSignSecret = "Iv3RlGKyxrCDfu3a";
            return this.strSignSecret;
        }
    }

    private String getExpression(MpProperties mpproc) {
        if (this.strExpression != null) {
            return strExpression;
        }
        this.strExpression = mpproc.getXssprotect().getExpression().trim();
        if (!StringHelper.IsEmptyOrNull(strExpression)) {
            return this.strExpression;
        } else {
            // 加入默认(<(iframe|script|body|img|layer|div|meta|style|base|object|input))
            this.strExpression = "(<(iframe|script|body|img|layer|div|meta|style|base|object|input))";
            return this.strExpression;
        }
    }

    private Map<String, Object> getSignExclude(MpProperties mpproc) {
        if (this.mapSignExclude != null) {
            return mapSignExclude;
        }
        mapSignExclude = new HashMap<>();
        String excludeUrl = mpproc.getSignprotect().getExclude();
        if (excludeUrl == null || excludeUrl.isEmpty()) {
            return mapSignExclude;
        }
        String[] arr = excludeUrl.split(",");
        if (arr != null) {
            for (String strExclude : arr) {
                String putStr = getCheckUri(strExclude).trim();
                mapSignExclude.put(putStr, true);
            }
        }
        return this.mapSignExclude;
    }

    private Map<String, Object> getExclude(MpProperties mpproc) {
        if (this.mapExclude != null) {
            return mapExclude;
        }
        mapExclude = new HashMap<>();
        String excludeUrl = mpproc.getXssprotect().getExclude();
        if (excludeUrl == null || excludeUrl.isEmpty()) {
            return mapExclude;
        }
        String[] arr = excludeUrl.split(",");
        if (arr != null) {
            for (String strExclude : arr) {
                String putStr = getCheckUri(strExclude).trim();
                mapExclude.put(putStr, true);
            }
        }
        return this.mapExclude;
    }

    private String getCheckUri(String requestUri) {
        // 去除/
        String str = requestUri.toLowerCase();
        if (str.startsWith("/")) {
            str = str.substring(1);
        }
        return str;
    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        // 没读取inputstream, 返回原始的inputstream
        if (!isRead) {
            return super.getInputStream();
        }

        if (requestBody == null) {
            requestBody = new byte[0];
        }

        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(requestBody);

        return new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }

            @Override
            public boolean isReady() {
                return false;
            }

            @Override
            public void setReadListener(ReadListener readListener) {
            }

            @Override
            public int read() {
                return byteArrayInputStream.read();
            }
        };
    }
}