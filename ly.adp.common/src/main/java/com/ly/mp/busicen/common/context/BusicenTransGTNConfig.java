package com.ly.mp.busicen.common.context;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class BusicenTransGTNConfig {
	
	@ConditionalOnProperty(name = "write.mp.jdbc.transactionPolicy",havingValue = "normal",matchIfMissing = false)
	@Bean
	public BusicenTransGTNAspect busicenTransGTNAspect() {
		return new BusicenTransGTNAspect();
	}

}
