package com.ly.mp.busicen.common.excel;

import java.util.List;
import java.util.Map;

/**
 * 
 * <AUTHOR>
 *
 */
public class ExcelDataBuilder {

	public ExcelData parent = new ExcelData();
	
	public static ExcelDataBuilder create() {
		return new ExcelDataBuilder();
	}
	
	public ExcelDataBuilder title(String title) {
		this.parent.setTitle(title);
		return this;
	}
	
	public ExcelDataBuilder columns(String[][] columns) {
		this.parent.setColumns(columns);
		return this;
	}
	
	public ExcelDataBuilder data(List<Map<String, Object>> data) {
		this.parent.setData(data);
		return this;
	}
	
	public ExcelData build() {
		return parent;
	}
	
}
