package com.ly.mp.busicen.common.controller;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.ly.mp.busicen.common.constant.ErrorEnum;
import com.ly.mp.busicen.common.handler.ResultHandler;

/**
 * 
* @Description: TIDB数据源切换
* @version: v1.0.0
* @author: ly-tianh
* @date: 2019年7月22日 下午9:12:09 
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年7月22日     ly-tianh           v1.0.0               修改原因
 */
@Aspect
@Component
public class DbSwitch {
	private static final Logger log = LoggerFactory.getLogger(DbSwitch.class);
	
	@Pointcut("execution(* com.ly.mp.busicen.*.biz.*.getTIDB*(..))")
	public void queryPoincut() {
		
	}
	
	@Around("queryPoincut()")
	public Object aroundInUpdateService(ProceedingJoinPoint joinPoint) {
		//ReadWriteDataSourceDecision.markWrite("tidb");
		log.error("切换数据源");
		try {
			Object result = joinPoint.proceed();			
			return result;
		} catch (Throwable e) {
			return ResultHandler.queryError(ErrorEnum.SYS_ERROR.getResult(),ErrorEnum.SYS_ERROR.getMsg());
		}
		finally {
			//ReadWriteDataSourceDecision.reset();
		}
	}
}
