package com.ly.mp.busicen.common.dal.mybatis;

import java.lang.reflect.Field;

import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

public class BusicenMbExt {

	public static <T> QueryWrapper<T> buildQueryWrapper(T param) {
		if (param == null) {
			return null;
		}
		QueryWrapper<T> qw = new QueryWrapper<T>();
		Class<?> clazz = param.getClass();
		Field[] fields = clazz.getDeclaredFields();
		for (Field field : fields) {
			TableId tableId = field.getAnnotation(TableId.class);
			if (tableId != null) {
				String columnName = tableId.value();
				buildWrapper("", columnName, field, param, qw);
			}

			TableField tableField = field.getAnnotation(TableField.class);
			if (tableField != null) {
				String columnName = tableField.value();
				String condition = tableField.condition();
				buildWrapper(condition, columnName, field, param, qw);
			}
		}

		return qw;

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	static void buildWrapper(String condition, String columnName, Field field, Object param, QueryWrapper qw) {
		try {
//			field.setAccessible(true);
			Object valueObject = field.get(param);
			if (!StringUtils.isEmpty(valueObject)) {
				if (SqlCondition.LIKE.equals(condition)) {
					qw.like(columnName, valueObject);
				} else {
					qw.eq(columnName, valueObject);
				}
			}
		} catch (IllegalArgumentException | IllegalAccessException e) {

		}
	}
}
