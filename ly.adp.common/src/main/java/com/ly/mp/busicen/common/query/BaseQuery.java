package com.ly.mp.busicen.common.query;



import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.mp.busicen.common.entity.BaseEntity;

import java.io.Serializable;
import java.util.List;

/**
* <AUTHOR>
* @version 创建时间 ：2018年8月24日 上午9:58:55
*/
public abstract class BaseQuery<T extends BaseEntity> extends QueryWrapper<T> implements Serializable {

	private static final long serialVersionUID = 2359012587669471409L;
	
	private int pageIndex = 1;     //当前页

	private int pageSize = 10;     //页大小

	//private String sortfield;   //排序字段
	private List<OrderItem> sortfields; //排序字段集合
	private String sort = "desc";    //desc asc
	
	
	/** 可用标记 1-可用，0-已删除    -1-查询全部数据 */
	private String isEnable = "1";

	public abstract QueryWrapper<T> init();

	public BaseQuery() {

    }
	
	public Page<T> getPage() {
		Page<T> page =  new Page<>(pageIndex, pageSize);
		page.setOrders(sortfields);
//		boolean isAsc = false;
//		if("asc".equalsIgnoreCase(sort)) {
//			isAsc = true;
//		}
//		page.setAsc(sortfields);
		return page;
	}

	public int getPageIndex() {
		return pageIndex;
	}

	public void setPageIndex(int pageIndex) {
		this.pageIndex = pageIndex;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}

//	public String getSortfield() {
//		return sortfield;
//	}
//
//	public void setSortfield(String sortfield) {
//		this.sortfield = sortfield;
//	}
	

	public List<OrderItem> getSortfields() {
		return sortfields;
	}

	public void setSortfields(List<OrderItem> sortfields) {
		this.sortfields = sortfields;
	}
	public String getSort() {
		return sort;
	}


	public void setSort(String sort) {
		this.sort = sort;
	}

	public String getIsEnable() {
		return isEnable;
	}

	public void setIsEnable(String isEnable) {
		this.isEnable = isEnable;
	}

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
	}
}
