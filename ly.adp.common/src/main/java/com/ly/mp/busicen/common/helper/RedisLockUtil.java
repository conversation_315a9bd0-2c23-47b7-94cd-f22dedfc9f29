package com.ly.mp.busicen.common.helper;

import java.nio.charset.Charset;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class RedisLockUtil {
	private final static Logger logger = LoggerFactory.getLogger(RedisLockUtil.class);

	@Autowired
	private StringRedisTemplate redisTemplate;

	private static String LOCK_PREFIX = "REDIS_LOCK";
	
	/**
	 * 加锁脚本，原子操作，lua脚本
	 */
	private static String LOCK_LUA_SCRIPT = "";
	
	/**
	 * 释放锁脚本，原子操作，lua脚本
	 */
	private static String UNLOCK_LUA_SCRIPT = "";

	/**
	 * 默认过期时间(5s)
	 */
	private static final int DEFAULT_EXPIRE = 5;

	static {
		//加锁脚本
		StringBuilder sb1 = new StringBuilder();
		//setnx 如果key不存在则添加值并返回1，如果已经存在key则返回0
		//使用业务setnx(key,业务流水号)当加锁成功返回1时设置过期时间，避免业务异常没有解锁时防止死锁
		sb1.append("if redis.call(\"setnx\", KEYS[1], ARGV[1]) == 1 then ");
		sb1.append("  redis.call(\"expire\", KEYS[1], tonumber(ARGV[2])) ");
		sb1.append("  return 1 ");
		sb1.append("else ");
		sb1.append("  return 0 ");
		sb1.append("end ");
		
		LOCK_LUA_SCRIPT = sb1.toString();
		
		// -- 解锁脚本
		StringBuilder sb2 = new StringBuilder();
		sb2.append("if redis.call(\"get\",KEYS[1]) == ARGV[1] then ");
		sb2.append("    return redis.call(\"del\",KEYS[1]) ");
		sb2.append("else ");
		sb2.append("    return -1 ");
		sb2.append("end ");
		UNLOCK_LUA_SCRIPT = sb2.toString();
	}

	/**
	 * 获取分布式锁，原子操作
	 * 
	 * @param lockKey 锁
	 * @param lockValue 唯一ID, 可以使用UUID.randomUUID().toString();
	 * @return 是否枷锁成功
	 */
	public boolean lock(String lockKey, String lockValue) {
		return lock(lockKey, lockValue, DEFAULT_EXPIRE);
	}

	/**
	 * 获取分布式锁(原子操作)
	 * 
	 * @param lockKey 锁
	 * @param lockValue 唯一ID, 可以使用UUID.randomUUID().toString();
	 * @param expire 过期时间(单位:秒)
	 * @return 是否枷锁成功
	 */
	public boolean lock(String lockKey, String lockValue, long expire) {
		try {
			RedisCallback<Boolean> callback = (connection) -> {
				return connection.eval(LOCK_LUA_SCRIPT.getBytes(), ReturnType.BOOLEAN, 1,
						getKey(lockKey).getBytes(Charset.forName("UTF-8")),
						lockValue.getBytes(Charset.forName("UTF-8")),
						String.valueOf(expire).getBytes(Charset.forName("UTF-8")));
			};
			return (Boolean) redisTemplate.execute(callback);
		} catch (Exception e) {
			logger.error("redis lock error ,lock key: {}, value : {}, error info : {}", lockKey, lockValue, e);
		}
		return false;
	}

	/**
	 * 释放锁
	 * 
	 * @param lockKey 锁
	 * @param lockValue 唯一ID
	 * @return 执行结果
	 */
	public boolean unlock(String lockKey, String lockValue) {
		RedisCallback<Boolean> callback = (connection) -> {
			return connection.eval(UNLOCK_LUA_SCRIPT.getBytes(), ReturnType.BOOLEAN, 1,
					getKey(lockKey).getBytes(Charset.forName("UTF-8")), lockValue.getBytes(Charset.forName("UTF-8")));
		};
		return (Boolean) redisTemplate.execute(callback);
	}

	/**
	 * 获取Redis锁的value值
	 */
	public String get(String lockKey) {
		try {
			RedisCallback<String> callback = (connection) -> {
				return new String(connection.get(getKey(lockKey).getBytes()), Charset.forName("UTF-8"));
			};
			return (String) redisTemplate.execute(callback);
		} catch (Exception e) {
			logger.error("get redis value occurred an exception");
		}
		return null;
	}

	/**
	 * 生成加锁的唯一字符串
	 * 
	 * @return
	 */
	public String fetchLockValue() {
		return UUID.randomUUID().toString() + "-" + System.currentTimeMillis();
	}

	private String getKey(String lockKey) {
		return LOCK_PREFIX + "_" + lockKey;
	}
	
}