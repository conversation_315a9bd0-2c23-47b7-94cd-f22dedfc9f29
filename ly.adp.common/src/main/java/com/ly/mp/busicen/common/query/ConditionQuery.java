package com.ly.mp.busicen.common.query;


import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.ly.mp.busicen.common.entity.BaseEntity;
import com.ly.mp.busicen.common.helper.DateHelper;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> huangjun
 * @Description: TODO
 * @date Date : 2019/08/19
 */
public abstract class ConditionQuery<T extends BaseEntity> extends BaseQuery<T> {


    public Wrapper<T> likeIsNotEmpty(String column, String value) {
        if (!StringUtils.isEmpty(value)) {
            return like(column, value);
        }
        return this;
    }

    public Wrapper<T> eqIsNotEmpty(String column, Object params) {
        if (!StringUtils.isEmpty(params)) {
            return eq(column, params);
        }
        return this;
    }

    public Wrapper<T> inIsNotEmpty(String column, List<?> params) {
        if (!CollectionUtils.isEmpty(params)) {
            return in(column, params);
        }
        return this;
    }

    public Wrapper<T> inIsNotEmpty(String column, String params) {
        if (!StringUtils.isEmpty(params)) {
            return in(column, params);
        }
        return this;
    }

    public Wrapper<T> betweenIsNotEmpty(String column, Object start, Object end) {
        if (!StringUtils.isEmpty(start) && !StringUtils.isEmpty(end)) {
            if (start instanceof Date && end instanceof Date) {
                return between(column, DateHelper.getDateTimenSecondBegin((Date) start), DateHelper.getDateTimeSecondEnd((Date) end));
            }
            else {
                return between(column, start, end);
            }
        }
        return this;
    }

    public Wrapper<T> leIsNotEmpty(String column, Object param) {
        if (!StringUtils.isEmpty(param)) {
            if (param instanceof Date) {
                return le(column, DateHelper.getDateTimeSecondEnd((Date) param));
            }
            else {
                return le(column, param);
            }
        }
        return this;
    }

    public Wrapper<T> geIsNotEmpty(String column, Object param) {
        if (!StringUtils.isEmpty(param)) {
            if (param instanceof Date) {
                return ge(column, DateHelper.getDateTimenSecondBegin((Date) param));
            }
            else {
                return ge(column, param);
            }
        }
        return this;
    }
}
