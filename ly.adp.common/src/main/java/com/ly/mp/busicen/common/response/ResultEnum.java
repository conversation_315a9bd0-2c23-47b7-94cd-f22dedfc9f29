package com.ly.mp.busicen.common.response;

/**
 * @desc   结果对象
 * <AUTHOR>
 * @date   2018-7-25
 */
public enum ResultEnum {
	FAIL("-1", "系统出现错误，请刷新页面或联系管理员"),
	SUCCESS("1", "成功"),
	
	QUERY_SUCCESS("1", "查询成功"),
	ADD_SUCCESS("1", "添加成功"),
	EDIT_SUCCESS("1", "修改成功"),
	DEL_SUCCESS("1", "删除成功"),
	
	QUERY_FAIL("-1", "查询失败"),
	ADD_FAIL("-1", "添加失败"),
	EDIT_FAIL("-1", "修改失败"),
	DEL_FAIL("-1", "删除失败");
	
	private String code;
	private String msg;
	
	private ResultEnum(String status, String msg) {
		this.code = status;
		this.msg = msg;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}
}
