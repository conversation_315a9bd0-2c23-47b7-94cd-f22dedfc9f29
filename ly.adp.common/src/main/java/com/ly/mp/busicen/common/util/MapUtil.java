package com.ly.mp.busicen.common.util;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.util.LinkedMultiValueMap;

import com.ly.mp.component.helper.StringHelper;

import io.swagger.annotations.ApiModelProperty;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;

public class MapUtil {
	public enum convKeyType {
		HumpToUnderline, UnderLineToHump
	}
	public enum convExcelOrder {
		Sequence,Reverse
	}

	/**
	 * 复制指定字段的数据给另一字段
	 * @param map
	 * @param column1Array 取值的字段
	 * @param column2Array 需要赋值的字段
	 * @param isClean 是否清空原字段的值
	 * @return
	 */
	public static <T> T copyObjectValue(T map, String[] column1Array, String[] column2Array, boolean isClean){
		List<T> list = new ArrayList<>();
		list.add(map);
		List<T> result = ListUtil.copyObjectValue(list, column1Array, column2Array, isClean);
		return result.get(0);
	}

	/**
	 * 复制指定字段的数据给另一字段
	 * @param map
	 * @param column1Array 取值的字段
	 * @param column2Array 需要赋值的字段
	 * @param isClean 是否清空原字段的值
	 * @return
	 */
	public static Map<String, Object> copyObjectValueMap(Map<String, Object> map, String[] column1Array, String[] column2Array, boolean isClean){
		List<Map<String, Object>> list = new ArrayList<>();
		list.add(map);
		List<Map<String, Object>> result = ListUtil.copyObjectValueMap(list, column1Array, column2Array, isClean);
		return result.get(0);
	}

	/**
	 * Map key值转换
	 */
	public static<V> Map<String, V> convKey(Map<String, V> map,Map<String, String> keyMapping){
		return convKey(map,null,new HashMap<String, V>(),keyMapping);
	}

	/**
	 * Map key值转换
	 */
	public static<V> Map<String, V> convKey(Map<String, V> map,MapUtil.convKeyType convKeyType){
		return convKey(map,convKeyType,new HashMap<String, V>(),null);
	}

	/**
	 * Map key值转换
	 */
	public static<V> Map<String, V> convKey(Map<String, V> map,MapUtil.convKeyType convKeyType,Map<String, String> keyMapping){
		return convKey(map,convKeyType,new HashMap<String, V>(),keyMapping);
	}

	/**
	 * Map key值转换
	 */
	public static<V> Map<String, V> convKey(Map<String, V> map,MapUtil.convKeyType convKeyType,Map<String, V> newmap,Map<String, String> keyMapping){
		if (StringHelper.IsEmptyOrNull(map)) {
			return map;
		}
		if (StringHelper.IsEmptyOrNull(keyMapping)) {
			switch (convKeyType) {
			case UnderLineToHump:
				map.forEach((k,v)->{
					newmap.put(BusicenUtils.UnderLineToHump(k),v);
				});
				break;
			case HumpToUnderline:
				map.forEach((k,v)->{
					newmap.put(BusicenUtils.HumpToUnderline(k),v);
				});
				break;
			default:
				break;
			}
		}else {
			map.forEach((k,v)->{
				if (!StringHelper.IsEmptyOrNull(keyMapping.get(k))) {
					newmap.put(keyMapping.get(k),v);
				}
			});
		}
		return newmap;
	}

	/**
	 * 创建链接调用map
	 *
	 * @param <K> Key类型
	 * @param <V> Value类型
	 * @return map创建类
	 */
	public static <K, V> MapBuilder<K, V> builder() {
		return builder(new HashMap<K, V>());
	}

	/**
	 * 创建链接调用map
	 *
	 * @param <K> Key类型
	 * @param <V> Value类型
	 * @param map 实际使用的map
	 * @return map创建类
	 */
	public static <K, V> MapBuilder<K, V> builder(Map<K, V> map) {
		return new MapBuilder<>(map);
	}

	/**
	 * 创建链接调用map
	 *
	 * @param <K> Key类型
	 * @param <V> Value类型
	 * @param k key
	 * @param v value
	 * @return map创建类
	 */
	public static <K, V> MapBuilder<K, V> builder(K k, V v) {
		return (builder(new HashMap<K, V>())).put(k, v);
	}

	/**
	 * 去除Map集合中value为null或空字符串的值
	 *
	 * @param map
	 */
	public static Map<String, Object> removeNullValue(Map<String, Object> map) {
		Iterator<Entry<String, Object>> ite = map.entrySet().iterator();
		while (ite.hasNext()) {
			Map.Entry<String, Object> next = ite.next();
			if (StringHelper.IsEmptyOrNull(next.getValue())) {
				ite.remove();
			}
		}
		return map;
	}

	/**
	 * 生成一个实体，填充分页信息
	 * @return
	 */
	public static <T> T getBaseQueryCondition(Class<T> classz){
		return getBaseQueryCondition(classz,1,1);
	}

	/**
	 * 生成一个实体，填充分页信息
	 * @return
	 */
	public static <T> T getBaseQueryCondition(Class<T> classz,int pageIndex,int pageSize){
		try {
			if(pageIndex<=0) {
				pageIndex=1;
			}
			if(pageSize<=0) {
				pageSize=-1;
			}
			T t = classz.newInstance();
			// 设置父类的分页字段
			try {
				Class<? extends Object> tClass = t.getClass();
				Field field1 = tClass.getSuperclass().getDeclaredField("pageIndex");
				Field field2 = tClass.getSuperclass().getDeclaredField("pageSize");
//				field1.setAccessible(true);
//				field2.setAccessible(true);
//				field1.set(t, pageIndex);
//				field2.set(t, pageSize);
				field1.setAccessible(false);
				field2.setAccessible(false);
			} catch (Exception e) {
			}
			// 设置本类的分页字段
			try {
				Class<? extends Object> tClass = t.getClass();
				Field field1 = tClass.getDeclaredField("pageIndex");
				Field field2 = tClass.getDeclaredField("pageSize");
//				field1.setAccessible(true);
//				field2.setAccessible(true);
//				field1.set(t, pageIndex);
//				field2.set(t, pageSize);
				field1.setAccessible(false);
				field2.setAccessible(false);
			} catch (Exception e) {
			}
			return t;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 生成一个Map，填充分页信息
	 * @return
	 */
	public static Map<String, Object> getBaseQueryCondition(Map<String,Object> map){
		return getBaseQueryCondition(map,1,1);
	}

	/**
	 * 生成一个Map，填充分页信息
	 * @return
	 */
	public static Map<String, Object> getBaseQueryCondition(Map<String,Object> map,int pageIndex,int pageSize){
		try {
			if(pageIndex<=0) {
				pageIndex=1;
			}
			if(pageSize<=0) {
				pageSize=-1;
			}
			if (map==null) {
				map=new HashMap<>();
			}
			Map<String, Object> newmap = map;
			newmap.put("pageSize", pageSize);
			newmap.put("pageIndex", pageIndex);
			return newmap;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 生成一个Map，填充分页信息
	 * @return
	 */
	public static Map<String, Object> getBaseQueryCondition(int pageIndex,int pageSize){
		return getBaseQueryCondition(new HashMap<>(),pageIndex,pageSize);
	}

	/**
	 * 生成一个Map，填充分页信息
	 * @return
	 */
	public static Map<String, Object> getBaseQueryCondition(){
		return getBaseQueryCondition(1,1);
	}

	/**
	 * 根据关键字合并数据集
	 * <AUTHOR>
	 * @since 2019-06-12
	 * @param sourceList  合并的源数据集
	 * @param targetList  合并的目标数据集
	 * @param wordList  需要从目标数据集合并的字段
	 * @param key  数据集关联的关键字
	 * @return
	 */
	public static  boolean mergeMapList(List<Map<String,Object>> sourceList,List<Map<String,Object>> targetList,List<String> wordList,String key)
	{
		for(int i=0;i<sourceList.size();i++)
		{
			Map<String,Object> map = sourceList.get(i);
			if (!map.containsKey(key)) {
				return false;
			}
			String keySource = StringHelper.IsEmptyOrNull(map.get(key))?"":map.get(key).toString();
			boolean isFind=false;
			for(int j=0;j<targetList.size();j++)
			{
				Map<String,Object> mapTarget = targetList.get(j);
				String keyTarget = StringHelper.IsEmptyOrNull(mapTarget.get(key))?"":mapTarget.get(key).toString();
				if("".equals(keyTarget)) {
					continue;
				}
				if(keySource.equals(keyTarget))
				{
					isFind=true;
					for(int k=0;k<wordList.size();k++)
					{
						String sourceValue =StringHelper.IsEmptyOrNull(mapTarget.get(wordList.get(k)))?"":mapTarget.get(wordList.get(k)).toString();
						sourceList.get(i).put(wordList.get(k).toString(),sourceValue);
					}
					break;
				}
			}
			if(!isFind){//若未找到匹配的数据，则默认加上空值
				for(int k=0;k<wordList.size();k++)
				{
					sourceList.get(i).put(wordList.get(k).toString(),"");
				}
			}
		}
		return true;
	}
	/**
	 * 判断是否有值 string空字符串为false
	 * @param map
	 * @param key
	 * @return
	 */
	public static <T> boolean hasKeyAndValue(Map<T, ?> map,T key) {
		if (!map.containsKey(key)) {
			return false;
		}
		if (map.get(key)==null) {
			return false;
		}
		if (map.get(key) instanceof String&&String.valueOf(map.get(key)).trim().isEmpty()) {
			return false;
		}
		return true;
	}

	/**
	 * 取值，没有值抛异常
	 * @param map
	 * @param key
	 * @param errmsg 异常信息
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T,U> U getValue4Error(Map<T, ?> map,T key,String errmsg) {
		if (!hasKeyAndValue(map, key)) {
			throw new RuntimeException(errmsg);
		}
		return (U) map.get(key);
	}

	/**
	 * 取值，没有值返回null
	 * @param map
	 * @param key
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T,U> U getValue4Null(Map<T, ?> map,T key) {
		if (!hasKeyAndValue(map, key)) {
			return null;
		}
		return (U) map.get(key);
	}

	/**
	 * 取值，没有值返回设定值
	 * @param map
	 * @param key
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T,U> U getValue4Value(Map<T, ?> map,T key,U value) {
		if (!hasKeyAndValue(map, key)) {
			return value;
		}
		return (U) map.get(key);
	}

	public static void main(String[] args) {
		Map<String, Object> test=new HashMap<>();
		test.put("aa", "a");
		System.out.println(hasKeyAndValue(test, "aa"));
	}

	@SuppressWarnings("unchecked")
	public static Map<String,Object> convertToMap(Object entity) {
		if (entity == null) {
			throw new NullPointerException("传入校验对象为空");
		}
		if (entity instanceof Map) {
			return (Map<String,Object>) entity;
		}
		return entityToMap(entity);
	}

	public static Map<String, Object> entityToMap(Object entity) {
		if (entity == null) {
			return null;
		}
		Map<String, Object> result = new HashMap<>();
		try {
			BeanInfo beanInfo = Introspector.getBeanInfo(entity.getClass());
			PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
			for (PropertyDescriptor m : propertyDescriptors) {
				String name = m.getName();
				Method method = m.getReadMethod();
				result.put(name, method.invoke(entity));
			}
		} catch (Exception e) {
		}
		return result;
	}

	/**
	 * JSON 转MAP
	 * @param jsonStr
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static Map<String, Object> parseJSON2Map(String jsonStr){
		Map<String, Object> map = new HashMap<>();
		try {
			JSONObject json = JSONObject.fromObject(jsonStr);
			for(Object k : json.keySet()){
				Object v = json.get(k);
				if(v instanceof JSONArray){
					List<Map<String, Object>> list = new ArrayList<>();
					Iterator<JSONObject> it = ((JSONArray)v).iterator();
					while(it.hasNext()){
						JSONObject json2 = it.next();
						list.add(parseJSON2Map(json2.toString()));
					}
					map.put(k.toString(), list);
				} else {
					map.put(k.toString(), v);
				}
			}
		} catch (Exception e) {
		}
		return map;
	}

	/**
	 * 	获取需要导出的字段 反射@ApiModelProperty实现
	 * 	中文名称取自的value
	 * 	排序取自position
	 */
	public static <T> String[][] getExcelHeader(Class<T> classz, MapUtil.convExcelOrder orderType) {
		try {
			if (classz == null) {
				return null;
			}
			List<Map<String, String>> exportColumnsList = new ArrayList<>();
			LinkedMultiValueMap<Integer, String> columnsOrder = new LinkedMultiValueMap<>();
			Map<String, String> columnsValue = new HashMap<>();
			Field[] fieldArray = classz.getDeclaredFields();
			for (Field f : fieldArray) {
//				f.setAccessible(true);
				ApiModelProperty apiModelProperty = f.getDeclaredAnnotation(ApiModelProperty.class);
				// 排除无中文名称与设置了隐藏的字段
				if (apiModelProperty == null || StringHelper.IsEmptyOrNull(apiModelProperty.value()) && !apiModelProperty.hidden()) {
					continue;
				}
				columnsOrder.add(apiModelProperty.position(), f.getName());
				columnsValue.put(f.getName(), apiModelProperty.value());
				f.setAccessible(false);
			}
			// 根据position排序 默认顺序
			Comparator<Entry<Integer, List<String>>> comparator = null;
			switch (orderType) {
			case Reverse:
				comparator = Map.Entry.<Integer, List<String>>comparingByKey().reversed();
				break;
			case Sequence:
				comparator = Map.Entry.<Integer, List<String>>comparingByKey();
				break;
			default:
				comparator = Map.Entry.<Integer, List<String>>comparingByKey();
				break;
			}
			columnsOrder.entrySet().stream().sorted(comparator).forEach((f) -> {
				List<String> orderKeyList = f.getValue();
				for (String key : orderKeyList) {
					exportColumnsList.add(MapUtil.<String, String>builder(key, columnsValue.get(key)).build());
				}
			});
			return ListUtil.listMapToStringArray(exportColumnsList);
		} catch (Exception e) {
			// e.printStackTrace();
			return null;
		}

	}

	/**
	 * 	获取需要导出的字段 反射@ApiModelProperty实现
	 * 	中文名称取自的value
	 * 	排序取自position
	 *	 顺序排序
	 */
	public static <T> String[][] getExcelHeader(Class<T> classz){
		return getExcelHeader(classz,MapUtil.convExcelOrder.Sequence);
	}

	/**
	 * 对象转json
	 * @param obj
	 * @return
	 */
	public static String Object2Json(Object obj){
		try{
			if(obj!=null){
				JsonConfig config = new JsonConfig();
				config.registerJsonValueProcessor(LocalDateTime.class,  new DateTimeJsonValueProcessor("yyyy-MM-dd HH:mm:ss"));
				JSONObject json = JSONObject.fromObject(obj,config);
				return json.toString();
			}
		} catch (Exception e) {
		}
		return null;


	}


	// 通过Map.entrySet()遍历遍历Map及将Map转化为二维数组
	public static String[][] mapToStringArray(Map<String,String> map){
		int c=0;
		String[][] columns = new String[map.size()][2];
		//通过Map.entrySet()遍历map的key和value
		for (Map.Entry<String, String> entry : map.entrySet()) {
			//System.out.println("key = " + entry.getKey() + " and value = " + entry.getValue());
			columns[c][0] = entry.getKey();
			columns[c][1] = entry.getValue();
			c++;
		}
		return columns;
	}


}
