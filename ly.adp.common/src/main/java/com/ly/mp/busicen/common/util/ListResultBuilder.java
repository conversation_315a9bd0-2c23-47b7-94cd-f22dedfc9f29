/**   
* @Description: 该函数的功能描述
*
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年8月23日 下午2:13:48 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年8月23日     ly-wyifan           v1.0.0               修改原因
*/
package com.ly.mp.busicen.common.util;


import java.util.List;

import com.ly.mp.component.entities.ListResult;


/**   
* @Description: 该函数的功能描述
*
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年8月23日 下午2:13:48 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年8月23日     ly-wyifan           v1.0.0               修改原因
*/
public class ListResultBuilder {
	private ListResult<?> parent = new ListResult<>();

	public static ListResultBuilder create() {		
		return new ListResultBuilder();
	}
	
	public static ListResultBuilder creatOk() {
		ListResultBuilder result = create();
		result.parent.setMsg("查询成功");
		result.parent.setResult("1");
		return result;
	}

	@SuppressWarnings("unchecked")
	public <T> ListResult<T> build() {
		return (ListResult<T>) parent;
	}

	public ListResultBuilder result(String result) {
		parent.setResult(result);
		return this;
	}

	public ListResultBuilder pageindex(Long pageindex) {
		parent.setPageindex(pageindex.intValue());
		return this;
	}

	public ListResultBuilder pages(Long pages) {
		parent.setPages(pages.intValue());
		return this;
	}

	public ListResultBuilder records(Long records) {
		parent.setRecords(records.intValue());
		return this;
	}

	public ListResultBuilder msg(String msg) {
		parent.setMsg(msg);
		return this;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public ListResultBuilder rows(List rows) {
		parent.setRows(rows);
		return this;
	}
	

}
