package com.ly.mp.busicen.common.util;

import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Map;

import org.springframework.util.StringUtils;

import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.helper.Reflections;

/**
 * 
* @Description:对象属性值转换类
*
* @version: v1.0.0
* @author: ly-tianh
* @date: 2019年7月6日 下午5:09:59 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年7月6日     ly-tianh           v1.0.0               修改原因
 */
public class ObjConvertToNull {
	private static final String FORMATER = "yyyy-MM-dd HH:mm:ss";
	private static final String FORMATER_T = "yyyy-MM-dd['T'HH:mm:ss[Z]]";
	/**
	 * 
	* Copyright: Copyright (c) 2019 ly-tianh
	* 
	* @Description: 修改对象属性值空字符串 为null
	* @version: v1.0.0
	* @author: ly-tianh
	* @date: 2019年7月6日 下午5:10:24 
	*
	* Modification History:
	* Date         Author          Version            Description
	*---------------------------------------------------------*
	* 2019年7月6日     ly-tianh           v1.0.0               修改原因
	 */
	public static <T> T convertObjAttrToNull(T obj) {
		if (obj == null) {
			return null;
		}
		try {
			
			Class<?> cls = obj.getClass();
			for (Field field : cls.getDeclaredFields()) {
//				field.setAccessible(true);
				if (field.getType().equals(String.class)) {
					Object temp = field.get(obj);
					if (temp != null && temp.toString().isEmpty()) {
//						field.set(obj, null);
					}
				}
				field.setAccessible(false);
			}
		} catch (Exception e) {

		}

		return obj;
	}
	
//	public static <T> T copyMapValueToObj(Map<String,Object>mapParam, T obj) {
//		if (obj == null) {
//			return null;
//		}
//		try {
//
//			Class<?> cls = obj.getClass();
//			for (Field field : cls.getDeclaredFields()) {
//				field.setAccessible(true);
//				if (field.getType().equals(String.class)) {
//					String fieldName=field.getName();
//				    Object value =mapParam.get(fieldName);
//					if (value instanceof String) {
//						if(mapParam.containsKey(fieldName))
//						{
//							field.set(obj, mapParam.get(fieldName));
//						}else if(mapParam.containsKey(fieldName.toUpperCase())) {
//							field.set(obj, mapParam.get(fieldName.toUpperCase()));
//						}else if(mapParam.containsKey(fieldName.toLowerCase())){
//							field.set(obj, mapParam.get(fieldName.toLowerCase()));
//						}
//					}
//					else if(value instanceof Long) {
//						if(mapParam.containsKey(fieldName))
//						{
//							field.set(obj, String.valueOf(value));
//						}else if(mapParam.containsKey(fieldName.toUpperCase())) {
//							field.set(obj, String.valueOf(value));
//						}else if(mapParam.containsKey(fieldName.toLowerCase())){
//							field.set(obj, String.valueOf(value));
//						}
//					}
//				}
//				else if(field.getType().equals(Integer.class)) {
//					String fieldName=field.getName();
//					if(mapParam.containsKey(fieldName))
//					{
//						field.set(obj, mapParam.get(fieldName));
//					}else if(mapParam.containsKey(fieldName.toUpperCase())) {
//						field.set(obj, mapParam.get(fieldName.toUpperCase()));
//					}else if(mapParam.containsKey(fieldName.toLowerCase())){
//						field.set(obj, mapParam.get(fieldName.toLowerCase()));
//					}
//				}
//				else if(field.getType().equals(LocalDateTime.class)) {
//					String fieldName=field.getName();
//					if(mapParam.get(fieldName)!=null)
//					{
//				    Object value =mapParam.get(fieldName);
//					LocalDateTime date =null;
//					if (value instanceof LocalDateTime) {
//						date = (LocalDateTime) value;
//					} else if (value instanceof String) {
//						if (StringUtils.hasText(value.toString())) {
//							try {
//								if (value.toString().contains("T")) {
//									date = LocalDateTime.parse(value.toString(),
//											DateTimeFormatter.ofPattern(FORMATER_T));
//								} else {
//									date = LocalDateTime.parse(value.toString(), DateTimeFormatter.ofPattern(FORMATER));
//								}
//
//							} catch (Exception e) {
//
//							}
//						}
//					} else if (value instanceof Long) {
//						try {
//							Instant instant = Instant.ofEpochMilli((Long) value);
//							ZoneId zone = ZoneId.systemDefault();
//							date = LocalDateTime.ofInstant(instant, zone);
//						} catch (Exception e) {
//
//						}
//					} else if (value instanceof Integer) {
//						try {
//							Long timestamp = ((Integer) value).longValue() * 1000;
//							Instant instant = Instant.ofEpochMilli(timestamp);
//							ZoneId zone = ZoneId.systemDefault();
//							date = LocalDateTime.ofInstant(instant, zone);
//						} catch (Exception e) {
//
//						}
//					} else if (value instanceof Timestamp) {
//						date = ((Timestamp) value).toLocalDateTime();
//					}
//					if(mapParam.containsKey(fieldName))
//					{
//						field.set(obj, date);
//					}else if(mapParam.containsKey(fieldName.toUpperCase())) {
//						field.set(obj, date);
//					}else if(mapParam.containsKey(fieldName.toLowerCase())){
//						field.set(obj, date);
//					}
//					}
//				}
//				field.setAccessible(false);
//			}
//		} catch (Exception e) {
//			throw BusicenException.create("数据转换失败");
//		}
//
//		return obj;
//	}
	
	/**
	 * 用知道字段复制一个新的obj
	 * @param obj 原实体对象
	 * @param fileds 字段列表
	 * @return
	 */
	public static <T> T copyObjFromFileds(T obj,String[] fileds) {
		
		if (obj == null) {
			return null;
		}
		
		T newObj = null; 
		try {
			newObj = (T) obj.getClass().newInstance();//实例化
			//赋值 只赋fileds数组中的字段
			for(int i=0;i<fileds.length;i++){
				try{
					Object filedvalue =  Reflections.getFieldValue(obj, fileds[i]);
					Reflections.setFieldValue(newObj, fileds[i], filedvalue);
				}catch(Exception ex){
					
				}
            }		
		} catch (Exception e) {

		}

		return newObj;
	}
}
