package com.ly.mp.busicen.common.entity;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 创建时间 ：2018年9月12日 上午10:33:22
 */
public class GlobalConstant {

    private GlobalConstant() {
    }
    //{"appkey":"lykj1212jkhsfhsoidf11322hkds","sign":"","data":{"endtime":"","starttime":"","type":"time"}}
    //{"appkey":"lykj1212jkhsfhsoidf11322hkds","sign":"","data":{"type":"traverorder","approvenolist":[""]}}

    public static final String URL_APPKEY="{\"appkey\":\"lykj1212jkhsfhsoidf11322hkds\",";
    //sign
    public static final String URL_SIGN="\"sign\":\"";
    //data
    public static final String URL_DATA="\",\"data\":";
    //结束时间(时间类型)
    public static final String URL_END_TIME="{\"endtime\":\"";
    //开始时间(时间类型)
    public static final String URL_START_TIME="\",\"starttime\":\"";
    //类型(时间)(时间类型)
    public static final String URL_TIME_TYPE="\",\"type\":\"time\"}}";
    //类型(订单)(订单类型)
    public static final String URL_ORDER_TYPE="{\"type\":\"traverorder\",";
    //订单(订单类型)
    public static final String URL_ORDER_NUM="\"approvenolist\":[\"";
    //结尾(订单类型)
    public static final String URL_JIE_WEI="\"]}}";


    //    {"traverorderno":"RES568840","product":"index","custinfo":{
//        "cusCode":"AUVGO001",
//        "emCode":"zhangwz"
//    }
//    }
    //订单号
    public static final String URL_BILL_NO="{\"traverorderno\":\"";
    //product类型
    public static final String URL_PRODUCT_TYPE="\",\"product\":\"index\",";
    //公司证件号
    public static final String URL_CUSCODE="\"custinfo\":{\"cusCode\":\"AUVGO001\",";
    //预订人用户名
    public static final String URL_EMCODE="\"emCode\":\"";
    //地址结尾
    public static final String URL_JIEWEI="\"}}";




    /**
     * 接口开关
     */
    public static final String OPEN = "1";

    /**
     * 附件存放在FTP的根目录
     */
    public static final String FTP_UPLOAD_ROOT_DIR = "attachment/";

    /**
     * 密级值列表类型码
     */
    public static final String SECRETLEVEL_LOOKUP_TYPE_CODE = "SECRETLEVEL";
    /**
     * 紧急值列表类型码
     */
    public static final String URGENT_LOOKUP_TYPE_CODE = "URGENT";
    /**
     * 公文类型值列表类型码
     */
    public static final String PAPERTYPE_LOOKUP_TYPE_CODE = "PAPERTYPE";

    /**
     * 接待信息来源值列表类型码
     */
    public static final String INFOSOURCE_LOOKUP_TYPE_CODE = "INFOSOURCE";
    /**
     * 接待分类值列表类型码
     */
    public static final String RECEIVECLASSIFY_LOOKUP_TYPE_CODE = "RECEIVECLASSIFY";
    /**
     * 接待分档值列表类型码
     */
    public static final String RECEIVESUB_LOOKUP_TYPE_CODE = "RECEIVESUB";
    /**
     * 审计密级列表类型码
     */
    public static final String SJMJ_LOOKUP_TYPE_CODE = "SJMJ";
    /**
     * 报告类别列表类型码
     */
    public static final String REPORT_LOOKUP_TYPE_CODE = "REPORT";


    /**
     * 发文稿纸单据前缀
     */
    public static final String FWGZ_BILL_PREFIX = "FWGZ";
    /**
     * 工作联络单据前缀
     */
    public static final String GZLL_BILL_PREFIX = "GZLL";
    /**
     * 签办单单据前缀
     */
    public static final String QBD_BILL_PREFIX = "QBD";
    /**
     * 印章使用申请单据前缀
     */
    public static final String YZSQ_BILL_PREFIX = "YZSQ";
    /**
     * 精品导入审批申请单据前缀
     */
    public static final String JPDR_BILL_PREFIX = "JPDR";
    /**
     * 费用报销申请单据前缀
     */
    public static final String FYBX_BILL_PREFIX = "FYBX";
    /**
     * 付款审批申请单据前缀
     */
    public static final String FKSP_BILL_PREFIX = "FKSP";
    /**
     * 用车使用申请单据前缀
     */
    public static final String YCSQ_BILL_PREFIX = "YCSQ";
    /**
     * 合同会审单据前缀
     */
    public static final String HTHS_BILL_PREFIX = "HTHS";
    /**
     * 请假申请单据前缀
     */
    public static final String QJSQ_BILL_PREFIX = "QJSQ";
    /**
     * 证照申请单据前缀
     */
    public static final String ZZSY_BILL_PREFIX = "ZZSY";
    /**
     * 月度考勤单据前缀
     */
    public static final String YDKQ_BILL_PREFIX = "YDKQ";
    /**
     * 出差审批单据前缀
     */
    public static final String CCSP_BILL_PREFIX = "CCSP";
    /**
     * 差旅报销单据前缀
     */
    public static final String CLBX_BILL_PREFIX = "CLBX";
    /**
     * 物品采购单据前缀
     */
    public static final String WPCG_BILL_PREFIX = "WPCG";
    /**
     * 物品领用单据前缀
     */
    public static final String WPLY_BILL_PREFIX = "WPLY";
    /**
     * 公务接待单据前缀
     */
    public static final String GWJD_BILL_PREFIX = "GWJD";
    /**
     * 公务接待单据前缀
     */
    public static final String NBSJ_BILL_PREFIX = "NBSJ";


    /**
     * 差旅明细信息来源-0:商旅,1:垫付
     */
    public static final int DFSL = 0;
    public static final int YGDF = 1;

    /**
     * 接口发送成功
     */
    public static final int SUCCESS = 1;
    /**
     * 接口发送失败
     */
    public static final int FAIL = 0;

    /**
     * 住宿标准
     */
    public static final String HOTEL = "1";
    /**
     * 工作出差补贴
     */
    public static final String WORK = "2";
    /**
     * 培训出差补贴
     */
    public static final String STUDY = "3";

    /**
     * 汽车
     */
    public static final String CAR = "1";
    /**
     * 火车
     */
    public static final String TRAIN = "2";
    /**
     * 飞机
     */
    public static final String FLIGHT = "3";

    /**
     * 单据状态：0-创建，未审批（对应工作流的草稿）
     */
    public static final int STATUS_CREATE = 0;
    /**
     * 审批中
     */
    public static final int STATUS_IN_AUDIT = 1;
    /**
     * 作废（对应工作流的作废、关闭）
     */
    public static final int STATUS_CANCLE = 2;
    /**
     * 审批通过
     */
    public static final int STATUS_AUDITED = 4;

    /**
     * 工作流状态：2-作废
     */
    public static final String WF_STATUS_ZUOFEI = "2";
    /**
     * 工作流状态：3-关闭
     */
    public static final String WF_STATUS_GUANBI = "3";

    /**
     * 工作日
     */
    public static final String CALENDAR_WORKDAY = "1";
    /**
     * 节假日
     */
    public static final String CALENDAR_HOILDAY = "2";
    /**
     * 法定节假日
     */
    public static final String CALENDAR_LEGAL_HOILDAY = "3";



    /**
     * @Description: 获取所有的状态集合
     * <AUTHOR> huangjun
     * @date Date : 2019/08/20
     */
    public static Map<Integer, String> getAllStatusName() {
        return STATUS_MAP;
    }

    /**
     * @param status 状态值
     * @Description: 获取指定状态值的状态名称
     * <AUTHOR> huangjun
     * @date Date : 2019/08/20
     */
    public static String getStatusName(Integer status) {
        return STATUS_MAP.get(status);
    }

    protected static final Map<Integer, String> STATUS_MAP = new HashMap<>();

    protected static final Map<String, String> TRIP_TRAFFIC_MAP = new HashMap<>();

    public static String getTrafficName(String traffic) {
        return TRIP_TRAFFIC_MAP.get(traffic);
    }

    protected static final Map<String, String> LICENSE_TYPE_NAME = new HashMap<>();

    public static String getLicenseName(String license) {
        return LICENSE_TYPE_NAME.get(license);
    }

    protected static final Map<String, String> CARD_TYPE_NAME = new HashMap<>();

    public static String getCardName(String card) {
        return CARD_TYPE_NAME.get(card);
    }

    protected static final Map<String, String> PRINT_TYPE_NAME = new HashMap<>();

    public static String getPrintName(String print) {
        return PRINT_TYPE_NAME.get(print);
    }

    protected static final Map<String, String> DELIVERABLESNAME = new HashMap<>();

    public static String getDeliverablesName(String value) {
        return DELIVERABLESNAME.get(value);
    }


    static {

        STATUS_MAP.put(-1, "全部");
        STATUS_MAP.put(STATUS_CREATE, "创建");
        STATUS_MAP.put(STATUS_IN_AUDIT, "审批中");
        STATUS_MAP.put(STATUS_CANCLE, "已作废");
        STATUS_MAP.put(STATUS_AUDITED, "审批通过");

        TRIP_TRAFFIC_MAP.put("-1", "全部");
        TRIP_TRAFFIC_MAP.put(CAR, "汽车");
        TRIP_TRAFFIC_MAP.put(TRAIN, "火车");
        TRIP_TRAFFIC_MAP.put(FLIGHT, "飞机");

        LICENSE_TYPE_NAME.put("-1", "全部");
        LICENSE_TYPE_NAME.put("0", "营业执照");
        LICENSE_TYPE_NAME.put("1", "集团登记证");
        LICENSE_TYPE_NAME.put("2", "领导个人身份证");
        LICENSE_TYPE_NAME.put("3", "其他证照");

        CARD_TYPE_NAME.put("-1", "全部");
        CARD_TYPE_NAME.put("0", "正本");
        CARD_TYPE_NAME.put("1", "副本");
        CARD_TYPE_NAME.put("2", "正本+副本");

        DELIVERABLESNAME.put("-1", "全部");
        DELIVERABLESNAME.put("0", "原件");
        DELIVERABLESNAME.put("1", "复印件");
        DELIVERABLESNAME.put("2", "扫描件");

        PRINT_TYPE_NAME.put("-1", "全部");
        PRINT_TYPE_NAME.put("1", "黑白");
        PRINT_TYPE_NAME.put("2", "彩照");
    }
}
