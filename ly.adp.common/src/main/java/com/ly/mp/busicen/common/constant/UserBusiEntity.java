package com.ly.mp.busicen.common.constant;

import java.io.Serializable;

public class UserBusiEntity implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String userID;
	private String userName;
	private String empID;//员工ID
	private String empName;//员工名称
	private String orgType;//组织类型 0厂商，1门店，2集团 3供应商
	private String orgID;//组织ID
	private String orgCode;//组织编码
	private String orgName;//组织名称
	private String dlrID;//专营店ID
	private String dlrName;//专营店名称
	private String dlrCode;//专营店编码
	private String parentDlrID;//父专营店ID
	private String parentDlrName;//父专营店名称
	private String parentDlrCode;//父专营店编码
	private String posID;//系统岗位Id 以逗号分隔
	private String posCode;//系统岗位编码  以逗号分隔
	private String oemID="1";
	private String oemCode="1";
	private String groupCode="1";
	private String groupID="1";
	private String brandCode="1";
	private String dlrStatus;//经销商建店状态
	private String dlrOrgType;//经销商类别
	private String belongFactoryCode;//所属主机厂编码
	private String belongFactoryId;//所属主机厂ID
	private String solutionId;
	private String companyID;//所属公司ID
	private String partId;
	private String mobile;
	private String stationName;
	private String stationId;
	private String empCode;
	private String dlrShortName;
	private String grayScale;//灰度
	
	
	public String getGrayScale() {
		return grayScale;
	}
	public void setGrayScale(String grayScale) {
		this.grayScale = grayScale;
	}
	public String getEmpCode() {
		return empCode;
	}
	public void setEmpCode(String empCode) {
		this.empCode = empCode;
	}
	public String getDlrShortName() {
		return dlrShortName;
	}
	public void setDlrShortName(String dlrShortName) {
		this.dlrShortName = dlrShortName;
	}
	public String getPartId() {
		return partId;
	}
	public void setPartId(String partId) {
		this.partId = partId;
	}
	
	public String getCompanyID() {
		return companyID;
	}
	public void setCompanyID(String companyID) {
		this.companyID = companyID;
	}
	public String getSolutionId() {
		return solutionId;
	}
	public void setSolutionId(String solutionId) {
		this.solutionId = solutionId;
	}
	public String getBelongFactoryId() {
		return belongFactoryId;
	}
	public void setBelongFactoryId(String belongFactoryId) {
		this.belongFactoryId = belongFactoryId;
	}
	public String getBelongFactoryCode() {
		return belongFactoryCode;
	}
	public void setBelongFactoryCode(String belongFactoryCode) {
		this.belongFactoryCode = belongFactoryCode;
	}
	public String getParentDlrID() {
		return parentDlrID;
	}
	public void setParentDlrID(String parentDlrID) {
		this.parentDlrID = parentDlrID;
	}
	public String getParentDlrName() {
		return parentDlrName;
	}
	public void setParentDlrName(String parentDlrName) {
		this.parentDlrName = parentDlrName;
	}
	public String getParentDlrCode() {
		return parentDlrCode;
	}
	public void setParentDlrCode(String parentDlrCode) {
		this.parentDlrCode = parentDlrCode;
	}
	public String getOrgName() {
		return orgName;
	}
	public void setOrgName(String orgName) {
		this.orgName = orgName;
	}
	public String getPosCode() {
		return posCode;
	}
	public void setPosCode(String posCode) {
		this.posCode = posCode;
	}
	public String getDlrStatus() {
		return dlrStatus;
	}
	public void setDlrStatus(String dlrStatus) {
		this.dlrStatus = dlrStatus;
	}
	public String getDlrOrgType() {
		return dlrOrgType;
	}
	public void setDlrOrgType(String dlrOrgType) {
		this.dlrOrgType = dlrOrgType;
	}
	public String getBrandCode() {
		return brandCode;
	}
	public void setBrandCode(String brandCode) {
		this.brandCode = brandCode;
	}
	public String getUserID() {
		return userID;
	}
	public void setUserID(String userID) {
		this.userID = userID;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getOemID() {
		return oemID;
	}
	public void setOemID(String oemID) {
		this.oemID = oemID;
	}
	public String getOemCode() {
		return oemCode;
	}
	public void setOemCode(String oemCode) {
		this.oemCode = oemCode;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public String getGroupID() {
		return groupID;
	}
	public void setGroupID(String groupID) {
		this.groupID = groupID;
	}
	public String getEmpID() {
		return empID;
	}
	public void setEmpID(String empID) {
		this.empID = empID;
	}
	public String getEmpName() {
		return empName;
	}
	public void setEmpName(String empName) {
		this.empName = empName;
	}
	public String getOrgType() {
		return orgType;
	}
	public void setOrgType(String orgType) {
		this.orgType = orgType;
	}
	public String getOrgID() {
		return orgID;
	}
	public void setOrgID(String orgID) {
		this.orgID = orgID;
	}
	public String getOrgCode() {
		return orgCode;
	}
	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}
	public String getDlrID() {
		return dlrID;
	}
	public void setDlrID(String dlrID) {
		this.dlrID = dlrID;
	}
	public String getDlrName() {
		return dlrName;
	}
	public void setDlrName(String dlrName) {
		this.dlrName = dlrName;
	}
	public String getDlrCode() {
		return dlrCode;
	}
	public void setDlrCode(String dlrCode) {
		this.dlrCode = dlrCode;
	}
	public String getPosID() {
		return posID;
	}
	public void setPosID(String posID) {
		this.posID = posID;
	}
	public String getMobile() {
		return mobile;
	}
	public void setMobile(String mobile) {
		this.mobile = mobile;
	}
	public String getStationName() {
		return stationName;
	}
	public void setStationName(String stationName) {
		this.stationName = stationName;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	
	
}
