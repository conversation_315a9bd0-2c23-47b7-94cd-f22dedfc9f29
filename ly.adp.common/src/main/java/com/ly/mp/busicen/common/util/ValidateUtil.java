package com.ly.mp.busicen.common.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.ly.mp.component.entities.OptResult;


/**
 * 字符串格式验证工具类
 * <AUTHOR>
 * @since 2019-05-23
 */
public class ValidateUtil {

	/**
	 * 手机号码格式是否正确
	 * <AUTHOR>
	 * @since 2019-05-23
	 * @param phone
	 * @return
	 */
	public static  OptResult isPhoneValid(String phone)
	{
		OptResult res =new OptResult();
	    String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$";
		    if (phone.length() != 11) {
		    	res.setMsg("手机号应为11位数");
		    	res.setResult("0");
		        return res;
		    } else {
		       Pattern p = Pattern.compile(regex);
		        Matcher m = p.matcher(phone);
		        boolean isMatch = m.matches();
		        if (!isMatch) {
			    	res.setMsg("请填入正确的手机号");
			    	res.setResult("0");
			        return res;
		        }
		        else{
			    	res.setResult("1");
			        return res;
		        }
		    }
	}
	
	public static  OptResult isMailValid(String mail)
	{
		OptResult res =new OptResult();
	    String regex = "[\\w\\.\\-]+@([\\w\\-]+\\.)+[\\w\\-]+";

		Pattern p = Pattern.compile(regex);
	    Matcher m = p.matcher(mail);
		boolean isMatch = m.matches();
		if (!isMatch) {
			res.setMsg("请填入正确的邮箱格式");
			res.setResult("0");
			return res;
		}
		else{
			res.setResult("1");
			return res;
		 }
	}
}
