package com.ly.mp.busicen.common.context;

import java.util.function.Supplier;

public interface IBusicenInvoke<T> {
	
	public static <T> IBusicenInvoke<T> invoker(){
		return new BusicenInvoker<T>();
	}

	IBusicenInvoke<T> defErr(T t);

	IBusicenInvoke<T> invoke(Supplier<T> supplier);

	IBusicenInvoke<T> optResult(BusicenInvokeConsumer<T> consumer);

	T result();

	@FunctionalInterface
	interface BusicenInvokeConsumer<T> {
		void accept(T t, Throwable expt);
	}

}
