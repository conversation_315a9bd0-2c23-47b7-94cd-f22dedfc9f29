package com.ly.mp.busicen.common.constant;

/**
 * 
 * <AUTHOR>
 * 整个异常编码长度
 * 1.异常类别：用一位定义
 *  0:系统异常
 *  1：业务异常
 * 
 * 2.业务中心：使用三位数字定义
 * 	001：客商中心
 * 	002：客服中心
 *  003：活动中心
 *  ...
 *  
 * 3：模块：比如客服中心的投诉、咨询、线索，用两位定义
 *  01：投诉
 *  02：咨询
 *  03：线索
 *  ...
 * 4：异常编码：自定义
 * 	20010001：用户未登录
 *	20010002：用户已登录
 *	····
 */
public enum ErrorEnum {
	
	NOT_LOGIN("20010001","用户未登录"),
	IS_LOGINED("20010002","用户已登录"),
	PARAM_ERROR("20010003","参数错误"),
	DATA_BASE_OPER_ERROR("20010004","数据库操作错误"),
	ACCESSERROR("20010005","权限错误"),
	DATA_CONCURRENT_Error("20010006","数据并发"),
	DATA_REPEAT("20010007","数据重复"),
	REPEAT_COMMIT("20010997","重复提交"),
	BUSS_RULE_ERROR("20010999","业务规则错误"),
	SYS_ERROR("00000","服务端异常"),
	UNKOWN_EXCEPTION("0","未知异常");
	
	 private String result;
	 private String msg;
	
	private ErrorEnum(String result, String msg) {
		this.result = result;
		this.msg = msg;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}
}
