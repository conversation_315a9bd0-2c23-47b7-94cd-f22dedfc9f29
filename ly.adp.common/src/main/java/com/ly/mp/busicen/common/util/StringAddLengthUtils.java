package com.ly.mp.busicen.common.util;

public class StringAddLengthUtils{
	
	public static String stringAddLength(String string) {
		String tail = string.substring(0,string.lastIndexOf('_')+1);
		String head = string.substring(string.lastIndexOf('_')+1,string.length());
		String stringAddOne = String.valueOf(Integer.valueOf(head) + 1);
		for (int i = 0; i < head.length(); i++) {
			if (stringAddOne.length() != head.length()) {
				stringAddOne = 0 + stringAddOne;
			}else {
				break;
			}
		}
		return tail+stringAddOne;
	}
}
