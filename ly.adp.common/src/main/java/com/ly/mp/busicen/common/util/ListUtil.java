package com.ly.mp.busicen.common.util;


import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.helper.StringHelper;

public class ListUtil {

    /**
     * 将List<Map>转为String二维数组
     * map中只能有一个键值对
     */
    public static String[][] listMapToStringArray(List<Map<String, String>> list) {
        String[][] array = new String[list.size()][2];
        for (int i = 0; i < list.size(); i++) {
            array[i][0] = list.get(i).entrySet().stream().map(m -> String.valueOf(m.getKey())).findAny().orElse(null);
            array[i][1] = list.get(i).entrySet().stream().map(m -> String.valueOf(m.getValue())).findAny().orElse(null);
        }
        return array;
    }

    /**
     * 判断ListResult是否成功
     *
     * @param <T>
     * @param listResult
     * @return
     */
    public static <T> boolean isListResultSuccess(ListResult<T> listResult) {
        if (!StringHelper.IsEmptyOrNull(listResult) && "1".equals(listResult.getResult())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 判断ListResult的rows是否为空
     *
     * @param <T>
     * @param listResult
     * @return
     */
    public static <T> boolean isListResultEmpty(ListResult<T> listResult) {
        if (StringHelper.IsEmptyOrNull(listResult) || StringHelper.IsEmptyOrNull(listResult.getRows())) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * ListResult分页
     *
     * @param <T>
     * @param list
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @return
     */
    public static <T> ListResult<T> splitListResult(ListResult<T> listResult, int pageIndex, int pageSize) {
        if (isListResultEmpty(listResult)) {
            return listResult;
        }
        List<T> splitList = splitList(listResult.getRows(), pageIndex, pageSize);
        listResult.setRecords(listResult.getRows().size());
        if (pageSize < 1) {
            listResult.setPages(1);
        } else {
            int pages = (int) Math.ceil((double) listResult.getRecords() / (double) pageSize);
            listResult.setPages(pages);
        }
        listResult.setPageindex(pageIndex);
        listResult.setRows(splitList);
        return listResult;
    }

    /**
     * List分页
     *
     * @param <T>
     * @param list
     * @param index   页码
     * @param partCut 分页大小
     * @return
     */
    public static <T> List<T> splitList(List<T> list, int index, int partCut) {
        if (partCut <= 0) {
            return list;
        }
        if (index <= 0) {
            index = 1;
        }
        if (list == null || list.isEmpty()) {
            return list;
        }
        List<T> result = new ArrayList<T>();
        int toIndex = ((index) * partCut) > list.size() ? list.size() : ((index) * partCut);
        if (!((index - 1) * partCut > toIndex)) {
            result.addAll(list.subList((index - 1) * partCut, toIndex));
        }
        return result;
    }

    /**
     * 整合数据（一对一关系）
     *
     * @param list    需要拼接数据的集合
     * @param list2   数据对应关系的集合
     * @param column1 关联字段(自动转为驼峰)
     * @param column2 拼接字段(自动转为驼峰)
     * @return
     */

    public static <T, M> List<T> mixList(List<T> list, List<M> list2, String[] column1Array, String[] column2Array) {
        for (int i = 0; i < column1Array.length; i++) {
            // 转驼峰
            String column1 = StrUtil.findProperty(column1Array[i]);
            String column2 = StrUtil.findProperty(column2Array[i]);
            for (T t : list) {
                try {
                    Class<? extends Object> tClass = t.getClass();
                    Field fGet = tClass.getDeclaredField(column1);
//					fGet.setAccessible(true);
                    Object fObj = fGet.get(t);
                    fGet.setAccessible(false);
                    if (!StringHelper.IsEmptyOrNull(fObj)) {
                        for (M m : list2) {
                            try {
                                // 关联字段
                                Class<? extends Object> mClass = m.getClass();
                                Field mGet = mClass.getDeclaredField(column1);
//								mGet.setAccessible(true);
                                Object mObj = mGet.get(m);
                                mGet.setAccessible(false);
                                // 拼接字段
                                Field mGet2 = mClass.getDeclaredField(column2);
//								mGet2.setAccessible(true);
                                Object mObj2 = mGet2.get(m);
                                mGet2.setAccessible(false);
                                if (!StringHelper.IsEmptyOrNull(mObj) && !StringHelper.IsEmptyOrNull(mObj2)) {
                                    if (String.valueOf(mObj).equals(String.valueOf(fObj))) {
                                        Field fSet = tClass.getDeclaredField(column2);
//										fSet.setAccessible(true);
//										fSet.set(t, mObj2);
                                        fSet.setAccessible(false);
                                        break;
                                    }
                                }
                            } catch (Exception e) {
                                // e.printStackTrace();
                            }
                        }
                    }
                } catch (Exception e) {
                    // e.printStackTrace();
                }
            }
        }
        return list;
    }

    /**
     * 整合数据（一对一关系）
     *
     * @param list    需要拼接数据的集合
     * @param list2   数据对应关系的集合(泛型为Map)
     * @param column1 关联字段(自动转为驼峰)
     * @param column2 拼接字段(自动转为驼峰)
     * @return
     */

    public static <T> List<T> mixListMap(List<T> list, List<Map<String, Object>> list2, String[] column1Array, String[] column2Array) {
        for (int i = 0; i < column1Array.length; i++) {
            // 转驼峰
            String column1 = StrUtil.findProperty(column1Array[i]);
            String column2 = StrUtil.findProperty(column2Array[i]);
            for (T t : list) {
                try {
                    Class<? extends Object> tClass = t.getClass();
                    Field fGet = tClass.getDeclaredField(column1);
//					fGet.setAccessible(true);
                    Object fObj = fGet.get(t);
                    fGet.setAccessible(false);
                    if (!StringHelper.IsEmptyOrNull(fObj)) {
                        for (Map<String, Object> map : list2) {
                            if (!StringHelper.IsEmptyOrNull(map.get(column1))) {
                                if (String.valueOf(map.get(column1)).equals(String.valueOf(fObj))) {
                                    if (!StringHelper.IsEmptyOrNull(map.get(column2))) {
                                        Field fSet = tClass.getDeclaredField(column2);
//										fSet.setAccessible(true);
//										fSet.set(t, map.get(column2));
                                        fSet.setAccessible(false);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return list;
    }

    /**
     * 整合数据（一对一关系）
     *
     * @param list    需要拼接数据的集合(泛型为Map)
     * @param list2   数据对应关系的集合(泛型为Map)
     * @param column1 关联字段(自动转为驼峰)
     * @param column2 拼接字段(自动转为驼峰)
     * @return
     */

    public static List<Map<String, Object>> mixListMap2(List<Map<String, Object>> list, List<Map<String, Object>> list2, String[] column1Array, String[] column2Array) {
        for (int i = 0; i < column1Array.length; i++) {
            // 转驼峰
            String column1 = StrUtil.findProperty(column1Array[i]);
            String column2 = StrUtil.findProperty(column2Array[i]);
            for (Map<String, Object> m : list) {
                try {
                    Object obj = m.get(column1);
                    if (!StringHelper.IsEmptyOrNull(obj)) {
                        for (Map<String, Object> map : list2) {
                            if (!StringHelper.IsEmptyOrNull(map.get(column1))) {
                                if (String.valueOf(map.get(column1)).equals(String.valueOf(obj))) {
                                    if (!StringHelper.IsEmptyOrNull(map.get(column2))) {
                                        m.put(column2, map.get(column2));
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    // e.printStackTrace();
                }
            }
        }
        return list;
    }


    /**
     * 整合数据（一对一关系）
     *
     * @param list    需要拼接数据的集合
     * @param list2   数据对应关系的集合(泛型为Map)
     * @param column1 需要拼接数据的集合的key字段(自动转为驼峰)
     * @param column2 数据对应关系的集合的key字段 (自动转为驼峰)
     * @param column3 数据对应关系的集合的value字段(自动转为驼峰)
     * @return
     */

    public static <T> List<T> mixListMap(List<T> list, List<Map<String, Object>> list2, String[] column1Array, String[] column2Array, String[] column3Array) {
        for (int i = 0; i < column1Array.length; i++) {
            // 转驼峰
            String column1 = StrUtil.findProperty(column1Array[i]);
            String column2 = StrUtil.findProperty(column2Array[i]);
            String column3 = StrUtil.findProperty(column3Array[i]);
            for (T t : list) {
                try {
                    Class<? extends Object> tClass = t.getClass();
                    Field fGet = tClass.getDeclaredField(column1);
//					fGet.setAccessible(true);
                    Object fObj = fGet.get(t);
                    fGet.setAccessible(false);
                    if (!StringHelper.IsEmptyOrNull(fObj)) {
                        for (Map<String, Object> map : list2) {
                            if (!StringHelper.IsEmptyOrNull(map.get(column2))) {
                                if (String.valueOf(map.get(column2)).equals(String.valueOf(fObj))) {
                                    if (!StringHelper.IsEmptyOrNull(map.get(column3))) {
                                        Field fSet = tClass.getDeclaredField(column3);
//										fSet.setAccessible(true);
//										fSet.set(t, map.get(column3));
                                        fSet.setAccessible(false);
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return list;
    }


    /**
     * 整合数据（一对一关系）
     *
     * @param list  需要拼接数据的集合(泛型为Map)
     * @param list2 数据对应关系的集合(泛型为Map)
     * @return
     */

    public static List<Map<String, Object>> mixListMap2(List<Map<String, Object>> list, List<Map<String, Object>> list2, String[] column1Array, String[] column2Array, String[] column3Array) {
        for (int i = 0; i < column1Array.length; i++) {
            // 转驼峰
            String column1 = StrUtil.findProperty(column1Array[i]);
            String column2 = StrUtil.findProperty(column2Array[i]);
            String column3 = StrUtil.findProperty(column3Array[i]);
            for (Map<String, Object> m : list) {
                try {
                    Object obj = m.get(column1);
                    if (!StringHelper.IsEmptyOrNull(obj)) {
                        for (Map<String, Object> map : list2) {
                            if (!StringHelper.IsEmptyOrNull(map.get(column2))) {
                                if (String.valueOf(map.get(column2)).equals(String.valueOf(obj))) {
                                    if (!StringHelper.IsEmptyOrNull(map.get(column3))) {
                                        m.put(column3, map.get(column3));
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    // e.printStackTrace();
                }
            }
        }
        return list;
    }

    /**
     * 复制指定字段的数据给另一字段
     *
     * @param list
     * @param column1Array 取值的字段
     * @param column2Array 需要赋值的字段
     * @param isClean      是否清空原字段的值
     * @return
     */
    public static <T> List<T> copyObjectValue(List<T> list, String[] column1Array, String[] column2Array, boolean isClean) {
        for (int i = 0; i < column1Array.length; i++) {
            String column1 = column1Array[i];
            String column2 = column2Array[i];
            for (T t : list) {
                try {
                    // 取值的字段
                    Class<? extends Object> tClass = t.getClass();
                    Field f1 = tClass.getDeclaredField(column1);
                    PropertyDescriptor f1PD = new PropertyDescriptor(f1.getName(), tClass);
                    Object f1Obj = f1PD.getReadMethod().invoke(t);
                    // 赋值的字段
                    Field f2 = tClass.getDeclaredField(column2);
                    PropertyDescriptor f2PD = new PropertyDescriptor(f2.getName(), tClass);
                    f2PD.getWriteMethod().invoke(t, f1Obj);
                } catch (Exception e) {
                }
            }
        }
        if (isClean) {
            for (int i = 0; i < column1Array.length; i++) {
                String column1 = StrUtil.findProperty(column1Array[i]);
                for (T t : list) {
                    try {
                        // 取值的字段
                        Class<? extends Object> tClass = t.getClass();
                        Field f1 = tClass.getDeclaredField(column1);
                        PropertyDescriptor f1PD = new PropertyDescriptor(f1.getName(), tClass);
                        f1PD.getWriteMethod().invoke(t, (Object) null);
                    } catch (Exception e) {
                    }

                }
            }
        }
        return list;
    }

    /**
     * 复制指定字段的数据给另一字段(泛型为Map)
     *
     * @param list
     * @param column1Array 取值的字段
     * @param column2Array 需要赋值的字段
     * @param isClean      是否清空原字段的值
     * @return
     */
    public static List<Map<String, Object>> copyObjectValueMap(List<Map<String, Object>> list, String[] column1Array, String[] column2Array, boolean isClean) {
        for (int i = 0; i < column1Array.length; i++) {
            String column1 = column1Array[i];
            String column2 = column2Array[i];
            for (int j = 0; j < list.size(); j++) {
                Map<String, Object> map = list.get(j);
                map.put(column2, map.get(column1));
            }
        }
        if (isClean) {
            for (int i = 0; i < column1Array.length; i++) {
                String column1 = column1Array[i];
                for (int j = 0; j < list.size(); j++) {
                    Map<String, Object> map = list.get(j);
                    map.remove(column1);
                }
            }
        }
        return list;
    }

    /**
     * list查重
     *
     * @return 返回重复的值
     */
    public static Set<String> checkRepeatObject(List<Map<String, Object>> list, String key) {
        List<String> uniqueList = new ArrayList<String>();
        Set<String> errList = new HashSet<>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map = list.get(i);
            if (!uniqueList.contains(map.get(key))) {
                uniqueList.add((String) map.get(key));
            } else {
                errList.add((String) map.get(key));
            }
        }
        return errList;
    }

    /**
     * list查重
     *
     * @return 返回重复的值
     */
    public static Set<String> checkRepeat(List<Map<String, String>> list, String key) {
        List<String> uniqueList = new ArrayList<String>();
        Set<String> errList = new HashSet<>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            if (!uniqueList.contains(map.get(key))) {
                uniqueList.add(map.get(key));
            } else {
                errList.add(map.get(key));
            }
        }
        return errList;
    }

    /**
     * excel表头转换
     */
    public static List<Map<String, Object>> excelHeaderConv(List<Map<String, String>> list, Map<String, String> keyConv) {
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map<String, String> map : list) {
            Map<String, Object> resultMap = new HashMap<>();
            Iterator<String> ite = map.keySet().iterator();
            while (ite.hasNext()) {
                String next = ite.next();
                String newKey = keyConv.get(next);
                if (!StringHelper.IsEmptyOrNull(newKey)) {
                    resultMap.put(newKey, map.get(next));
                }
            }
            result.add(resultMap);
        }
        return result;
    }

    public static List<Map<String, String>> excelHeaderConvStr(List<Map<String, String>> list, Map<String, String> keyConv) {
        List<Map<String, String>> result = new ArrayList<>();
        for (Map<String, String> map : list) {
            Map<String, String> resultMap = new HashMap<>();
            Iterator<String> ite = map.keySet().iterator();
            while (ite.hasNext()) {
                String next = ite.next();
                String newKey = keyConv.get(next);
                if (!StringHelper.IsEmptyOrNull(newKey)) {
                    resultMap.put(newKey, map.get(next));
                }
            }
            result.add(resultMap);
        }
        return result;
    }
}
