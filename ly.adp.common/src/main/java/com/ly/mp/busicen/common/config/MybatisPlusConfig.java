///*
// * Copyright (c) 2011-2020, bao<PERSON><PERSON><PERSON> (<EMAIL>).
// * <p>
// * Licensed under the Apache License, Version 2.0 (the "License"); you may not
// * use this file except in compliance with the License. You may obtain a copy of
// * the License at
// * <p>
// * https://www.apache.org/licenses/LICENSE-2.0
// * <p>
// * Unless required by applicable law or agreed to in writing, software
// * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
// * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
// * License for the specific language governing permissions and limitations under
// * the License.
// */
//package com.ly.mp.busicen.common.config;
//
//
//
//import org.apache.ibatis.plugin.Interceptor;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mockito.internal.configuration.GlobalConfiguration;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//
//import com.baomidou.mybatisplus.extension.injector.LogicSqlInjector;
//import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
//import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
//
//import javax.sql.DataSource;
//import java.util.Properties;
//
///**
// * Mybatis Plus Config
// *
// * <AUTHOR>
// * @since 2017/4/1
// */
//@Configuration
//@MapperScan(basePackages = "com.ly.mp.**.idal.**.mapper")
//public class MybatisPlusConfig {
//
//    @Value("${mybatis-plus.mapperLocations}")
//    String mapperLocations;
//    @Value("${mybatis-plus.typeAliasesPackage}")
//    String typeAliasesPackage;
//    @Value("${mybatis-plus.global-config.db-config.id-type}")
//    Integer idType; //主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
//    @Value("${mybatis-plus.global-config.db-config.field-strategy}")
//    Integer fieldStrategy; //字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
//    @Value("${mybatis-plus.global-config.db-config.column-underline}")
//    Boolean dbColumnUnderline; //驼峰下划线转换
//    @Value("${mybatis-plus.global-config.refresh}")
//    Boolean isRefresh; //刷新mapper 调试神器
//    @Value("${mybatis-plus.global-config.db-config.capital-mode}")
//    Boolean isCapitalMode; //数据库大写下划线转换
//    @Value("${mybatis-plus.global-config.db-config.logic-delete-value}")
//    String logicDeleteValue; //逻辑删除配置
//    @Value("${mybatis-plus.global-config.db-config.logic-not-delete-value}")
//    String logicNotDeleteValue; //逻辑删除配置
//    @Value("${mybatis-plus.global-config.db-config.db-type}")
//    String dbType;
//    @Value("${mybatis-plus.configuration.map-underscore-to-camel-case}")
//    Boolean camelCase;
//    @Value("${mybatis-plus.configuration.cache-enabled}")
//    Boolean cacheEnabled;
//
//    @Bean("mybatisSqlSession")
//    public SqlSessionFactory sqlSessionFactory(@Qualifier("dataSource") DataSource dataSource,
//                                               @Qualifier("globalConfig") GlobalConfiguration globalConfig,
//                                               @Qualifier("paginationInterceptor") PaginationInterceptor paginationInterceptor) throws Exception {
//        MybatisSqlSessionFactoryBean sqlSessionFactory = new MybatisSqlSessionFactoryBean();
//        /* 数据源 */
//        sqlSessionFactory.setDataSource(dataSource);
//        /* xml扫描 */
//        sqlSessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
//                .getResources(mapperLocations));
//        /* 扫描 typeHandler */
//        sqlSessionFactory.setTypeAliasesPackage(typeAliasesPackage);
//        sqlSessionFactory.setPlugins(new Interceptor[]{
//                paginationInterceptor
//        });
//        sqlSessionFactory.setConfigLocation(new PathMatchingResourcePatternResolver()
//                .getResource("mybatis-config.xml"));
////        MybatisConfiguration configuration = new MybatisConfiguration();
////        configuration.setJdbcTypeForNull(JdbcType.NULL);
////        configuration.setCacheEnabled(cacheEnabled);
////        /* 驼峰转下划线 */
////        configuration.setMapUnderscoreToCamelCase(camelCase);
////        /* 分页插件 */
////        //configuration.addInterceptor(paginationInterceptor);
////        //SQL日志打印格式，开发自用
////        configuration.setLogImpl(StdOutImpl.class);
////        sqlSessionFactory.setConfiguration(configuration);
//        sqlSessionFactory.setGlobalConfig(globalConfig);
//        return sqlSessionFactory.getObject();
//    }
//
//    @Bean
//    public GlobalConfiguration globalConfig(//MybatisKeyGenerator mybatisKeyGenerator)
//    ) {
//        GlobalConfiguration conf = new GlobalConfiguration(new LogicSqlInjector());
//        conf.setIdType(idType);
//        conf.setFieldStrategy(fieldStrategy);
//        conf.setDbColumnUnderline(dbColumnUnderline);
//        conf.setCapitalMode(isCapitalMode);
//        conf.setLogicDeleteValue(logicDeleteValue);
//        conf.setLogicNotDeleteValue(logicNotDeleteValue);
//        conf.setRefresh(isRefresh);
//        // conf.setKeyGenerator(mybatisKeyGenerator);
//        return conf;
//    }
//
//    @Bean
//    public PaginationInterceptor paginationInterceptor() {
//        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
//        // 开启 PageHelper 的支持
//        paginationInterceptor.setDialectType("mysql");
//        return paginationInterceptor;
//    }
//
////    @Bean
////    public MybatisKeyGenerator mybatisKeyGenerator() {
////        MybatisKeyGenerator mybatisKeyGenerator = new MybatisKeyGenerator();
////        return mybatisKeyGenerator;
////    }
//
//    /**
//     * SQL执行效率插件
//     */
//    @Bean
//    public PerformanceInterceptor performanceInterceptor() {
//        PerformanceInterceptor performanceInterceptor = new PerformanceInterceptor();
//        performanceInterceptor.setMaxTime(1000);
//        performanceInterceptor.setFormat(true);
//        //格式化sql语句
//        Properties properties = new Properties();
//        properties.setProperty("format", "true");
//        performanceInterceptor.setProperties(properties);
//        return performanceInterceptor;
//    }
//
//}
