package com.ly.mp.busicen.common.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.StringHelper;




/**
 * OMS工具类
 *
 * <AUTHOR>
 * @since 2019年1月14日
 */
public class OmsUtils {


	/**
	 * 构造方法私有化
	 */
	private OmsUtils(){}

	public static boolean checkEmail(String email) {
		String regex ="^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$";
		if(StringHelper.IsEmptyOrNull(email)) {
			return false;
		}
		return email.matches(regex);
	}
   
	/**
     * 不能包含中文以及特殊字符
     * @param object
     * @return
     */
    public static boolean IsNumAndEnglish(Object object){
    	if(object == null ) { return false;}
        String regex = "/^[A-Za-z0-9]+$/";
        return object.toString().matches(regex);
    }
    private static Pattern HAS_CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    public static boolean isContainChinese(String str) {
        Matcher m = HAS_CHINESE_PATTERN.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }
	
	/**
	 * 手机号隐藏
	 * @param iphone
	 * @return
	 */
	public static String iphoneHide(String iphone){
		if(iphone == null) {
			return "";
		}
		if(iphone.length() <= 4){
			return iphone;
		}
		/*if(iphone == null)
			return "";
		return iphone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");  */
		return iphone.substring(0,iphone.length()-4) + "****";
	}
	
	/**
	 * 身份证隐藏
	 * @param iphone
	 * @return
	 */
	public static String idCardHide(String idCard){
		if(idCard == null) {
			return "";
		}
		return idCard.replaceAll("(\\d{4})\\d{10}(\\d{4})","$1****$2");
	}
	
	/**
	 * 判断c是否为汉字或者日韩文
	 * @param
	 * @return boolean
	 */
	public static boolean isLetter(char c) { 
        int k = 0x80; 
        return c / k == 0 ? true : false; 
    }
	/**
    * 判断一个字符是否包含中文
    * @param String s 需要判断的字符串 
    * @return boolean 是否包含汉字
    */ 
	public static boolean isHaveHanZi(String str){
		 
		boolean flag=true;
		int count = 0;
		String regEx = "[\\u4e00-\\u9fa5]";
		Pattern p = Pattern.compile(regEx);
		Matcher m = p.matcher(str);
		
		while (m.find()) {
			for (int i = 0; i <= m.groupCount(); i++) {
				count = count + 1;
			}
		}
		if(count==0){
			flag=false;
		}
			return flag;
	}
   /**
    * 得到一个字符串的长度,显示的长度,一个汉字或日韩文长度为2,英文字符长度为1 
    * @param String s 需要得到长度的字符串 
    * @return int 得到的字符串长度 
    */ 
   public static int lengths(String str) {
       if (str == null) {
           return 0;
       }
       char[] c = str.toCharArray();
       int len = 0;
       for (int i = 0; i < c.length; i++) {
           len++;
           if (!isLetter(c[i])) {
               len++;
           }
       }
       return len;
   }
   
   /**
    * 判断接收的参数是否超过字段最大长度
    * @param field   字段名 
    * @param length  字段长度最大值
    * @param values  字段的值
    * @return ListResult
    */
	public static ListResult<Map<String, Object>> checkFieldsLength(
			String field,int length,String values)
	{
			ListResult<Map<String, Object>> result = new ListResult<>();
			result.setMsg("参数正常");
			result.setResult("1");
			try {

				if (values.length() > length) {
					result.setMsg(field+"不能长度不能超过"+length);
					result.setResult("0");
				}
				
			} catch (Exception e) {
				result.setResult("0");
				result.setMsg(e.getMessage().toString());
			}
			
			return result;
	}
	
	/**
	 * 新版 判断接收的参数是否超过字段最大长度
	 * @param field   字段名 
     * @param length  字段长度最大值
     * @param values  字段的值
     * @param flag 	     是否进行非空验证，
     * @return OptResult
	 */
	public static OptResult newCheckFieldsLength(
			String field,int length,String values,boolean flag){
		OptResult opt = new OptResult();
		ListResult<Map<String,Object>> result = checkFieldsLength(field,length,values);
		opt.setMsg(result.getMsg());
		opt.setResult(result.getResult());
		
		if (flag) {//是否进行非空判断
			 
			if (StringHelper.IsEmptyOrNull(values)) {//非空判断
				
				throw new RuntimeException(field+"不能为空");
				
			}else if ("0".equals(opt.getResult())){//字段长度判断
				
				throw new RuntimeException(opt.getMsg());
				
			}
			
		}else{
			
			if ("0".equals(opt.getResult())) {
				throw new RuntimeException(opt.getMsg());
			}
		}
		return opt;
	}
	/**
	 * 对参数进行非空判断
	 * @param  str	参数
	 * @param  name	 抛出异常得名字
	 * @return boolean
	 */
	public static boolean isNull(String str,String name){
		
		if (StringHelper.IsEmptyOrNull(str)) {//非空判断
			throw new RuntimeException(name+"不能为空");
		}
	
		return true;
	}
	/**
	 * 如果这个键不存在或则为空格则返回true
	 */
	public static boolean ChechMapValue(Map<String, Object> mapParam, String Key) {
		//if(StringHelper.IsEmptyOrNull(mapParam.get(Key)))
		if (!(mapParam.containsKey(Key) && !"".equals(mapParam.get(Key)))) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * 判断字符串是否是正确的时间格式
	 * @param dateString 需要判断的时间字符串
	 * 转换格式为：yyyy-MM-dd HH:mm:ss
	 * @return true表示正确的时间，false表示错误的时间
	 */
	public static boolean isValidDate(String dateString) {
		boolean convertSuccess = true;
		try {
			// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			// 设置lenient为false.
			// 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
			format.setLenient(false);
			format.parse(dateString);
		} catch (ParseException e) {
			// e.printStackTrace();
			// 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
			convertSuccess = false;
		}
		return convertSuccess;
	}
	

	/**
	 * 时间字符串转日期格式，如果时间字符串不符合要求，返回空值
	 * @param dateString 需要判断的时间字符串
	 * @param formartType 转换格式，如：yyyy-MM-dd HH:mm:ss
	 * @return 返回空值表示错误的时间
	 */
	public static String checkDate(String dateString, String formartType) {
		String dateRet = "";
		try {
			// 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
			SimpleDateFormat format = new SimpleDateFormat(formartType);
			// 设置lenient为false.
			// 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
			format.setLenient(false);
			dateRet = format.parse(dateString).toString();
		} catch (ParseException e) {
			// e.printStackTrace();
			// 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
			dateRet = "";
		}
		return dateRet;
	}
	
	/**
	 * 判断是否是整数，如-1，0，1，2...
	 * @param string
	 * @return true是 false否
	 */
	public static boolean isNumeric(String string){
	    return NUMERIC_PATTERN.matcher(string).matches();   
	}
	private static Pattern NUMERIC_PATTERN = Pattern.compile("^-?\\d+$");
	
	/**
	 * 判断两个集合元素值是否一致
	 * @param a
	 * @param b
	 * @return
	 */
	public static <T extends Comparable<T>> boolean compare(List<T> a, List<T> b) {
        if (a.size() != b.size()) {
            return false;
        }
        Collections.sort(a);
        Collections.sort(b);
        for (int i = 0; i < a.size(); i++) {
            if (!a.get(i).equals(b.get(i))) {
                return false;
            }
        }
        return true;
    }
	
	/**
	 * 根据Key值获取Value值，当key不存在时，返回空值不会抛出空指针异常
	 * @return
	 */
	public static String getMapValue(Map<String, Object> map,String key) {
		try {
			if (map.containsKey(key)) {
				return ToString(map.get(key)).replace("'", "");/*替换 ' 防止数据库报错*/
			}else{
				return "";
			}
		} catch (Exception e) {
			return "";
		}
    }

	/**
	 * ToString方法，如果是null值，返回空值不会抛出空指针异常
	 * @return
	 */
	public static String ToString(Object value) {
		try {
			return value.toString().trim();
		} catch (Exception e) {
			return "";
		}
    }
	
	/**
	 * 判断对象null 或  empty 或 ""
	 * @param object
	 * @return true 是  false 否
	 */
	public static boolean isNullOrEmpty(Object object) {
		boolean flag = false;
		try {
			if (object == null 
					|| ToString(object).equals("")) {
				flag = true;
			}
		} catch (Exception e) {
			flag = true;
		}
		return flag;
	}
	
	/**
     * 使用 Map按key进行排序
     * @param map
     * @return
     */
    public static Map<String, Object> sortMapObjectByKey(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }

        Map<String, Object> sortMap = new TreeMap<String, Object>(new MapKeyComparator());
        sortMap.putAll(map);
        return map;
    }
    
    /**
     * 使用 Map按key进行排序
     * @param map
     * @return
     */
    public static Map<String, String> sortMapStringByKey(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }

        Map<String, String> sortMap = new TreeMap<String, String>(new MapKeyComparator());
        sortMap.putAll(map);
        return map;
    }
	
    /**
	 * 必须是排序好的ListMap，求和列为单列使用
	 * @param listMap
	 * @param sumCol 求和的列
	 * @param sumByCol 根据哪列进行分组求和
	 */
	@SuppressWarnings("unused")
	private static List<Map<String, Object>> sumMapByOneKey(List<Map<String, Object>> listMap, String sumCol, String sumByCol) {
		try{
			List<Map<String, Object>> retListMap = new ArrayList<Map<String,Object>>();/*最终返回结果*/
			List<Map<String, Object>> sumListMap = new ArrayList<Map<String,Object>>();/*合计行*/
			if(listMap.size() == 0) { return listMap;}
			String[] cols = sumCol.split(",");/*统计的列*/
			Map<Object, List<Map<String, Object>>> groupBy = listMap.stream().collect(
							Collectors.groupingBy(myMap -> myMap.get(sumByCol)));
			/*分组后，执行分组求和*/
			for (Object keyGroup : groupBy.keySet()) {
				List<Map<String, Object>> groupList = groupBy.get(keyGroup.toString());
				Map<String,Object> goupMap = new HashMap<>();
				/*新增合计行*/
				for (String ss : groupList.get(0).keySet()) {
					goupMap.put(ss, groupList.get(0).get(sumByCol)+"小计：");
				}
	
				/*求和的列*/
				for (String sumCols : cols) {
					double sumMoney = groupList.stream().mapToDouble(myMap -> 
					{
						Double d_value = 0D;
						try {
							d_value = Double.valueOf(myMap.get(sumCols).toString());
						} catch (Exception e) {
						}
						return d_value;
					}).sum();
					goupMap.put(sumCols, sumMoney);
				}
				groupList.add(goupMap);
				sumListMap.add(goupMap);
				retListMap.addAll(groupList);
			}
			/*总计*/
			Map<String,Object> goupMap = new HashMap<>();
			for (String ss : listMap.get(0).keySet()) {
				goupMap.put(ss, "合计：");
			}
			/*求和的列*/
			for (String sumCols : cols) {
				double sumMoney = listMap.stream().mapToDouble(myMap -> 
				{
					Double d_value = 0D;
					try {
						d_value = Double.valueOf(myMap.get(sumCols).toString());
					} catch (Exception e) {
					}
					return d_value;
				}).sum();
				goupMap.put(sumCols, sumMoney);
			}
			retListMap.add(goupMap);
	//		System.out.println(retListMap);
			return retListMap;/*总计*/
		} catch (Exception e) {
			return listMap;
		}
	}
	/**
	 * 传入的ListMap必须按照sumByCol进行排序
	 * 
	 * @param listMap 需要进行统计的ListMap
	 * @param sumCol 需要求和的列名，如“数量，金额”
	 * @param sumByCol 通过哪些列作为筛选条件进行分组求和，如“终端，车型”，有顺序要求，从第二个条件起进行组合分组
	 * @return
	 */
	public static List<Map<String, Object>> sumMapByMoreKey(List<Map<String, Object>> listMap, String sumCol, String sumByCol){
		try {	
			/*这时候构建出新的结构*/
			String[] cols = sumCol.split(",");/*统计的列*/
			String[] colBys = sumByCol.split(",");/*根据哪些列求和*/
			String columValue = "";
			List<Map<String, Object>> newListMap = new ArrayList<>(); /*主体部分*/
			List<Map<String, Object>> groupListMap = new ArrayList<>(); /*分组求和部分，每次不同分组根据此集合进行*/
			Map<String,Object> mapSum = new HashMap<>();
	
			/*总计*/
			Map<String,Object> goupSumMap = new HashMap<>();
			for (String ss : listMap.get(0).keySet()) {
				goupSumMap.put(ss, "合计：");
			}
			/*求和的列*/
			for (String sumCols : cols) {
				double sumMoney = listMap.stream().mapToDouble(myMap -> 
				{
					Double d_value = 0D;
					try {
						d_value = Double.valueOf(myMap.get(sumCols).toString());
					} catch (Exception e) {
					}
					return d_value;
				}).sum();
				goupSumMap.put(sumCols, sumMoney);
			}
			
			for (int j = 0; j < colBys.length; j++) {
				columValue = colBys[j];
				boolean is_new = true;//是否新的分组
				for (int i = 0; i < listMap.size(); i++) {/*求值循环*/
					boolean is_continueadd = true;/*是否继续求和*/
					for (Map<String, Object> map : groupListMap) {
						String Value1 = "";
						String Value2 = "";
						for (int k = 0; k <= j; k++) {
							Value1 += map.get(colBys[k]).toString();
							Value2 += listMap.get(i).get(colBys[k]).toString();
							columValue = map.get(colBys[k]).toString();
						}
						if(Value1.contains("小计：")) {
							is_continueadd = false;
						}
						if(!Value1.equals(Value2)){
							/*此行未出现过，此时需要进行下一个分组求和*/
							is_new = true; 
							break;
						}
					}
					if(!is_continueadd) {
						/*新分组求和*/
						is_new = false;
						groupListMap = new ArrayList<>(); 
						mapSum = new HashMap<>(); 
						groupListMap.add(listMap.get(i));
						continue;
					}
					if(is_new){
						if(i > 0){
							/*构建合计行*/
							for (String keyName : listMap.get(i).keySet()) {
								mapSum.put(keyName, columValue+"小计：");
								for (String iSumCol : cols) {
									if(keyName.equals(iSumCol)){
										mapSum.put(iSumCol, "0");
									}
								}
							}
							/*分组结束，进行合计*/
							for (Map<String, Object> map : groupListMap) {
								for (String iSumCol : cols) {
									Double sumValue = Double.valueOf("0");
									try {
										sumValue = Double.valueOf(map.get(iSumCol).toString());
									} catch (Exception e) {
										sumValue = Double.valueOf("0");
									}
									mapSum.put(iSumCol,
										Double.valueOf(mapSum.get(iSumCol).toString()) + sumValue);
								}
							}
							
							
	//						System.out.println("添加前后" + groupListMap);
	//						System.out.println("分组统计" + mapSum);
							if(j > 0){
								/*多列统计，只新增新的合计行即可*/
								newListMap.add(i,mapSum);
								listMap.add(i,mapSum);
	//							System.out.println("多列统计最终" + newListMap);
							}else{
								/*单列统计*/
								newListMap.addAll(groupListMap);
								newListMap.add(mapSum);
	//							System.out.println("单列统计最终" + newListMap);
							}
							/*新分组求和*/
							groupListMap = new ArrayList<>(); 
							mapSum = new HashMap<>();  
						}
						is_new = false;
					}
					groupListMap.add(listMap.get(i));
					if(i+1 == listMap.size()){
						/*处理最后一行*/
						is_new = true; 
						if(i > 0){
							/*构建合计行*/
							for (String keyName : listMap.get(i).keySet()) {
								mapSum.put(keyName, listMap.get(i).get(colBys[j])+"小计：");
								for (String iSumCol : cols) {
									if(keyName.equals(iSumCol)){
										mapSum.put(iSumCol, "0");
									}
								}
							}
							/*分组结束，进行合计*/
							for (Map<String, Object> map : groupListMap) {
								for (String iSumCol : cols) {
									Double sumValue = Double.valueOf("0");
									try {
										sumValue = Double.valueOf(map.get(iSumCol).toString());
									} catch (Exception e) {
										sumValue = Double.valueOf("0");
									}
									mapSum.put(iSumCol,
										Double.valueOf(mapSum.get(iSumCol).toString()) + sumValue);
								}
							}
							
							
	//						System.out.println("添加前后" + groupListMap);
	//						System.out.println("分组统计" + mapSum);
	
							if(j > 0){
								/*多列统计，只新增新的合计行即可*/
								newListMap.add(i,mapSum);
								listMap.add(i,mapSum);
	//							System.out.println("多列统计最终" + newListMap);
							}else{
								/*单列统计*/
								newListMap.addAll(groupListMap);
								newListMap.add(mapSum);
	//							System.out.println("单列统计最终" + newListMap);
							}
							/*新分组求和*/
							groupListMap = new ArrayList<>(); 
							mapSum = new HashMap<>();  
						}else if(i == 0){
							for (Map<String, Object> map : groupListMap) {
								String Value1 = "";
								String Value2 = "";
								for (int k = 0; k <= j; k++) {
									Value1 += map.get(colBys[k]).toString();
									Value2 += listMap.get(i).get(colBys[k]).toString();
									columValue = map.get(colBys[k]).toString();
								}
								if(Value1.contains("小计：")) {
									is_continueadd = false;
								}
								if(!Value1.equals(Value2)){
									/*此行未出现过，此时需要进行下一个分组求和*/
									is_new = true; 
									break;
								}
							}
							/*构建合计行*/
							for (String keyName : listMap.get(i).keySet()) {
								mapSum.put(keyName, listMap.get(i).get(colBys[j])+"小计：");
								for (String iSumCol : cols) {
									if(keyName.equals(iSumCol)){
										mapSum.put(iSumCol, "0");
									}
								}
							}
							/*分组结束，进行合计*/
							for (Map<String, Object> map : groupListMap) {
								for (String iSumCol : cols) {
									Double sumValue = Double.valueOf("0");
									try {
										sumValue = Double.valueOf(map.get(iSumCol).toString());
									} catch (Exception e) {
										sumValue = Double.valueOf("0");
									}
									mapSum.put(iSumCol,
										Double.valueOf(mapSum.get(iSumCol).toString()) + sumValue);
								}
							}
							
							
	//						System.out.println("添加前后" + groupListMap);
	//						System.out.println("分组统计" + mapSum);
	
							if(j > 0){
								/*多列统计，只新增新的合计行即可*/
								newListMap.add(i,mapSum);
								listMap.add(i,mapSum);
	//							System.out.println("多列统计最终" + newListMap);
							}else{
								/*单列统计*/
								newListMap.addAll(groupListMap);
								newListMap.add(mapSum);
	//							System.out.println("单列统计最终" + newListMap);
							}
							/*新分组求和*/
							groupListMap = new ArrayList<>(); 
							mapSum = new HashMap<>();  
						}
						is_new = false;
					}
				}/*求值循环*/
				
				groupListMap = new ArrayList<>(); 
				mapSum = new HashMap<>();  
				columValue = "";
				listMap = new ArrayList<>(); /*主体部分*/ 
				
				listMap.addAll(newListMap);
	//			System.out.println(newListMap);
			}
			/*添加总计列*/
			newListMap.add(goupSumMap);
			//System.out.println(newListMap);
			return newListMap;
		} catch (Exception e) {
			return listMap;
		}
	}
	
	/**
	 * @Title:  map中是否有这些key
	 */
	public static boolean isMapContainsKey(Map<String , Object> map , String... keys ) {
		if (keys == null) {
			return true;
		}
		if (keys .length == 0) {
			return true;
		}
		for (int i = 0; i < keys.length; i++) {
			if(!map.containsKey(keys[i])) {
				return false;
			}
		}		
		return true;
	}
	
	/**
	 * @Title:  map中有这些key值是否为空
	 */
	public static boolean isMapValueNotNull(Map<String, Object> map , String... keys) {
		if(isMapContainsKey(map, keys)) {
			for (int i = 0; i < keys.length; i++) {
				if(map.get(keys[i]) == null) {
					return false;
				}else if("".equals(map.get(keys[i]).toString())) {
					return false;
				}
			}	
			return true;
		}else {
			return false;
		}
	}
	
	/**'
	 * 
	 * @Title:  字符串转换成时间
	 * @author: SimpleWu
	 */
	public static Date StringFormatData(String data , String format){
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		try {
			return sdf.parse(data);
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public static boolean isCarnumberNO(Object carnumber) {
		/**
		 *  1.常规车牌号：仅允许以汉字开头，后面可录入六个字符，由大写英文字母和阿拉伯数字组成。如：粤B12345；
		 *  2.武警车牌：允许前两位为大写英文字母，后面可录入五个或六个字符，由大写英文字母和阿拉伯数字组成，其中第三位可录汉字也可录大写英文字母及阿拉伯数字，第三位也可空，如：WJ警00081、WJ京1234J、WJ1234X。
		 *  3.最后一个为汉字的车牌：允许以汉字开头，后面可录入六个字符，前五位字符，由大写英文字母和阿拉伯数字组成，而最后一个字符为汉字，汉字包括“挂”、“学”、“警”、“军”、“港”、“澳”。如：粤Z1234港。
		 *  4.新军车牌：以两位为大写英文字母开头，后面以5位阿拉伯数字组成。如：BA12345。
		 */
		   String carnumRegex = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[警京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{0,1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$";
		   if (StringHelper.IsEmptyOrNull(carnumber)) {return false;}
		   else {return carnumber.toString().matches(carnumRegex);}
	}
	
	/** 
     * 判断手机号码格式是否有效 
     *  
     */
	public static boolean isPhoneLegal(String phoneNumber){
		/**
		 * 手机号码11位数，匹配格式：前三位固定格式+后8位任意数 
		 */
		if (phoneNumber == null || "".equals(phoneNumber)) {
        return false;
		}
		String regex = "^[0-9][0-9][0-9]\\d{8}$";
		return phoneNumber.matches(regex);
	}
	
	/** 
     * 判断是否是数字
     *  
     */
	public static boolean isNumber(Object number){
		//判断是否为空
		if (number == null || "".equals(number)) {
        return false;
		}
		String regex = "^[0-9]*[1-9][0-9]*$";
		return number.toString().matches(regex);
	}
	
	/** 
     * 判断是否是数字
	 * 只是一个方法重载
     *  
     */
	public static boolean isNumber(Object number,boolean is_number){
		//判断是否为空
		if (number == null || "".equals(number)) {
        return false;
		}
		String regex = "^[0-9]{1,20}$";
		return number.toString().matches(regex);
	}
	
	/** 
     * 判断是否包含非法字符
     *  
     */
	public static boolean isCharacter(Object fields){
		//判断是否为空
		if (fields == null || "".equals(fields)) {
        return false;
		}
		String regex = "^[A-Za-z0-9\u4e00-\u9fa5]+$";
		return fields.toString().matches(regex);
	}
	
	
	/** 
     * 判断身份证格式是否有效
     *  
     */
	 public static boolean isIDNumber(String IDNumber) {
        if (IDNumber == null || "".equals(IDNumber)) {
            return false;
        }
        // 定义判别用户身份证号的正则表达式（15位或者18位，最后一位可以为字母）
        String regularExpression = "(^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|" +
                "(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}$)";
       /**
        *          假设18位身份证号码:41000119910101123X  410001 19910101 123X
        *          ^开头
        *          [1-9] 第一位1-9中的一个      4
        *          \\d{5} 五位数字           10001（前六位省市县地区）
        *          (18|19|20)                19（现阶段可能取值范围18xx-20xx年）
        *          \\d{2}                    91（年份）
        *          ((0[1-9])|(10|11|12))     01（月份）
        *          (([0-2][1-9])|10|20|30|31)01（日期）
        *          \\d{3} 三位数字            123（第十七位奇数代表男，偶数代表女）
        *          [0-9Xx] 0123456789Xx其中的一个 X（第十八位为校验值）
        *          $结尾
        *          
        *          假设15位身份证号码:410001910101123  410001 910101 123
        *          ^开头
        *          [1-9] 第一位1-9中的一个      4
        *          \\d{5} 五位数字           10001（前六位省市县地区）
        *          \\d{2}                    91（年份）
        *          ((0[1-9])|(10|11|12))     01（月份）
        *          (([0-2][1-9])|10|20|30|31)01（日期）
        *          \\d{3} 三位数字            123（第十五位奇数代表男，偶数代表女），15位身份证不含X
        *          $结尾         
        */
        
        boolean matches = IDNumber.matches(regularExpression);

        //判断第18位校验值
        if (matches) {

            if (IDNumber.length() == 18) {
                try {
                    char[] charArray = IDNumber.toCharArray();
                    //前十七位加权因子
                    int[] idCardWi = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
                    //这是除以11后，可能产生的11位余数对应的验证码
                    String[] idCardY = {"1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"};
                    int sum = 0;
                    for (int i = 0; i < idCardWi.length; i++) {
                        int current = Integer.parseInt(String.valueOf(charArray[i]));
                        int count = current * idCardWi[i];
                        sum += count;
                    }
                    char idCardLast = charArray[17];
                    int idCardMod = sum % 11;
                    if (idCardY[idCardMod].toUpperCase().equals(String.valueOf(idCardLast).toUpperCase())) {
                        return true;
                    } else {
                        System.out.println("身份证最后一位:" + String.valueOf(idCardLast).toUpperCase() + 
                                "错误,正确的应该是:" + idCardY[idCardMod].toUpperCase());
                        return false;
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("异常:" + IDNumber);
                    return false;
                }
            }

        }
        return matches;
    }
	public static void main(String[] args) {
        Matcher m = HAS_CHINESE_PATTERN.matcher("2wq-汉化");
        if (m.find()) {
          System.out.println("汉字");
        } else {
          System.out.println("NOT CHARACTER");
        }
	}
	/**
	   *   校验手机号码 ，严格形式
	 * <AUTHOR>
	 */
	public static boolean checkTelephone(String phone) {
		String yphone = "^[1][0-9][0-9]\\d{8}$";
		String zphone = "^[0][1-9]{2,3}-[0-9]{5,10}$";
		if(StringHelper.IsEmptyOrNull(phone)) {//手机号为空
			return false;
		}
		if(phone.matches(yphone)) {
			return true;//手机号码格式正确
		}
		if(phone.matches(zphone)) {
			return true;//座机号正确
		}
		return false;
	}
}

/**
 * Map列排序使用
 * <AUTHOR>
 *
 */
class MapKeyComparator implements Comparator<String>{

    @Override
    public int compare(String str1, String str2) {   
        return str1.compareTo(str2);
    }
}
