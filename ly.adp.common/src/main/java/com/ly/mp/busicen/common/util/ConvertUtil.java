package com.ly.mp.busicen.common.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;



/**
 * 数据转换util
 *
 */
public class ConvertUtil {
	private ConvertUtil() {
	}

	/**
	 * 将Map中value为null的值转换成空字符串('')
	 * 
	 * @param datas
	 * @return
	 */
	public static List<Map<String, Object>> null2EmptyStr(List<Map<String, Object>> datas) {
		if (datas == null || datas.isEmpty()) {
			return datas;
		}

		// 转换后的数据
		List<Map<String, Object>> rows = new ArrayList<>();

		for (Map<String, Object> data : datas) {
			Map<String, Object> row = new LinkedHashMap<>();
			
			for (Map.Entry<String, Object> entry : data.entrySet()) {
				// value为null的entry, 将value更改为空字符串
				Object value = entry.getValue() == null ? "" : entry.getValue();
				row.put(entry.getKey(), value);
			}
			
			rows.add(row);
		}

		return rows;
	}
	
	public static void main(String[] args) {
		List<Map<String, Object>> datas = new ArrayList<>();
		Map<String, Object> data = new LinkedHashMap<>();
		data.put("name", "tom");
		data.put("date", new Date());
		Map<String, Object> data2 = new LinkedHashMap<>();
		data2.put("name", "jim");
		data2.put("date", null);
		
		datas.add(data);
		datas.add(data2);
		System.out.println("datas : " + datas);
		
		List<Map<String, Object>> rows = ConvertUtil.null2EmptyStr(datas);
		System.out.println("rows : " + rows);
	}
	
}
