package com.ly.mp.busicen.common.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 返回结果工具类
 * <AUTHOR>
 * @date   2018-7-25
 */
public class ResultUtil {

    public static <T> Result success(T entity) {
    	Result result = new Result();
        result.setResult(ResultEnum.SUCCESS.getCode());
        result.setMsg(ResultEnum.SUCCESS.getMsg());
        result.setRows(entity);
        return result;
    }
    
    public static <T> Result success(Page<T> userList) {
    	Result resultList = new Result();
		resultList.setResult(ResultEnum.SUCCESS.getCode());
		resultList.setMsg(ResultEnum.SUCCESS.getMsg());
		resultList.setPageindex(userList.getCurrent());
		resultList.setPages(userList.getPages());
		resultList.setRecords(userList.getTotal());
		resultList.setRows(userList.getRecords());
		return resultList;
	}
    
    public static Result success() {
    	Result result = new Result();
    	result.setResult(ResultEnum.SUCCESS.getCode());
    	result.setMsg(ResultEnum.SUCCESS.getMsg());
        return result;
    }

    public static Result error(String code, String msg) {
    	Result result = new Result();
    	result.setResult(code);
    	result.setMsg(msg);
        return result;
    }
    
    public static Result error(String msg) {
    	Result result = new Result();
    	result.setResult(ResultEnum.FAIL.getCode());
    	result.setMsg(msg);
        return result;
    }
}