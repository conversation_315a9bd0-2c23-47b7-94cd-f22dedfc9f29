package com.ly.mp.busicen.common.controller;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.ly.mp.busicen.common.constant.ErrorEnum;
import com.ly.mp.busicen.common.handler.ResultHandler;
import com.ly.mp.busicen.common.service.ServiceException;

/**
 * 控制器增强
 * <AUTHOR>
 * 日期：2019-07-01
 */
@Aspect
@Component
public class ControllerAdvice {
	
	private static final Logger log = LoggerFactory.getLogger(ControllerAdvice.class);

	@Pointcut("execution(* com.ly.mp.busicen.csc.resolver.**.*(..))")
	public void queryPoincut() {
		
	}
	
	@Around("queryPoincut()")
	public Object aroundInQueryService(ProceedingJoinPoint joinPoint) {
		String methodName = joinPoint.getSignature().getDeclaringTypeName();
		Object[] args = joinPoint.getArgs();
		log.error("调用方法:{}=>参数:{}=>开始执行",methodName,args);
		Object result = null;
		try {
			result = joinPoint.proceed();
			log.error("调用方法:{}=>参数:{}=>执行成功",methodName,args);
			return result;
		} catch (Throwable e) {
			e.printStackTrace();
			log.error("调用方法:{}=>参数:{}=>执行失败=>原因：{}",methodName,args,e.getMessage());
			if (e instanceof ServiceException) {
				ServiceException serviceException = (ServiceException) e;
				return ResultHandler.queryError(serviceException.getResult(), serviceException.getMassage());
			}else {
				return ResultHandler.queryError(ErrorEnum.SYS_ERROR.getResult(),ErrorEnum.SYS_ERROR.getMsg());
			}
		}
	}
	
	
	@Around("execution(* com.ly.mp.busicen.csc.resolver.mutation.**.*(..))")
	public Object aroundInUpdateService(ProceedingJoinPoint joinPoint) {
		String methodName = joinPoint.getSignature().getDeclaringTypeName();
		Object[] args = joinPoint.getArgs();
		log.error("调用方法:{}=>参数:{}=>开始执行",methodName,args);
		Object result = null;
		try {
			result = joinPoint.proceed();
			log.error("调用方法:{}=>参数:{}=>执行成功",methodName,args);
			return result;
		} catch (Throwable e) {
			e.printStackTrace();
			log.error("调用方法:{}=>参数:{}=>执行失败=>原因：{}",methodName,args,e.getMessage());
			if (e instanceof ServiceException) {
				ServiceException serviceException = (ServiceException) e;
				return ResultHandler.updateError(serviceException.getResult(), serviceException.getMessage());
			}else {
				return ResultHandler.updateError(ErrorEnum.SYS_ERROR.getResult(),ErrorEnum.SYS_ERROR.getMsg());
			}
		}
	}

}
