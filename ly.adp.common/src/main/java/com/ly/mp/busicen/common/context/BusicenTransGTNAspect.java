package com.ly.mp.busicen.common.context;

import java.util.function.Supplier;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

@Aspect
public class BusicenTransGTNAspect {
	
	@Pointcut("@annotation(io.seata.spring.annotation.GlobalTransactional)")
	public void pointcut() {
		
	}
	
	@Around(value="pointcut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {		
		
		Object result = BusicenTransNormalTemplate.doTrans(new SupplierTransAspect(point));		
		return result;
		
	}
	
	public class SupplierTransAspect implements Supplier<Object>{
		
		ProceedingJoinPoint point;
		
		public SupplierTransAspect(ProceedingJoinPoint point) {
			this.point = point;
		}

		@Override
		public Object get() {
			try {
				return point.proceed(point.getArgs());
			} catch (RuntimeException e) {
				throw e;
			}
			catch (Exception e) {
				throw new RuntimeException(e);
			}catch (Throwable e) {
				throw new RuntimeException(e);
			}
		}
		
	}
	
	

}
