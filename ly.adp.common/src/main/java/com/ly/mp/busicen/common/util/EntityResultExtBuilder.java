package com.ly.mp.busicen.common.util;

import com.ly.mp.busicen.common.entity.EntityResultExt;

public class EntityResultExtBuilder<T> {

	private EntityResultExt<T> parent = new EntityResultExt<>();

	public static <T> EntityResultExtBuilder<T> create() {		
		return new EntityResultExtBuilder<T>();
	}
	
	public static <T> EntityResultExtBuilder<T> creatOk() {
		EntityResultExtBuilder<T> result = create();
		result.parent.setMsg("保存成功");
		result.parent.setResult("1");
		return result;
	}

	public EntityResultExt<T> build() {
		return  parent;
	}

	public EntityResultExtBuilder<T> result(String result) {
		parent.setResult(result);
		return this;
	}	

	public EntityResultExtBuilder<T> msg(String msg) {
		parent.setMsg(msg);
		return this;
	}

	public  EntityResultExtBuilder<T> rows(T rows) {
		parent.setRows(rows);
		return this;
	}
	
	
	public static void main(String[] args) {
		EntityResultExt<?> tt = EntityResultExtBuilder.create().result("-1").rows(11).build();
		EntityResultExt<String> tts = EntityResultExtBuilder.<String>creatOk().result("1").msg("aa").rows("rrrrj").build();
		System.out.println(tt.getRows());
		System.out.println(tts.getRows());
	}

}
