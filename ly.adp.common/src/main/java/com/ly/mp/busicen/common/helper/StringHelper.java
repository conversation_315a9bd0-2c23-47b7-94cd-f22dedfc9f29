package com.ly.mp.busicen.common.helper;

import java.util.Collection;
import java.util.Map;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR> huangjun
 * @Description: 字符处理工具类
 * @date Date : 2019/08/29
 */
public class StringHelper {

    /**
    * @Description: 将形如：1,2,3的字符串转为'1','2','3'
    * <AUTHOR> huang<PERSON>
    * @date Date : 2019/08/29
    */
    public static String convert(String sourceStr) {
        StringBuilder realId = new StringBuilder();
        if (!StringUtils.isEmpty(sourceStr)) {
            for (String orgId : sourceStr.split(",")) {
                if (realId.length() > 0 ) {
                    realId.append(",").append("'").append(orgId).append("'");
                } else {
                    realId.append("'").append(orgId).append("'");
                }
            }
        }
        return realId.toString();
    }

}
