package com.ly.mp.busicen.common.constant;
/**
 * CIP代码中用到的常量定义
 * 
 * <AUTHOR>
 * @date 2021-08-10
 * 
 */
public enum CipConstEnum {

	/**
	 * 单据类型：救援
	 */
	SERVER_TYPE_JY("1","救援"),//服务类别
	/**
	 * 单据类型：投诉
	 */
	SERVER_TYPE_TS("2","投诉"),
	/**
	 * 单据类型：咨询
	 */
	SERVER_TYPE_ZX("3","咨询"),
	/**
	 * 单据类型：RTM安全风险处置
	 */
	SERVER_TYPE_RTM("50","RTM安全风险处置"),
	/**
	 * 投诉级别：一般投诉
	 */
	SERVER_LEVEL_YBTS("YBTS","一般投诉"),
	/**
	 * 投诉级别：重大投诉
	 */
	SERVER_LEVEL_ZDTS("ZDTS","重大投诉"),
	/**
	 * 导出操作类型：从页面导出-实时导出
	 */
	EXPORT_WAY_PAGE("2","从页面导出-实时导出"),
	/**
	 * 导出操作类型：JOB任务导出-离线导出
	 */
	EXPORT_WAY_JOB("1","JOB任务导出-离线导出");
	
	private CipConstEnum(String code,String name) {
		this.code=code;
		this.name=name;
	}
	private String code;
	private String name;
	public String getCode() {
		return code;
	}
	public String getName() {
		return name;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public void setName(String name) {
		this.name = name;
	}
}
