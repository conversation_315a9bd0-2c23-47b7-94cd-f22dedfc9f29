package com.ly.mp.busicen.common.util;

import java.util.Collection;
import java.util.Map;

import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.service.ServiceException;
/**
 * 断言抛出自定义异常，统一拦截异常处理
 * <AUTHOR>
 * 日期：2019-07-06
 */

public class BusicenAssert {

	/**
	 * @param expression true 抛出异常信息
	 * @param message
	 */
	public static void isTrue(boolean expression, String message) {
		if (expression) {
			throw BusicenException.create(message);
		}
	}

	public static void isNull(@Nullable Object object, String message) {
		if (object != null) {
			throw BusicenException.create(message);
		}
	}

	public static BusicenAssert notNull(@Nullable Object object, String result,String message) throws ServiceException{
		if (object == null) {
			throw new ServiceException(result, message);
		}
		return null;
	}

	public static BusicenAssert noNullElements(@Nullable Object[] array,String result, String message) throws ServiceException{
		if (array != null) {
			for (Object element : array) {
				if (element == null) {
					throw new ServiceException(result, message);
				}
			}
		}
		return null;
	}

	public static BusicenAssert notEmpty(@Nullable Collection<?> collection, String result, String message) throws ServiceException{
		if (CollectionUtils.isEmpty(collection)) {
			throw new ServiceException(result, message);
		}
		return null;
	}

	public static BusicenAssert notEmpty(@Nullable Map<?, ?> map, String result, String message) throws ServiceException {
		if (CollectionUtils.isEmpty(map)) {
			throw new ServiceException(result, message);
		}
		return null;
	}

	public static BusicenAssert notEmptyOrNull(@Nullable String object, String result, String message) throws ServiceException {
		if (StringUtils.isEmpty(object)) {
			throw new ServiceException(result, message);
		}
		return null;
	}

}
