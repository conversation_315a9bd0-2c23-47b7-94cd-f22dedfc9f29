package com.ly.mp.busicen.common.constant;
/**
 * CIP代码中用到的常量定义
 * 
 * <AUTHOR>
 * @date 2021-10-13
 * 
 */
public enum CipDataSourceEnum {
	
	/**
	 * 查询数据源：db
	 */
	DATA_SOURCE_DB("db","查询数据库：db"),
	/**
	 * 查询数据源：es
	 */
	DATA_SOURCE_ES("es","查询数据库：es");
	
	private CipDataSourceEnum(String code,String name) {
		this.code=code;
		this.name=name;
	}
	private String code;
	private String name;
	public String getCode() {
		return code;
	}
	public String getName() {
		return name;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public void setName(String name) {
		this.name = name;
	}
}
