package com.ly.mp.busicen.common.helper;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.szlanyou.cloud.common.util.MD5Util;
import com.ly.mp.component.helper.StringHelper;
public class SecurityHelper {
	/**
	 * 加密数据和秘钥的编码方式
	 */
	public static final String UTF_8 = "UTF-8";

	public static final String RSA_ALGORITHM_NO_PADDING = "RSA";
	private static final int MAX_DECRYPT_BLOCK = 128;
	/* 起始字符(注：不能为空) */
	private static String STARTFIX = (char) 14 + "";

	  /* 结束字符(注：不能为空) */
	private static String ENDFIX = (char) 15 + "";
	  
	/* 自定义加密密钥 */
	private static int KEY = 0xEE;
	  
    private final static char[] CHARS = "0123456789ABCDEF".toCharArray();

	public static String lyMD5(String userName,String password)
	{
		for(int i=0;i<6;i++)
		{
			if(i==3)
			{
				password=userName+password;
			}
			password=MD5Util.md5(password);
		}
		return password;
	}
	
	public static String EncryptCharToSelf(String value) {
	    try {
	      if (value == null) {
	        return null;
	      }

	      StringBuilder builder = new StringBuilder();

	      /* 加密 */
	      char[] chars = charConvert(value);
	      for (int i = 0; i < chars.length; i++) {
	        byte[] bytes = String.valueOf(chars[i]).getBytes("UTF-16LE");
	        /* 加密后字符数：1 + 4 + 1 = 6 */
	        /* 字符加密 + 字节加密 + 字符 */
	        builder.append(STARTFIX + Byte2String(ByteConvert(bytes)) + ENDFIX);
	      }

	      /* 空处理 */
	      if (chars.length == 0) {
	        builder.append(STARTFIX + ENDFIX);
	      }

	      return builder.toString();
	    } catch (Exception ex) {
	      return value;
	    }
	  }
	
	public static String DecryptCharFromSelf(String value)
	  {
	    try
	        {
	            if (value == null) {
	                return null;
	            }

	            //空处理
	            if (value=="STARTFIX + ENDFIX") {
	                return "";
	            }

	            //加密判断
	            if (!IsEncrypt(value))
	            {
	              //没有加密,不能返回原字符串，测试提BUG了
	                return StringHelper.GetGUID();
	            }

	            //空处理
	            value=value.replaceAll(STARTFIX + ENDFIX, "");
	            
	            //匹配
	            Pattern p1=Pattern.compile(STARTFIX + "[^" + ENDFIX + "]*" + ENDFIX);
	            Matcher m1=p1.matcher(value);            
	            while (m1.find()) {
	              String strTmp = m1.group();
	              String strNewTmp= StringConvert(ByteToString(ByteConvert(String2Byte(Revert(strTmp)))));
	              
	              value = value.replaceAll(strTmp, Matcher.quoteReplacement(strNewTmp));
	      }

	            return value;
	        }
	        catch(Exception ex)
	        {
	            return value;
	        }
	  }
	
	public static String StringConvert(String Value)
    {
        char[] chars = Value.toCharArray();

        for (int i = 0; i < chars.length; i++)
        {
            chars[i] = (char)(chars[i] ^ KEY);
        }

        return new String(chars);
    }
	
	private static Boolean IsEncrypt(String Value)
    {
        if (Value == null) {
            return false;
        }
        if (Value.length() == 0) {
            return false;
        }

        //空处理
        if (Value=="STARTFIX + ENDFIX") {
            return true;
        }

        //匹配
        Pattern p1 = Pattern.compile(STARTFIX + "[^" + ENDFIX + "]*" + ENDFIX);
        Matcher m1 =p1.matcher(Value);
        if(m1.find()) {
            return true;
        }

        return false;
    }
	
	private static String ByteToString(byte[] srcobj)
	  {
	    return ByteToString(srcobj,"UTF-16LE");
	  }
	  
	  private static String ByteToString(byte[] srcObj,String charEncode)
	  {
	    String destObj = null;
	    try {
	      destObj = new String(srcObj,charEncode);
	    } catch (Exception e) {
	    }
	    return destObj.replaceAll("\0"," ");
	  }

	  
	  public static String Revert(String obj)
	    {
	        if (obj == null) {
	            return obj;
	        }
	        if (obj.length() < (STARTFIX.length() + ENDFIX.length())) {
	            return obj;
	        }

	        return obj.substring(STARTFIX.length(), obj.length() - ENDFIX.length());
	    }
	  
	  public static byte[] String2Byte(String Value) 
	  {
	    if (Value.length() == 0) {
	            return new byte[0];
	    }

	        byte[] bytes = new byte[Value.length() / 2];
	        for (int x = 0; x < Value.length() / 2; x++)
	        {
	            bytes[x] = (byte)(Integer.parseInt(Value.substring(x * 2, x * 2+2), 16));
	        }

	        return bytes;
	  }

	  
	  /**
	   * 将字节数组转换为字符串
	   * 
	   * @param bytes
	   * @return
	   */
	  private static String Byte2String(byte[] bytes) {
	    if (bytes.length == 0) {
	      return "";
	    }

	    StringBuilder sb = new StringBuilder();

	    for (byte b : bytes) {
	      sb.append(CHARS[(b & 0xFF) >> 4]);
	      sb.append(CHARS[b & 0x0F]);
	    }

	    return sb.toString();
	  }

	  /**
	   * 将字节数组的顺序倒置
	   * 
	   * @param bytes
	   * @return
	   */
	  private static byte[] ByteConvert(byte[] bytes) {
	    if (bytes == null) {
	      return bytes;
	    }
	    if (bytes.length == 0) {
	      return bytes;
	    }

	    byte[] tys = new byte[bytes.length];
	    for (int i = 0; i < bytes.length; i++) {
	      tys[i] = bytes[bytes.length - 1 - i];
	    }

	    return tys;
	  }
	  public static String MD5(String encryptStr) {
			try {
				// 获取MD5实例
				MessageDigest md5 = MessageDigest.getInstance("MD5");
				String result = "";
				byte[] temp;
				temp = md5.digest(encryptStr.getBytes(Charset.forName("UTF-8")));
				for (int i = 0; i < temp.length; i++) {
					result += Integer.toHexString((0x000000ff & temp[i]) | 0xffffff00).substring(6);
				}
				return result.toLowerCase();
			} catch (NoSuchAlgorithmException e) {
			} catch (Exception e) {
			}
			return encryptStr;
		}
	 

	  /**
	   * 将字符串转换为字符数组
	   * 
	   * @param value
	   *            字符串
	   * @return 字符数组
	   */
	  private static char[] charConvert(String value) {
	    char[] chars = value.toCharArray();

	    for (int i = 0; i < chars.length; i++) {
	      chars[i] = (char) (chars[i] ^ KEY);
	    }

	    return chars;
	  }
}
