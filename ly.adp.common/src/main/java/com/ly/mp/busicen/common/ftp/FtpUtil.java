package com.ly.mp.busicen.common.ftp;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPFileFilters;
import org.apache.commons.pool2.ObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * FTP工具类
 *
 * <AUTHOR>
 */
public class FtpUtil {

    private static Logger log = LoggerFactory.getLogger(FtpConfiguration.class);

    /**
     * 0
     * ftpClient连接池初始化标志
     */
    private static volatile boolean hasInit = false;
    /**
     * ftpClient连接池
     */
    private static ObjectPool<FTPClient> ftpClientPool;

    /**
     * 初始化ftpClientPool
     *
     * @param ftpClientPool
     */
    public static void init(ObjectPool<FTPClient> ftpClientPool) {
        if (!hasInit) {
            synchronized (FtpUtil.class) {
                if (!hasInit) {
                    FtpUtil.ftpClientPool = ftpClientPool;
                    hasInit = true;
                }
            }
        }
    }


    /**
     * 按行读取FTP文件
     *
     * @param remoteFilePath 文件路径（path+fileName）
     * @return
     * @throws IOException
     */
    public static List<String> readFileByLine(String remoteFilePath) throws IOException {
        FTPClient ftpClient = getFtpClient();
        try (InputStream in = ftpClient.retrieveFileStream(encoding(remoteFilePath)); BufferedReader br =
                new BufferedReader(new InputStreamReader(in))) {
            return br.lines().map(line -> StringUtils.trimToEmpty(line)).filter(line -> StringUtils.isNotEmpty(line)).collect(Collectors.toList());
        } finally {
            ftpClient.completePendingCommand();
            releaseFtpClient(ftpClient);
        }
    }

    /**
     * 获取指定路径下FTP文件
     *
     * @param remotePath 路径
     * @return FTPFile数组
     * @throws IOException
     */
    public static FTPFile[] retrieveFTPFiles(String remotePath) throws IOException {
        FTPClient ftpClient = getFtpClient();
        try {
            return ftpClient.listFiles(encoding(remotePath + "/"), file -> file != null && file.getSize() > 0);
        } finally {
            releaseFtpClient(ftpClient);
        }
    }

    /**
     * 获取指定路径下FTP文件名称
     *
     * @param remotePath 路径
     * @return ftp文件名称列表
     * @throws IOException
     */
    public static List<String> retrieveFileNames(String remotePath) throws IOException {
        FTPFile[] ftpFiles = retrieveFTPFiles(remotePath);

        if (ArrayUtils.isEmpty(ftpFiles)) {
            return new ArrayList<>();
        }
        return Arrays.stream(ftpFiles).filter(Objects::nonNull).map(FTPFile::getName).collect(Collectors.toList());
    }

    /**
     * 编码文件路径
     */
    private static String encoding(String pathOrFileName) throws UnsupportedEncodingException {
        // FTP协议里面，规定文件名编码为iso-8859-1，所以目录名或文件名需要转码
        //return new String(path.replaceAll("/", "\\\\").getBytes("UTF-8"),"iso-8859-1");  //如果是部署在windows系统的ftp需要使用此行代码
        return new String(pathOrFileName.getBytes("UTF-8"), "iso-8859-1");  //如果是部署在linux系统的ftp需要使用此行代码
    }

    /**
     * 区分浏览器的编码文件
     *
     * @param fileName
     * @param request
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String newEncoding(String fileName, HttpServletRequest request) throws UnsupportedEncodingException {
        String userAgent = request.getHeader("user-agent").toLowerCase();

        if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
            // win10 ie edge 浏览器 和其他系统的ie
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } else {
            // fe
            fileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
        }
        return fileName;
    }

    /**
     * 获取ftpClient
     *
     * @return
     */
    private static FTPClient getFtpClient() {
        checkFtpClientPoolAvailable();
        FTPClient ftpClient = null;
        Exception ex = null;
        // 获取连接最多尝试3次
        for (int i = 0; i < 3; i++) {
            try {
                ftpClient = ftpClientPool.borrowObject();
                ftpClient.changeWorkingDirectory("/");
                break;
            } catch (Exception e) {
                ex = e;
            }
        }
        if (ftpClient == null) {
            throw new RuntimeException("Could not get a ftpClient from the pool", ex);
        }
        return ftpClient;
    }

    /**
     * 释放ftpClient
     */
    private static void releaseFtpClient(FTPClient ftpClient) {
        if (ftpClient == null) {
            return;
        }

        try {
            ftpClientPool.returnObject(ftpClient);
        } catch (Exception e) {
            log.error("Could not return the ftpClient to the pool", e);
            // destoryFtpClient
            if (ftpClient.isAvailable()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException io) {
                }
            }
        }
    }

    /**
     * 检查ftpClientPool是否可用
     */
    private static void checkFtpClientPoolAvailable() {
        Assert.state(hasInit, "FTP未启用或连接失败！");
    }


    /**
     * 从ftp上下载文件
     *
     * @param remoteFilePath ftp上文件全路径
     * @param response       输出对象
     * @throws IOException 异常
     */
    public static void downloadFile(String remoteFilePath, HttpServletRequest request, HttpServletResponse response) throws IOException {
        String fileName = remoteFilePath.substring(remoteFilePath.lastIndexOf('/') + 1);
        downloadFile(remoteFilePath, fileName, request, response);
    }

    /**
     * 文件下载
     *
     * <AUTHOR> @version
     */
    public static void downloadFile(String remoteFilePath, String fileName, HttpServletRequest request, HttpServletResponse response) throws IOException {
        //自动匹配通用格式
        String userAgent = request.getHeader("user-agent").toLowerCase();
        if (userAgent.contains("msie") || userAgent.contains("like gecko")) {
            fileName = URLEncoder.encode(fileName, "UTF-8");
        } else {
            fileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
        }
        //excel下载格式
        response.setContentType("multipart/form-data;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
        FTPClient ftpClient = getFtpClient();
        String path = encoding(remoteFilePath);
        try (InputStream in = ftpClient.retrieveFileStream(path); OutputStream out = response.getOutputStream()) {
            int size = 0;
            byte[] buf = new byte[10240];
            while ((size = in.read(buf)) > 0) {
                out.write(buf, 0, size);
                out.flush();
            }
        } finally {
            ftpClient.completePendingCommand();
            releaseFtpClient(ftpClient);
        }
    }

    public static void preView(String remoteFilePath, String fileName, HttpServletResponse response, HttpServletRequest request) throws IOException {
//    	fileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
        //excel下载格式
        //自动匹配通用格式
        FTPClient ftpClient = getFtpClient();
        String path = encoding(remoteFilePath);
        String suffix1 = fileName.substring(fileName.lastIndexOf('.') + 1);
        response.setContentType(returnContentType(suffix1));
        response.setHeader("Content-Disposition", "filename=" + newEncoding(fileName, request));
        FTPClient ftpClient2 = getFtpClient();
        try (InputStream in2 = ftpClient2.retrieveFileStream(path)) {
            response.setCharacterEncoding(getFilecharset(in2));
        } finally {
            ftpClient2.completePendingCommand();
            releaseFtpClient(ftpClient2);
        }
        try (InputStream in = ftpClient.retrieveFileStream(path);
             OutputStream out = response.getOutputStream()) {
            int size = 0;
            byte[] buf = new byte[10240];
            while ((size = in.read(buf)) > 0) {
                out.write(buf, 0, size);
                out.flush();
            }
        } finally {
            ftpClient.completePendingCommand();
            releaseFtpClient(ftpClient);
        }
    }

    public static void preView(String remoteFilePath, String fileName, HttpServletResponse response) throws IOException {
//    	fileName = new String(fileName.getBytes("UTF-8"), "iso-8859-1");
        //excel下载格式
        //自动匹配通用格式
        String suffix1 = fileName.substring(fileName.lastIndexOf('.') + 1);
        response.setContentType(returnContentType(suffix1) + ";charset=UTF-8");
        response.setHeader("Content-Disposition", "filename=" + encoding(fileName));

        FTPClient ftpClient = getFtpClient();
        String path = encoding(remoteFilePath);
        try (InputStream in = ftpClient.retrieveFileStream(path);
             OutputStream out = response.getOutputStream()) {
            int size = 0;
            byte[] buf = new byte[10240];
            while ((size = in.read(buf)) > 0) {
                out.write(buf, 0, size);
                out.flush();
            }
        } finally {
            ftpClient.completePendingCommand();
            releaseFtpClient(ftpClient);
        }
    }

    public static String returnContentType(String str) {
        String type = "";
        if ("pdf".equalsIgnoreCase(str)) {
            type = "application/pdf";
        } else if ("txt".equalsIgnoreCase(str)) {
            type = "text/plain";
        } else if ("pptx".equalsIgnoreCase(str)) {
            type = "application/x-ppt";
        } else if ("doc".equalsIgnoreCase(str)) {
            type = "application/msword";
        } else if ("jpg".equalsIgnoreCase(str) || "jpeg".equalsIgnoreCase(str) || "png".equalsIgnoreCase(str)) {
            type = "image/jpeg";
        }
        return type;
    }


    /**
     * 上传文件
     *
     * @param localFilePath   本地文件路径
     * @param remoteDirectory 服务器地址
     * @return 返回上传状态
     * @throws IOException 异常
     */
    public static boolean uploadFile(String localFilePath, String remoteDirectory) throws IOException {
        localFilePath = localFilePath.replaceAll("\\\\", "/");
        String remoteFileName = localFilePath.substring(localFilePath.lastIndexOf('/') + 1);
        return uploadFile(localFilePath, encoding(remoteDirectory), encoding(remoteFileName));
    }

    /**
     * <AUTHOR>
     * @version 创建时间 ：2018年9月11日 上午11:01:57
     */
    public static boolean uploadFile(InputStream inputStream, String remoteDirectory, String remoteFileName) throws IOException {
        FTPClient ftpClient = getFtpClient();
        boolean flag = false;
        try {
            checkAndChangeWorkingDirecotry(ftpClient, remoteDirectory);
            flag = ftpClient.storeFile(encoding(remoteFileName), inputStream);
        } catch (Exception e) {
            log.error(" uploadFile error " + e.getMessage(), e);
        } finally {
            inputStream.close();
            releaseFtpClient(ftpClient);
        }
        return flag;
    }

    private static boolean uploadFile(String localFilePath, String remoteDirectory, String remoteFileName) throws IOException {
        FTPClient ftpClient = getFtpClient();
        boolean flag = false;
        //InputStream fileStream = null;
        try (InputStream fileStream = new FileInputStream(localFilePath)) {
            checkAndChangeWorkingDirecotry(ftpClient, remoteDirectory);
            flag = ftpClient.storeFile(remoteFileName, fileStream);
        } catch (Exception e) {
            log.error(" uploadFile error " + e.getMessage(), e);
        } finally {
            releaseFtpClient(ftpClient);
        }
        return flag;
    }

    /**
     * 上传文件
     *
     * @param inputStream    文件输入流
     * @param remoteFileName 上传地址
     * @param billNo         业务单号
     * @return 返回上传状态
     * @throws IOException 异常
     * <AUTHOR>
     * @since 2018-09-22
     */
    public static Boolean uploadFile(InputStream inputStream, String remoteFileName, String remoteDirectory, String billNo) throws IOException {
        FTPClient ftpClient = getFtpClient();
        //以二进制流的形式上传,防止文件内容乱码.
        boolean flag = false;
        //兼容中文路径,中文文件名
//		remoteDirectory = new String(remoteDirectory.getBytes("UTF-8"),"iso-8859-1");
        try {
//			remoteFileName = new String(remoteFileName.getBytes("UTF-8"),"iso-8859-1");
            checkAndChangeWorkingDirecotry(ftpClient, encoding(remoteDirectory));
            flag = ftpClient.storeFile(encoding(remoteFileName), inputStream);
        } catch (Exception e) {
            log.error(" uploadFile error " + e.getMessage(), e);
        } finally {
            inputStream.close();
            releaseFtpClient(ftpClient);
        }
        return flag;
    }

    public boolean uploadFolder(String localFolderPath, String remoteDirectoryPath) throws IOException {
        FTPClient ftpClient = getFtpClient();
        try {
            remoteDirectoryPath = checkModifyRemoteDirPath(remoteDirectoryPath);
            Map<String, List<File>> recordFilesMap = loopRecordFiles(localFolderPath, remoteDirectoryPath, null);
            Iterator<String> it = recordFilesMap.keySet().iterator();
            while (it.hasNext()) {
                String remoteDirPath = it.next();
                List<File> files = recordFilesMap.get(remoteDirPath);
                checkAndChangeWorkingDirecotry(ftpClient, remoteDirPath);
                for (File file : files) {
                    if (file.isDirectory()) {
                        ftpClient.mkd(file.getName());
                    } else if (file.isFile()) {
                        try (InputStream fileIs = new FileInputStream(file);) {
                            ftpClient.storeFile(file.getName(), fileIs);
                        } catch (FileNotFoundException e) {
                            log.error(" uploadFolder error " + e.getMessage(), e);
                        }
                    }
                }
            }
        } finally {
            ftpClient.completePendingCommand();
            releaseFtpClient(ftpClient);
        }
        return false;
    }

    private Map<String, List<File>> loopRecordFiles(String localDirPath, String remoteDirPath, Map<String, List<File>> recordMap) {
        if (recordMap == null) {
            recordMap = new LinkedHashMap<String, List<File>>();
        }
        File currDirFile = new File(localDirPath);
        List<File> currDirPathFiles = new ArrayList<File>();
        recordMap.put(remoteDirPath, currDirPathFiles);
        for (File file : currDirFile.listFiles()) {
            currDirPathFiles.add(file);
            if (file.isDirectory()) {
                loopRecordFiles(localDirPath + "/" + file.getName(), remoteDirPath + "/" + file.getName(), recordMap);
            }
        }
        return recordMap;
    }

    /**
     * 删除文件夹下的所有的文件
     *
     * @param remoteDir 服务器文件夹，删除子文件夹，删除文件夹下的文件
     * @throws IOException 异常
     */
    public static void cleanRemoteDirFiles(String remoteDir) throws IOException {
        FTPClient ftpClient = getFtpClient();
        try {
            if (!ftpClient.changeWorkingDirectory(encoding(remoteDir))) {
                //makeAndChangeWorkingDirecotry(ftpClient,remoteDir);
                log.info("没有对应目录");
            } else {
                List<String> names = retrieveFileNames(remoteDir);
                log.info("文件夹下的文件：" + names.toString());
                FTPFile[] remoteFiles = ftpClient.listFiles(encoding(remoteDir + "/"), FTPFileFilters.DIRECTORIES);
                if (ArrayUtils.isEmpty(remoteFiles)) {

                } else {
                    List<String> disNames = Arrays.stream(remoteFiles).filter(Objects::nonNull).map(FTPFile::getName).collect(Collectors.toList());
                    log.info(disNames.toString());
                    for (String name : disNames) {
                        if (!".".equals(name) && !"..".equals(name)) {
                            //递归
                            cleanRemoteDirFiles(remoteDir + "/" + name);
                            //ftpClient.changeWorkingDirectory(name);
                            boolean res = ftpClient.removeDirectory(encoding(remoteDir + "/" + name));
                            log.info("目录删除状态：" + res);
                        }
                    }
                }
                for (String name : names) {
                    log.info(name);
                    boolean res = ftpClient.deleteFile(encoding(name));
                    log.info("删除状态：" + res);
                }
            }
        } finally {
            releaseFtpClient(ftpClient);
        }
    }

    /**
     * 检查服务器是否有对应的目录，没有则创建
     *
     * @param ftpClient ftp服务器
     * @param remoteDir 服务器文件夹
     * @return 返回创建文件夹状态
     * @throws IOException 异常信息
     */
    private static boolean checkAndChangeWorkingDirecotry(FTPClient ftpClient, String remoteDir) throws IOException {

        boolean flag = ftpClient.changeWorkingDirectory(remoteDir);
        if (!flag) {
            flag = makeAndChangeWorkingDirecotry(ftpClient, remoteDir);
        }
        return flag;
    }

    /**
     * 创建文件夹
     *
     * @param ftpClient ftp服务器
     * @param remoteDir 创建的文件夹
     * @return 返回创建状态
     * @throws IOException
     */
    private static boolean makeAndChangeWorkingDirecotry(FTPClient ftpClient, String remoteDir) throws IOException {
        ftpClient.changeWorkingDirectory("/");
        for (String path : remoteDir.split("/")) {
            if (path.length() > 0) {
                if (!ftpClient.changeWorkingDirectory(path)) {
                    ftpClient.mkd(path);
                    if (!ftpClient.changeWorkingDirectory(path)) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 给外面调用的路径都要是绝对路径/开头但没有/ 结尾
     *
     * @param remotePath
     * @return
     * @throws UnsupportedEncodingException
     */
    private static String checkModifyRemoteDirPath(String remotePath) throws UnsupportedEncodingException {
        if (remotePath == null || remotePath.length() == 0) {
            return "/";
        }
        if (!remotePath.startsWith("/")) {
            remotePath = "/" + remotePath;
        }
        if (remotePath.length() > 1 && remotePath.endsWith("/")) {
            remotePath = remotePath.substring(0, remotePath.length() - 1);
        }
        return (encoding(remotePath));
    }

    /**
     * 文件移动
     *
     * @param from 需要被移动文件的全路径
     * @param to   到移动的目录，需要带上文件名称
     * @throws IOException 异常
     */
    public static void remove(String from, String to) throws IOException {
        FTPClient ftpClient = getFtpClient();
        try {
            //不存在目录就创建，存在就返回false
            ftpClient.makeDirectory(encoding(to.substring(0, to.lastIndexOf('/'))));
            boolean res = ftpClient.rename(encoding(from), encoding(to));
            log.info("状态：" + res);
        } catch (IOException e) {
            // TODO Auto-generated catch block
            log.error("remove IOException :" + e.getMessage(), e);
            //e.printStackTrace();
        } finally {
            releaseFtpClient(ftpClient);
        }
    }

    /**
     * * 删除文件 *
     *
     * @param pathname FTP服务器保存目录 *
     * @param filename 要删除的文件名称 *
     * @return
     */
    public static boolean deleteFile(String pathname, String filename) {
        boolean result = false;
        FTPClient ftpClient = getFtpClient();
        try {
            // 切换FTP目录
//			ftpClient.changeWorkingDirectory(pathname);
            result = ftpClient.deleteFile(encoding(pathname));
            ftpClient.logout();
            log.info("删除文件成功");
        } catch (Exception e) {
            log.error("删除文件失败 :" + e.getMessage(), e);
            //e.printStackTrace();
        } finally {
            releaseFtpClient(ftpClient);
        }
        return result;
    }

    /**
     * 获取文件的编码格式
     *
     * @param inputStream
     * @return
     */
    public static String getFilecharset(InputStream inputStream) {

        String charset = "GBK";
        byte[] first3Bytes = new byte[3];
        try {
            boolean checked = false;
            BufferedInputStream bis = new BufferedInputStream(inputStream);
            bis.mark(0);
            int read = bis.read(first3Bytes, 0, 3);
            if (read == -1) {
                return charset; // 文件编码为 ANSI
            } else if (first3Bytes[0] == (byte) 0xFF
                    && first3Bytes[1] == (byte) 0xFE) {
                charset = "UTF-16LE"; // 文件编码为 Unicode
                checked = true;
            } else if (first3Bytes[0] == (byte) 0xFE
                    && first3Bytes[1] == (byte) 0xFF) {
                charset = "UTF-16BE"; // 文件编码为 Unicode big endian
                checked = true;
            } else if (first3Bytes[0] == (byte) 0xEF
                    && first3Bytes[1] == (byte) 0xBB
                    && first3Bytes[2] == (byte) 0xBF) {
                charset = "UTF-8"; // 文件编码为 UTF-8
                checked = true;
            }
            bis.reset();
            if (!checked) {
                int loc = 0;
                while ((read = bis.read()) != -1) {
                    loc++;
                    if (read >= 0xF0) {
                        break;
                    }
                    if (0x80 <= read && read <= 0xBF) { // 单独出现BF以下的，也算是GBK
                        break;
                    }
                    if (0xC0 <= read && read <= 0xDF) {
                        read = bis.read();
                        if (0x80 <= read && read <= 0xBF) { // 双字节 (0xC0 - 0xDF)
                            // (0x80
                            // - 0xBF),也可能在GB编码内
                            continue;
                        } else {
                            break;
                        }
                    } else if (0xE0 <= read && read <= 0xEF) {// 也有可能出错，但是几率较小
                        read = bis.read();
                        if (0x80 <= read && read <= 0xBF) {
                            read = bis.read();
                            if (0x80 <= read && read <= 0xBF) {
                                charset = "UTF-8";
                                break;
                            } else {
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                }
            }
            bis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return charset;
    }
}
