package com.ly.mp.busicen.common.util;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.context.ApplicationContext;
//import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
//import org.springframework.stereotype.Component;
import com.ly.mp.busicen.common.helper.SpringContextHolder;

/**
 * 配置获取公共类 env.getProperty()方法，如果不存在则返回空字符串，不报错
 *
 * <AUTHOR>
 *
 */
public class ConfigGetUtil {

	private static Logger log = LoggerFactory.getLogger(ConfigGetUtil.class);

	private enum Singleton {
		INSTANCE;

		private final ConfigGetUtil instance;

		Singleton() {
			instance = new ConfigGetUtil();
		}

		private ConfigGetUtil getInstance() {
			return instance;
		}
	}
	private ConfigGetUtil() {
		configKeyMap.put("vcpBlackNameUrl", "urlconfig.vcp_black_name.url");
		configKeyMap.put("port", "server.port");
		configKeyMap.put("e3sUrlprefix", "urlconfig.e3sinterfaceset.urlprefix");
		configKeyMap.put("cipplatformUrl", "urlconfig.cipplatform.url");
		configKeyMap.put("doaEmpUrl", "urlconfig.cip_tblp_doaws01.doasq_emp_url");
		configKeyMap.put("doaSynUrl", "urlconfig.cip_tblp_doaws01.doasq_syn_url");
		configKeyMap.put("doaWsUrl", "urlconfig.cip_tblp_doaws01.doasq_ws_url");
		configKeyMap.put("clueSendUrl", "urlconfig.cip_clue_send_config.url");
		configKeyMap.put("clueSendToken", "urlconfig.cip_clue_send_config.token");
		configKeyMap.put("clueSendCharset", "urlconfig.cip_clue_send_config.charset");
		configKeyMap.put("clueSendConnectTimeout", "urlconfig.cip_clue_send_config.connect_timeout");
		configKeyMap.put("clueSendReadTimeout", "urlconfig.cip_clue_send_config.read_timeout");
		configKeyMap.put("saleClueSendUrl", "urlconfig.cip_sale_clue_send_config.url");
		configKeyMap.put("saleClueSendTokenUrl", "urlconfig.cip_sale_clue_send_config.token_url");
		configKeyMap.put("saleClueSendCharset", "urlconfig.cip_sale_clue_send_config.charset");
		configKeyMap.put("saleClueSendConnectTimeout", "urlconfig.cip_sale_clue_send_config.connect_timeout");
		configKeyMap.put("saleClueSendReadTimeout", "urlconfig.cip_sale_clue_send_config.read_timeout");
		configKeyMap.put("saleClueSendHeadSystem", "urlconfig.cip_sale_clue_send_config.head_system");
		configKeyMap.put("saleClueSendHeadService", "urlconfig.cip_sale_clue_send_config.head_service");
		configKeyMap.put("wxcsiUserAgent", "urlconfig.cip_wxcsi_basedata.user_agent");
		configKeyMap.put("wxcsiCallType", "urlconfig.cip_wxcsi_basedata.call_type");
		configKeyMap.put("wxcsiDealProgressAdd", "urlconfig.cip_wxcsi_basedata.deal_progress_add");
		configKeyMap.put("wxcsiServerTitle", "urlconfig.cip_wxcsi_basedata.server_title");
		configKeyMap.put("wxcsiConsultDesc", "urlconfig.cip_wxcsi_basedata.consult_desc");
		configKeyMap.put("wxcsiServerAnswer", "urlconfig.cip_wxcsi_basedata.server_answer");
		configKeyMap.put("wxcsiRemark", "urlconfig.cip_wxcsi_basedata.remark");
		configKeyMap.put("wxcsiCustRequirement", "urlconfig.cip_wxcsi_basedata.cust_requirement");
		configKeyMap.put("evaBuilder", "urlconfig.eva_001.builder");
		configKeyMap.put("evaAppkey", "urlconfig.eva_001.appkey");
		configKeyMap.put("evaAppid", "urlconfig.eva_001.appid");
		configKeyMap.put("evaZxBigType", "urlconfig.eva_001.zx_big_type");
		configKeyMap.put("evaZxType", "urlconfig.eva_001.zx_type");
		configKeyMap.put("evaPicAdd", "urlconfig.eva_001.pic_add");
		configKeyMap.put("evaDealProgressAdd", "urlconfig.eva_001.deal_progress_add");
		configKeyMap.put("cipjob007MaxDay", "busicen.cipjob007.max_day");
		configKeyMap.put("xfSitSwitch", "busicen.xf_sit.switch");
		configKeyMap.put("xfSitPhone", "busicen.xf_sit.phone");
		configKeyMap.put("accidentClassCodeTwo", "busicen.server_order_config.accident_class_code_two");
		configKeyMap.put("publicClassCodeTwo", "busicen.server_order_config.public_class_code_two");
		configKeyMap.put("xiaoiBaseUrl", "xiaoi.base-url");
		configKeyMap.put("xiaoiAesKey", "xiaoi.aes-key");
		configKeyMap.put("xfNetUrlprefix", "urlconfig.xfnetinterface.urlprefix");
		configKeyMap.put("qualityTsClassCode", "busicen.server_order_config.quality_ts_class_code");
		configKeyMap.put("partTsClassCode", "busicen.server_order_config.part_ts_class_code");
		configKeyMap.put("zdtsCode", "busicen.server_order_config.zdts_code");
		configKeyMap.put("serverOrderHfContent", "busicen.cip_serverorder_hf.satisfaction.content");
		configKeyMap.put("serverOrderHfMsgAccountN", "busicen.cip_serverorder_hf.msgaccount.n");
		configKeyMap.put("serverOrderHfMsgAccountV", "busicen.cip_serverorder_hf.msgaccount.v");
		configKeyMap.put("exportMaxNum", "busicen.exportLimit.maxNum");
		configKeyMap.put("exportESMaxNum", "busicen.exportLimit.esMaxNum");
		configKeyMap.put("e3sInterfaceUrl", "busicen.e3sInterface.url");
		configKeyMap.put("e3sCode", "busicen.e3sInterface.e3sCode");
		configKeyMap.put("e3sVisitSource", "busicen.e3sInterface.visitSource");
		configKeyMap.put("e3sMaxNum", "busicen.e3sInterface.maxNum");
		configKeyMap.put("esIp", "busicen.esConfig.ip");
		configKeyMap.put("esPort", "busicen.esConfig.port");
		configKeyMap.put("esUserName", "busicen.esConfig.userName");
		configKeyMap.put("esPassword", "busicen.esConfig.password");
		configKeyMap.put("baiduReverseGeocoding", "urlconfig.baidu.reverse_reocoding.url");
		configKeyMap.put("riskWorkGroup", "busicen.workGroup.risk");
		configKeyMap.put("dfnWorkGroup", "busicen.workGroup.dfn");
		configKeyMap.put("ghWorkGroup", "busicen.workGroup.gh");
		configKeyMap.put("exportMntDir", "busicen.dir_config.exportMntDir");
		configKeyMap.put("workGroupPopConfig", "busicen.workGroup.popConfig");
	}
	/**
	 * 实例对象
	 */
	public static ConfigGetUtil getInstance() {
		return Singleton.INSTANCE.getInstance();
	}

	private Environment env = SpringContextHolder.getBean(Environment.class);

	// 所有获取配置都走这个方法，方便记录日志
	private String getIfNotEmpty(String oldValue, String key) {
		String tmpStr = oldValue;
		if (env != null) { //StringHelper.IsEmptyOrNull(tmpStr) //不判断原值非空，防止修改之后要重启服务
			tmpStr = env.getProperty(key);
			if (tmpStr == null) {
				tmpStr = "";
			}
		}
		log.info(LogBuilder.getBizParams(LogBuilder.Module.CIP, "获取配置文件", "配置:" + key + ",值:" + tmpStr));
		return tmpStr;
	}

	/**
	 * 通过配置key获取配置值，配置路径示例：urlconfig.vcp_black_name.url
	 *
	 * <AUTHOR>
	 *
	 */
	public String getConfigByKey(String key) {
		return getIfNotEmpty("",key);
	}

	/**
	 * 通过配置属性获取配置值，配置属性示例：vcpBlackNameUrl
	 *
	 * <AUTHOR>
	 *
	 */
	public String getConfigByName(String configName) {
		return getIfNotEmpty("",this.getMapValueByKey(configName));
	}

	/*
	 * 服务端口号
	 */
	private String port;
	/*
	 * E3S接口地址前缀
	 */
	private String e3sUrlprefix;
	//车联平台网关
	private String cipplatformUrl;
	//DOA接口
	private String doaEmpUrl;
	private String doaSynUrl;
	private String doaWsUrl;
	// 售后线索下发
	private String clueSendUrl;
	private String clueSendToken;
	private String clueSendCharset;
	private String clueSendConnectTimeout;
	private String clueSendReadTimeout;
	// 销售线索下发
	private String saleClueSendUrl;
	private String saleClueSendTokenUrl;
	private String saleClueSendCharset;
	private String saleClueSendConnectTimeout;
	private String saleClueSendReadTimeout;
	private String saleClueSendHeadSystem;
	private String saleClueSendHeadService;
	// 微信CSI配置
	private String wxcsiUserAgent;
	private String wxcsiCallType;
	private String wxcsiDealProgressAdd;
	private String wxcsiServerTitle;
	private String wxcsiConsultDesc;
	private String wxcsiServerAnswer;
	private String wxcsiRemark;
	private String wxcsiCustRequirement;
	//一键报障建单人及咨询类别
	private String evaBuilder;//建单人
	private String evaAppkey;
	private String evaAppid;
	private String evaZxBigType;//咨询类别（大类）
	private String evaZxType;//咨询类别（小类）
	private String evaPicAdd;//图片访问地址
	private String evaDealProgressAdd;//处理进度接口地址
	// VCP黑名单配置
	private String vcpBlackNameUrl;
	private String cipjob007MaxDay;//启辰未认证短信发送天数(交车日期)
	private String xfSitSwitch; //讯飞测试下发开关
	private String xfSitPhone;//讯飞测试号码
	private String accidentClassCodeTwo;//事故类二级服务类别配置
	private String publicClassCodeTwo;//公共机构类二级服务类别配置
	// 小I配置
	private String xiaoiBaseUrl;
	private String xiaoiAesKey;

	/**
	 * 	工单回访-满意度回访配置
	 */
	private String serverOrderHfContent; // 短信内容
	private String serverOrderHfMsgAccountN; // 短信账号 日产
	private String serverOrderHfMsgAccountV; // 短信账号 启辰
	/**
	 * 	百度地图解析经纬度
	 */
	private String baiduReverseGeocoding; // 百度请求api地址
	
	//报表实时导出最大数量
	private String exportMaxNum;
	//报表查询ES离线导出最大数量
	private String exportESMaxNum;
	private String e3sInterfaceUrl;
	private String e3sCode;
	private String e3sVisitSource;
	private String e3sMaxNum;
	private String esIp;
	private String esPort;
	private String esUserName;
	private String esPassword;
	
	
	/*
	 * .net讯飞接口地址前缀
	 */
	private String xfNetUrlprefix;
	/*
	 * 质量投诉类别
	 */
	private String qualityTsClassCode;
	/*
	 * 备件投诉类别
	 */
	private String partTsClassCode;
	
	/*
	 * 重大投诉类别：
	 * 一级或者一级二级编码，各级之间以|分隔，每级多个以英文,号分隔
	 * 示例：TS05|TS05015
	 */
	private String zdtsCode;
	
	/**
	 * 导出文件挂载根目录，默认：springboot/project
	 */
	private String exportMntDir;
	/**
	 *  弹屏工作组、类型配置
	 */
	private String workGroupPopConfig;
	
	private Map<String,String> configKeyMap=new HashMap<>();
	
	
	public Map<String,String> getConfigKeyMap(){
		return configKeyMap;
	}
	//通过配置属性获取真实的配置key
	private String getMapValueByKey(String configName) {
		if(configKeyMap.containsKey(configName)) {
			return configKeyMap.get(configName);
		}
		return "";
	}

	public String getVcpBlackNameUrl() {
		//this.getConfigKey("XXXXX")
		vcpBlackNameUrl = this.getIfNotEmpty("", this.getMapValueByKey("vcpBlackNameUrl"));
		return vcpBlackNameUrl;
	}

	public String getPort() {
		port = this.getIfNotEmpty("", this.getMapValueByKey("port"));
		return port;
	}

	public String getE3sUrlprefix() {
		e3sUrlprefix = this.getIfNotEmpty("", this.getMapValueByKey("e3sUrlprefix"));
		return e3sUrlprefix;
	}

	public String getCipplatformUrl() {
		cipplatformUrl = this.getIfNotEmpty("", this.getMapValueByKey("cipplatformUrl"));
		return cipplatformUrl;
	}

	public String getDoaEmpUrl() {
		doaEmpUrl = this.getIfNotEmpty("", this.getMapValueByKey("doaEmpUrl"));
		return doaEmpUrl;
	}

	public String getDoaSynUrl() {
		doaSynUrl = this.getIfNotEmpty("", this.getMapValueByKey("doaSynUrl"));
		return doaSynUrl;
	}

	public String getDoaWsUrl() {
		doaWsUrl = this.getIfNotEmpty("", this.getMapValueByKey("doaWsUrl"));
		return doaWsUrl;
	}

	public String getClueSendUrl() {
		clueSendUrl = this.getIfNotEmpty("", this.getMapValueByKey("clueSendUrl"));
		return clueSendUrl;
	}

	public String getClueSendToken() {
		clueSendToken = this.getIfNotEmpty("", this.getMapValueByKey("clueSendToken"));
		return clueSendToken;
	}

	public String getClueSendCharset() {
		clueSendCharset = this.getIfNotEmpty("", this.getMapValueByKey("clueSendCharset"));
		return clueSendCharset;
	}

	public String getClueSendConnectTimeout() {
		clueSendConnectTimeout = this.getIfNotEmpty("", this.getMapValueByKey("clueSendConnectTimeout"));
		return clueSendConnectTimeout;
	}

	public String getClueSendReadTimeout() {
		clueSendReadTimeout = this.getIfNotEmpty("", this.getMapValueByKey("clueSendReadTimeout"));
		return clueSendReadTimeout;
	}

	public String getSaleClueSendUrl() {
		saleClueSendUrl = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendUrl"));
		return saleClueSendUrl;
	}

	public String getSaleClueSendTokenUrl() {
		saleClueSendTokenUrl = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendTokenUrl"));
		return saleClueSendTokenUrl;
	}

	public String getSaleClueSendCharset() {
		saleClueSendCharset = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendCharset"));
		return saleClueSendCharset;
	}

	public String getSaleClueSendConnectTimeout() {
		saleClueSendConnectTimeout = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendConnectTimeout"));
		return saleClueSendConnectTimeout;
	}

	public String getSaleClueSendReadTimeout() {
		saleClueSendReadTimeout = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendReadTimeout"));
		return saleClueSendReadTimeout;
	}

	public String getSaleClueSendHeadSystem() {
		saleClueSendHeadSystem = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendHeadSystem"));
		return saleClueSendHeadSystem;
	}

	public String getSaleClueSendHeadService() {
		saleClueSendHeadService = this.getIfNotEmpty("", this.getMapValueByKey("saleClueSendHeadService"));
		return saleClueSendHeadService;
	}

	public String getWxcsiUserAgent() {
		wxcsiUserAgent = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiUserAgent"));
		return wxcsiUserAgent;
	}

	public String getWxcsiCallType() {
		wxcsiCallType = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiCallType"));
		return wxcsiCallType;
	}

	public String getWxcsiDealProgressAdd() {
		wxcsiDealProgressAdd = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiDealProgressAdd"));
		return wxcsiDealProgressAdd;
	}

	public String getWxcsiServerTitle() {
		wxcsiServerTitle = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiServerTitle"));
		return wxcsiServerTitle;
	}

	public String getWxcsiConsultDesc() {
		wxcsiConsultDesc = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiConsultDesc"));
		return wxcsiConsultDesc;
	}

	public String getWxcsiServerAnswer() {
		wxcsiServerAnswer = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiServerAnswer"));
		return wxcsiServerAnswer;
	}

	public String getWxcsiRemark() {
		wxcsiRemark = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiRemark"));
		return wxcsiRemark;
	}

	public String getWxcsiCustRequirement() {
		wxcsiCustRequirement = this.getIfNotEmpty("", this.getMapValueByKey("wxcsiCustRequirement"));
		return wxcsiCustRequirement;
	}

	public String getEvaBuilder() {
		evaBuilder = this.getIfNotEmpty("", this.getMapValueByKey("evaBuilder"));
		return evaBuilder;
	}
	public String getEvaAppkey() {
		evaAppkey = this.getIfNotEmpty("", this.getMapValueByKey("evaAppkey"));
		return evaAppkey;
	}
	public String getEvaAppid() {
		evaAppid = this.getIfNotEmpty("", this.getMapValueByKey("evaAppid"));
		return evaAppid;
	}
	public String getEvaZxBigType() {
		evaZxBigType = this.getIfNotEmpty("", this.getMapValueByKey("evaZxBigType"));
		return evaZxBigType;
	}
	public String getEvaZxType() {
		evaZxType = this.getIfNotEmpty("", this.getMapValueByKey("evaZxType"));
		return evaZxType;
	}
	public String getEvaPicAdd() {
		evaPicAdd = this.getIfNotEmpty("", this.getMapValueByKey("evaPicAdd"));
		return evaPicAdd;
	}
	public String getEvaDealProgressAdd() {
		evaDealProgressAdd = this.getIfNotEmpty("", this.getMapValueByKey("evaDealProgressAdd"));
		return evaDealProgressAdd;
	}
	public String getCipjob007MaxDay() {
		cipjob007MaxDay = this.getIfNotEmpty("", this.getMapValueByKey("cipjob007MaxDay"));
		return cipjob007MaxDay;
	}
	public String getXfSitSwitch() {
		xfSitSwitch = this.getIfNotEmpty("", this.getMapValueByKey("xfSitSwitch"));
		return xfSitSwitch;
	}
	public String getXfSitPhone() {
		xfSitPhone = this.getIfNotEmpty("", this.getMapValueByKey("xfSitPhone"));
		return xfSitPhone;
		
	}
	public String getAccidentClassCodeTwo() {
		accidentClassCodeTwo = this.getIfNotEmpty("", this.getMapValueByKey("accidentClassCodeTwo"));
		return accidentClassCodeTwo;
	}
	public String getPublicClassCodeTwo() {
		publicClassCodeTwo = this.getIfNotEmpty("", this.getMapValueByKey("publicClassCodeTwo"));
		return publicClassCodeTwo;
	}
	public String getXiaoiBaseUrl() {
		xiaoiBaseUrl = this.getIfNotEmpty("", this.getMapValueByKey("xiaoiBaseUrl"));
		return xiaoiBaseUrl;
	}
	public String getXiaoiAesKey() {
		xiaoiAesKey = this.getIfNotEmpty("", this.getMapValueByKey("xiaoiAesKey"));
		return xiaoiAesKey;
	}
	
	public String getXfNetUrlprefix() {
		xfNetUrlprefix = this.getIfNotEmpty("", this.getMapValueByKey("xfNetUrlprefix"));
		return xfNetUrlprefix;
	}
	public String getQualityTsClassCode() {
		qualityTsClassCode = this.getIfNotEmpty("", this.getMapValueByKey("qualityTsClassCode"));
		return qualityTsClassCode;
	}
	public String getPartTsClassCode() {
		partTsClassCode = this.getIfNotEmpty("", this.getMapValueByKey("partTsClassCode"));
		return partTsClassCode;
	}
	public String getZdtsCode() {
		zdtsCode = this.getIfNotEmpty("", this.getMapValueByKey("zdtsCode"));
		return zdtsCode;
	}

	public String getBaiduReverseGeocoding() {
		baiduReverseGeocoding = this.getIfNotEmpty("", this.getMapValueByKey("baiduReverseGeocoding"));
		return baiduReverseGeocoding;
	}

	public String getServerOrderHfContent() {
		serverOrderHfContent = this.getIfNotEmpty("", this.getMapValueByKey("serverOrderHfContent"));
		return serverOrderHfContent;
	}
	public String getServerOrderHfMsgAccountN() {
		serverOrderHfMsgAccountN = this.getIfNotEmpty("", this.getMapValueByKey("serverOrderHfMsgAccountN"));
		return serverOrderHfMsgAccountN;
	}
	public String getServerOrderHfMsgAccountV() {
		serverOrderHfMsgAccountV = this.getIfNotEmpty("", this.getMapValueByKey("serverOrderHfMsgAccountV"));
		return serverOrderHfMsgAccountV;
	}
	
	public String getExportMaxNum() {
		exportMaxNum = this.getIfNotEmpty("", this.getMapValueByKey("exportMaxNum"));
		return exportMaxNum;
	}
	
	public String getExportESMaxNum() {
		exportESMaxNum = this.getIfNotEmpty("", this.getMapValueByKey("exportESMaxNum"));
		return exportESMaxNum;
	}
	public String getE3sInterfaceUrl() {
		e3sInterfaceUrl = this.getIfNotEmpty("", this.getMapValueByKey("e3sInterfaceUrl"));
		return e3sInterfaceUrl;
	}
	
	public String getE3sCode() {
		e3sCode = this.getIfNotEmpty("", this.getMapValueByKey("e3sCode"));
		return e3sCode;
	}
	
	public String getE3sVisitSource() {
		e3sVisitSource = this.getIfNotEmpty("", this.getMapValueByKey("e3sVisitSource"));
		return e3sVisitSource;
	}
	
	public String getE3sMaxNum() {
		e3sMaxNum = this.getIfNotEmpty("", this.getMapValueByKey("e3sMaxNum"));
		return e3sMaxNum;
	}
	
	public String getEsIp() {
		esIp = this.getIfNotEmpty("", this.getMapValueByKey("esIp"));
		return esIp;
	}
	
	public String getEsPort() {
		esPort = this.getIfNotEmpty("", this.getMapValueByKey("esPort"));
		return esPort;
	}
	
	public String getEsUserName() {
		esUserName = this.getIfNotEmpty("", this.getMapValueByKey("esUserName"));
		return esUserName;
	}
	
	public String getEsPassword() {
		esPassword = this.getIfNotEmpty("", this.getMapValueByKey("esPassword"));
		return esPassword;
	}
//	public String getRiskWorkGroup() {
//		riskWorkGroup = this.getIfNotEmpty("", this.getMapValueByKey("riskWorkGroup"));
//		return riskWorkGroup;
//	}
//	public String getDfnWorkGroup() {
//		dfnWorkGroup = this.getIfNotEmpty("", this.getMapValueByKey("dfnWorkGroup"));
//		return dfnWorkGroup;
//	}
//	public String getGhWorkGroup() {
//		ghWorkGroup = this.getIfNotEmpty("", this.getMapValueByKey("ghWorkGroup"));
//		return ghWorkGroup;
//	}
	public String getExportMntDir() {
		exportMntDir = this.getIfNotEmpty("", this.getMapValueByKey("exportMntDir"));
		return exportMntDir;
	}
	public String getWorkGroupPopConfig() {
		workGroupPopConfig = this.getIfNotEmpty("", this.getMapValueByKey("workGroupPopConfig"));
		return workGroupPopConfig;
	}
}
