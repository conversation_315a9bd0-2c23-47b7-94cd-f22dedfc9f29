package com.ly.mp.busicen.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.ly.mp.busicen.common.handler.MetaHandler;

/**
 * mybatis配置
 * <AUTHOR>
 * 日期：2019-07-11
 */
@Configuration
public class MybatisConfig {
	/**
	 * 自动填充元字段
	 */
	@Bean
	public GlobalConfig globalConfig(MetaHandler metaHandler) {
		GlobalConfig config = new GlobalConfig();
		config.setMetaObjectHandler(metaHandler);
		return config;
	}
	
	@Bean
	public MetaHandler metaHandler() {
		return new MetaHandler();
	}
}
