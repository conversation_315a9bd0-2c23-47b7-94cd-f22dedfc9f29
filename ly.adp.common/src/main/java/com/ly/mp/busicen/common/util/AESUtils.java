package com.ly.mp.busicen.common.util;

import java.io.UnsupportedEncodingException;
import java.security.Key;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AESUtils {
	private static Logger logger = LoggerFactory.getLogger(AESUtils.class);

	// 算法名称
	final static String KEY_ALGORITHM = "AES";

	// 加解密算法/模式/填充方式
	final static String algorithmStr = "AES/CBC/PKCS7Padding";

	/**
	 * 加密方法
	 *
	 * @param content  要加密的字符串
	 * @param keyBytes 加密密钥
	 * @return
	 */
	public static String encrypt(String input, String key, String iv) {
		byte[] encryptedText = null;
		try {
			Security.addProvider(new BouncyCastleProvider());
			// 转化成JAVA的密钥格式
			Key secKey = new SecretKeySpec(getSHA256(key.getBytes()), KEY_ALGORITHM);

			// 初始化cipher
			Cipher cipher = Cipher.getInstance(algorithmStr, "BC");

			byte[] btIV = getIV(iv);

			cipher.init(Cipher.ENCRYPT_MODE, secKey, new IvParameterSpec(btIV));
			encryptedText = cipher.doFinal(input.getBytes());
		} catch (Exception e) {
			logger.error("加密出错，明文：" + input + "，私有秘钥：" + key + "，公有秘钥:" + iv, e);
			return "";
		}
		return Base64.getEncoder().encodeToString(encryptedText);
	}

	/**
	 * 解密方法
	 *
	 * @param encryptedData 要解密的字符串
	 * @param keyBytes      解密密钥
	 * @return
	 */
	public static String decrypt(String input, String key, String iv) {
		byte[] decryptedText = null;

		try {
			Security.addProvider(new BouncyCastleProvider());

			// 转化成JAVA的密钥格式
			Key secKey = new SecretKeySpec(getSHA256(key.getBytes()), KEY_ALGORITHM);
			Cipher cipher = Cipher.getInstance(algorithmStr, "BC");

			byte[] btIV = getIV(iv);

			cipher.init(Cipher.DECRYPT_MODE, secKey, new IvParameterSpec(btIV));
			
			byte[] encBytes = Base64.getDecoder().decode(input);
			decryptedText = cipher.doFinal(encBytes);
		} catch (Exception ex) {
			logger.error("解密出错，密文：" + input + "，私有秘钥：" + key + "，公有秘钥:" + iv, ex);
			return "";
		}
		return new String(decryptedText);
	}

	private static byte[] getIV(String iv) throws UnsupportedEncodingException, NoSuchAlgorithmException {
		byte[] oldBtIV = getSHA256(iv.getBytes());
		byte[] btIV = new byte[16];
		System.arraycopy(oldBtIV, 0, btIV, 0, 8); // 取原数组前8个字节
		System.arraycopy(oldBtIV, 24, btIV, 8, 8);// 取原数组后8个字节
		return btIV;
	}

	private static byte[] getSHA256(byte[] data) throws UnsupportedEncodingException, NoSuchAlgorithmException {
		MessageDigest messageDigest;
		messageDigest = MessageDigest.getInstance("SHA-256");
		messageDigest.update(data);
		return messageDigest.digest();
	}

	public static void main(String[] args) {
		// 加解密 密钥
		String content = "1abc2";
		System.out.println("加密前的内容：" + content);
		// 加密方法
//		String enc = AESUtils.encrypt(content, "LYvcp@2021Y6m20d", "LYvcp@2021Y6m20d");
		String enc = AESUtils.encrypt(content, "1", "LYvcp@2021Y6m20d");
		System.out.println("加密后的内容：" + enc);
		// 解密方法
//		String dec = AESUtils.decrypt(enc, "LYvcp@2021Y6m20d", "LYvcp@2021Y6m20d");
		String dec = AESUtils.decrypt(enc, "1", "LYvcp@2021Y6m20d");
		System.out.println("解密后的内容：" + dec);
	}
}