package com.ly.mp.busicen.common.util;

import java.util.Calendar;
import java.util.Date;

import org.hibernate.validator.internal.util.StringHelper;

import com.alibaba.fastjson.JSONObject;

public class LogBuilder {
	
	// 模块枚举
	public enum Module {
		CIP,
		CSC,
		EAP,
		ORC,
		PRC,
		SCC,
		USC,
		XAPI,
		MSG,
		MEMBER,
		SYS
	}
	
	/**
	 * 日志字符转业务日志格式-业务类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getBizParams(Module m, String opt, String logInfo) {
		return getBizParams(m, opt, "", logInfo);
	}
	
	/**
	 * 日志字符转业务日志格式-业务类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param orderCode 业务单号
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getBizParams(Module m, String opt, String orderCode, String logInfo) {
		return getParams("biz", m, opt, orderCode, -1, logInfo);
	}
	
	/**
	 * 日志字符转业务日志格式-错误类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getErrParams(Module m, String opt, String logInfo) {
		return getErrParams(m, opt, "", logInfo);
	}
	
	/**
	 * 日志字符转业务日志格式-错误类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param orderCode 业务单号
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getErrParams(Module m, String opt, String orderCode, String logInfo) {
		return getParams("err", m, opt, "", -1, logInfo);
	}
	
	/**
	 * 日志字符转业务日志格式-性能类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param spendTime 耗时（单位：ms，0：不记录）
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getPfmParams(Module m, String opt, long spendTime, String logInfo) {
		return getPfmParams(m, opt, spendTime, "", logInfo);
	}
	
	/**
	 * 日志字符转业务日志格式-性能类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param spendTime 耗时（单位：ms，0：不记录）
	 * @param orderCode 业务单号
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getPfmParams(Module m, String opt, long spendTime, String orderCode, String logInfo) {
		return getParams("pfm", m, opt, orderCode, spendTime, logInfo);
	}

	/**
	 * 日志字符转业务日志格式-性能类型
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @param orderCode 业务单号
	 * @param logInfo 日志信息
	 * @return
	 */
	public static String getPfmParams(Module m, String opt, Date startTime, Date endTime, String orderCode, String logInfo) {
		Calendar c1 = Calendar.getInstance();
	    c1.setTime(startTime);
	    Calendar c2 = Calendar.getInstance();
	    c2.setTime(endTime);
		long spendTime = c2.getTimeInMillis() - c1.getTimeInMillis();
		return getParams("pfm", m, opt, orderCode, spendTime, logInfo);
	}
	
	/**
	 * 日志字符转业务日志格式
	 *
	 * @param logType 日志类型（biz(业务),pfm(性能),err(错误)）
	 * @param m 模块
	 * @param opt 操作（如：潜客导入）
	 * @param orderCode 业务单号
	 * @param spendTime 耗时（单位：ms，0：不记录）
	 * @param logInfo 日志信息
	 * @return
	 */
	private static String getParams(String logType, Module m, String opt, String orderCode, long spendTime, String logInfo) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("logtype", logType);
		jsonObject.put("module", m.toString());
		jsonObject.put("operate", opt);
		if (!StringHelper.isNullOrEmptyString(orderCode)) {
			jsonObject.put("ordercode", orderCode);
		}
		if (spendTime >= 0) {
			jsonObject.put("spendtime", spendTime);
		}
		jsonObject.put("loginfo", logInfo);
		return jsonObject.toString();
	}
}