package com.ly.mp.busicen.common.util;

import java.util.List;
import java.util.Map;

public class BaseSqlSegment extends SqlSegment {
	protected String key;

	public BaseSqlSegment(String dicKey, String sqlClause) {
		super(sqlClause);
		this.key = dicKey;
	}

	@Override
	public void parse(Map<String, Object> dicParam, StringBuilder builder) {

		if (dicParam.containsKey(key)) {
			Object paraValue = dicParam.get(key);
			// 1. in语句 中的参数(List类型) 是否包含元素, 2. 其它类型参数(单值)
			if (paraValue != null && (paraValue instanceof List && !((List) paraValue).isEmpty()
					|| !(paraValue instanceof List) && dicParam.get(key).toString().length() > 0)) {
				builder.append(this.sqlClause);
				builder.append(" ");
			}
		}
	}
}
