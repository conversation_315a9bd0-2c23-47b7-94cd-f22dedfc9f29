package com.ly.mp.busicen.common.context;

import io.seata.core.context.RootContext;
import io.seata.tm.api.TransactionalExecutor;
import io.seata.tm.api.TransactionalTemplate;
import io.seata.tm.api.transaction.TransactionInfo;

import java.util.function.Supplier;


public class GtsTemplate {
	
	public static final int DEFAULT_TIME_OUT = 60000;

	/**
	 * 事务挂起
	 * @param <T>
	 * @param supplier
	 * @return
	 */
	public static <T> T breakTrans(Supplier<T> supplier) {
		boolean inGts = RootContext.inGlobalTransaction();
		String xid=null;
		if (inGts) {
			xid = RootContext.unbind();
		}
		try {
			return supplier.get();
		} catch (Exception e) {
			throw e;
		}finally {
			//事务恢复
			if (inGts) {
				RootContext.bind(xid);
			}
		}
	}

	/**
	 * 事务挂起模板方法
	 * @param supplier
	 */
	public static void breakTrans(SupplierTemp supplier) {
		boolean inGts = RootContext.inGlobalTransaction();
		String xid=null;
		if (inGts) {
			xid = RootContext.unbind();
		}
		try {
			supplier.doing();
		} catch (Exception e) {
			throw e;
		}finally {
			//事务恢复
			if (inGts) {
				RootContext.bind(xid);
			}
		}
	}

	/**
	 * 事务模板方法
	 * @param <T>
	 * @param name 事务名称
	 * @param timeOut 超时时间
	 * @param supplier 委托方法
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> T doTrans(String name,int timeOut,Supplier<T> supplier) {
		TransactionalTemplate transactionalTemplate = new TransactionalTemplate();
		try {
			return (T) transactionalTemplate.execute(new BucnTransactionalExecutor<T>(name, timeOut, supplier));
		}catch (RuntimeException e) {
			throw e;
		}catch(TransactionalExecutor.ExecutionException e) {
			if(e.getOriginalException()!=null && e.getOriginalException() instanceof RuntimeException) {
				throw (RuntimeException)e.getOriginalException();
			}
			throw new RuntimeException(e.getOriginalException());
		} catch (Throwable e) {
			throw new RuntimeException(e);
		}
	}

	public static <T> T doTrans(String name,Supplier<T> supplier) {
		return doTrans(name, DEFAULT_TIME_OUT, supplier);
	}

	@FunctionalInterface
	public static interface SupplierTemp{
		void doing();
	}


	public static class BucnTransactionalExecutor<T> implements TransactionalExecutor {

		public BucnTransactionalExecutor(String name,int timeOut,Supplier<T> supplier) {
			this.name = name;
			this.timeOut=timeOut;
			this.supplier=supplier;
		}

		Supplier<T> supplier;
		String name;
		int timeOut;

		@Override
		public Object execute() throws Throwable {
			if (supplier!=null) {
				return supplier.get();
			}else {
				return  null;
			}

		}

		@Override
		public TransactionInfo getTransactionInfo() {
			TransactionInfo ti= new TransactionInfo();
			ti.setName(name);
			ti.setTimeOut(timeOut);
			return ti;
		}

	}

}
