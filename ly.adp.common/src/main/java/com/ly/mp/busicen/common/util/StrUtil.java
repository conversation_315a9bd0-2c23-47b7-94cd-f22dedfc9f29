package com.ly.mp.busicen.common.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

import com.ly.mp.component.helper.StringHelper;



public class StrUtil {
	/**
	 *  日期转LocalDateTime
	 */
	public static LocalDateTime convStrTimeToLdt(String str) {
		if (StringHelper.IsEmptyOrNull(str) || "null".equals(str)) {
			return null;
		}
		// 由大到小校验 (由于SimpleDateFormat特性)
		Map<String,SimpleDateFormat> convTimeType=new LinkedHashMap<>();
		// key作为长度判断 value为实际转换的格式
		convTimeType.put("yyyy-MM-ddTHH:mm:ss.SSS",new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS"));
		convTimeType.put("yyyy-MM-dd HH:mm:ss.SSS",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS"));
		convTimeType.put("yyyy-MM-dd HH:mm:ss",new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
		convTimeType.put("yyyyMMddHHmmss",new SimpleDateFormat("yyyyMMddHHmmss"));
		convTimeType.put("yyyy-MM-dd",new SimpleDateFormat("yyyy-MM-dd"));
		convTimeType.put("yyyyMMdd",new SimpleDateFormat("yyyyMMdd"));
		convTimeType.put("yyyy-MM",new SimpleDateFormat("yyyy-MM"));
		convTimeType.put("yyyyMM",new SimpleDateFormat("yyyyMM"));
		convTimeType.put("yyyy",new SimpleDateFormat("yyyy"));
		// 严格模式
		convTimeType.forEach((k,v)->{
			v.setLenient(false);
		});
		LocalDateTime result=null;
		Iterator<Entry<String, SimpleDateFormat>> convIte = convTimeType.entrySet().iterator();
		while(convIte.hasNext()) {
			Entry<String, SimpleDateFormat> next = convIte.next();
			try {
				// 跳过不可能匹配的格式
				if (next.getKey().length()!=str.length()) {
					continue;
				}
				result=LocalDateTime.ofInstant(next.getValue().parse(str).toInstant(), ZoneId.systemDefault());
				break;
			} catch (Exception e) {
			}
		}
		return result;
	}

	/**
	 * 转驼峰
	 *
	 * @param name
	 * @return
	 */
	public static String findProperty(String name) {
		if ((name.charAt(0) >= 'A' && name.charAt(0) <= 'Z') || name.contains("_")) {
			return underlineToCamelhump(name);
		}
		return name;
	}

	private static String underlineToCamelhump(String inputString) {
		StringBuilder sb = new StringBuilder();

		boolean nextUpperCase = false;
		for (int i = 0; i < inputString.length(); i++) {
			char c = inputString.charAt(i);
			if (c == '_') {
				if (sb.length() > 0) {
					nextUpperCase = true;
				}
			} else {
				if (nextUpperCase) {
					sb.append(Character.toUpperCase(c));
					nextUpperCase = false;
				} else {
					sb.append(Character.toLowerCase(c));
				}
			}
		}
		return sb.toString();
	}
	
	public static Integer toInt(Object obj,int defaultInt) {
		int result=defaultInt;
		if (!StringHelper.IsEmptyOrNull(obj)) {
			try {
				result=Integer.parseInt(String.valueOf(obj));
			} catch (Exception e) {
			}
		}
		return result;
	}

	public static Integer toInt(Object obj) {
		return toInt(obj,1);
	}
}
