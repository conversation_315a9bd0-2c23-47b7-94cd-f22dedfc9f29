package com.ly.mp.busicen.common.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.ly.mp.component.entities.EntityResult;




/**
 * 统一拦截异常
 * <AUTHOR>
 *
 */
@ControllerAdvice
@ResponseBody
public class WebExceptionHandle {
	private static Logger logger = LoggerFactory.getLogger(WebExceptionHandle.class);
    /**
     * 400 - Bad Request
     */
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public EntityResult<String> handleHttpMessageNotReadableException(HttpMessageNotReadableException e) {
    	logger.error("参数解析失败", e);
        EntityResult<String> rs = createResult("参数解析失败");
        return rs;
    }

	private EntityResult<String> createResult(String msg) {
		
        EntityResult<String> rs = new EntityResult<>();
        rs.setResult("0");
        rs.setMsg(msg);
		return rs;
	}
    
    /**
     * 405 - Method Not Allowed
     */
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public EntityResult<String> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException e) {
        logger.error("不支持当前请求方法", e);
        EntityResult<String> rs = createResult("不支持当前请求方法");
        return rs;
    }

    /**
     * 415 - Unsupported Media Type
     */
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public EntityResult<String> handleHttpMediaTypeNotSupportedException(Exception e) {
        logger.error("不支持当前媒体类型", e);
        EntityResult<String> rs = createResult("不支持当前媒体类型");
        return rs;
    }

    /**
     * 500 - Internal Server Error
     */
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Exception.class)
    public EntityResult<String> handleException(Exception e) {
//        if (e instanceof BusinessException){
//            return ServiceResponseHandle.failed("BUSINESS_ERROR", e.getMessage());
//        }
    	
        
        logger.error("因网络错误而导致此操作无法完成", e);
      
        EntityResult<String> rs = createResult("因网络错误而导致此操作无法完成");
        return rs;
    }
}
