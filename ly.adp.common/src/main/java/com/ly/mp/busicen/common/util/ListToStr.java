/**   
* @Description: 该函数的功能描述
*
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年8月12日 下午10:07:48 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年8月12日     ly-wyifan           v1.0.0               修改原因
*/
package com.ly.mp.busicen.common.util;

import java.util.ArrayList;
import java.util.List;

import com.ly.mp.component.helper.StringHelper;


/**   
* @Description: 该函数的功能描述
*
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年8月12日 下午10:07:48 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年8月12日     ly-wyifan           v1.0.0               修改原因
*/
public class ListToStr {

	 List<String> list = new ArrayList<String>();
	  String spiletor = ",";
	
	public ListToStr add(String str) {
		list.add(str);
		return this;
	}
	
	public ListToStr spiltor(String sp) {
		spiletor = sp;
		return this;
	}
	
	@Override
	public String toString() {
		StringBuffer buffer = new StringBuffer();
		if (StringHelper.IsEmptyOrNull(list)) {
			return "";
		}
		if (list.size() == 1 && "".equals(list.get(0))) {
			return "";
		}
		for (String string : list) {
			buffer.append(string+spiletor);
		}
		String substring = buffer.toString().substring(0,buffer.length()-1);
		return substring;
	}
}
