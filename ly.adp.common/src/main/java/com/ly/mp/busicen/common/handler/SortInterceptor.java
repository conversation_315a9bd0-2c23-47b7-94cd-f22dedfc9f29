package com.ly.mp.busicen.common.handler;

import java.util.List;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.ly.mp.busicen.common.query.BaseQuery;



/**
* <AUTHOR>
* @version 创建时间 ：2018年11月20日 下午7:45:39
*/
@Component
@Aspect
public class SortInterceptor {

	@Before("execution(public * com.ly.mp.busicen.cm.service.*.query*(..))")
	public void processSort(JoinPoint jp) {
		Object[] args = jp.getArgs();
		if(null != args[0] && args[0] instanceof BaseQuery) {
			BaseQuery<?> query = (BaseQuery<?>) args[0];
			List<OrderItem> sortFields = query.getSortfields();
			if(StringUtils.isEmpty(sortFields)) {
				OrderItem sortField=new OrderItem();
				sortField.setColumn("LAST_UPDATED_DATE");
				query.setSortfields(sortFields);
			}
		}
	}
}
