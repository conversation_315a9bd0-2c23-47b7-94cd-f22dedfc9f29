package com.ly.mp.busicen.common.util;

import java.util.Map;

public class LikeSqlSegment extends BaseSqlSegment {
	private PercentSignStyle percentSignStyle = PercentSignStyle.Both;

	public LikeSqlSegment(String dicKey, String sqlClause, PercentSignStyle percentSignStyle) {
		super(dicKey, sqlClause);
		this.percentSignStyle = percentSignStyle;
	}

	@Override
	public void parse(Map<String, Object> dicParam, StringBuilder builder) {
		if (dicParam.containsKey(key) && dicParam.get(key) != null && dicParam.get(key).toString().length() > 0) {
			
			// 更改参数值，前后增加%
			dicParam.put(key, addPercentSign(dicParam.get(key).toString()));
			
			// 将 LIKE或like 替换成=
			if (this.percentSignStyle == PercentSignStyle.None) {
				String sql = this.sqlClause.replace("LIKE", "=");
				sql = sql.replace("like", "=");
				builder.append(sql);
			} else {
				builder.append(this.sqlClause);
			}
			
			builder.append(" ");
		}
	}

	// 在参数前后增加%
	private String addPercentSign(String val) {
		String retVal = "";
		if (this.percentSignStyle == PercentSignStyle.Both) {
			retVal = "%" + val + "%";
		} else if (this.percentSignStyle == PercentSignStyle.Left) {
			retVal = "%" + val;
		} else if (this.percentSignStyle == PercentSignStyle.Right) {
			retVal = val + "%";
		} else if (this.percentSignStyle == PercentSignStyle.None) {
			retVal = val;
		} else {
			retVal = val;
		}

		return retVal;
	}
}
