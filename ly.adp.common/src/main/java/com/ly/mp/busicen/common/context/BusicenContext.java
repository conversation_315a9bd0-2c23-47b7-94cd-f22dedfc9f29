package com.ly.mp.busicen.common.context;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.helper.CacheHelper;
import com.ly.mp.component.helper.SessionHelper;

public class BusicenContext {
	
	public static final String BUSICEN_QUERY_ORDERS = "busicen_query_orders";
	public static final String BUSICEN_ERRORS = "busicen_errors";
	public static final String BUSICEN_RBG = "busicen_rbg";
	public static final String BUSICEN_TRACE = "busicen_trace";
	public static final String BUSICEN_DOMAIN = "busicen_domain";
	public static final String BUSICEN_EXCEL = "busicen_excel";
	
	private static List<Consumer<String>> initCallBacks=new ArrayList<>();
	
	private static List<Consumer<String>> releaseCallBacks = new ArrayList<>();
	
	private static final ThreadLocal<Map<String, Object>> CONTEXT=new ThreadLocal<Map<String, Object>>();
	
	private static final ThreadLocal<String> TOKEN = new ThreadLocal<String>();
	
	
	static {
		addInitCallBack(tk->{
			//UserEntity userEntity = (UserEntity) SessionHelper.get(tk);
			//CURRENTUSER.set(userEntity);
		});
	}
	
	public static void addInitCallBack(Consumer<String> consumer) {
		if(!initCallBacks.contains(consumer)) {
			initCallBacks.add(consumer);
		}
	}
	
	public static void addReleaseCallBack(Consumer<String> consumer) {
		if(!releaseCallBacks.contains(consumer)) {
			releaseCallBacks.add(consumer);
		}
	}

	public static UserBusiEntity getCurrentUserBusiInfo(String token) {

		UserBusiEntity busiUserEntity =new UserBusiEntity();
        Object objRes= SessionHelper.get("busicen"+ token);
        if(objRes!=null)
        {
        	UserBusiEntity objUserRes=(UserBusiEntity) objRes;
        	busiUserEntity.setBelongFactoryCode(objUserRes.getBelongFactoryCode());
        	busiUserEntity.setBelongFactoryId(objUserRes.getBelongFactoryId());
        	busiUserEntity.setBrandCode(objUserRes.getBrandCode());
        	busiUserEntity.setCompanyID(objUserRes.getCompanyID());
        	busiUserEntity.setDlrCode(objUserRes.getDlrCode());
        	busiUserEntity.setDlrID(objUserRes.getDlrID());
        	busiUserEntity.setDlrName(objUserRes.getDlrName());
        	busiUserEntity.setDlrShortName(objUserRes.getDlrShortName());
        	busiUserEntity.setDlrOrgType(objUserRes.getDlrOrgType());
        	busiUserEntity.setDlrStatus(objUserRes.getDlrStatus());
        	busiUserEntity.setEmpID(objUserRes.getEmpID());
        	busiUserEntity.setEmpName(objUserRes.getEmpName());
        	busiUserEntity.setGroupCode(objUserRes.getGroupCode());
        	busiUserEntity.setGroupID(objUserRes.getGroupID());
        	busiUserEntity.setOemCode(objUserRes.getOemCode());
        	busiUserEntity.setOemID(objUserRes.getOemID());
        	busiUserEntity.setOrgCode(objUserRes.getOrgCode());
        	busiUserEntity.setOrgID(objUserRes.getOrgID());
        	busiUserEntity.setOrgName(objUserRes.getOrgName());
        	busiUserEntity.setOrgType(objUserRes.getOrgType());
        	busiUserEntity.setParentDlrCode(objUserRes.getParentDlrCode());
        	busiUserEntity.setParentDlrID(objUserRes.getParentDlrID());
        	busiUserEntity.setParentDlrName(objUserRes.getParentDlrName());
        	busiUserEntity.setPartId(objUserRes.getPartId());
        	busiUserEntity.setPosCode(objUserRes.getPosCode());
        	busiUserEntity.setPosID(objUserRes.getPosID());
        	busiUserEntity.setSolutionId(objUserRes.getSolutionId());
        	busiUserEntity.setUserID(objUserRes.getUserID());
        	busiUserEntity.setUserName(objUserRes.getUserName());
        	busiUserEntity.setMobile(objUserRes.getMobile());
        	busiUserEntity.setStationId(objUserRes.getStationId());
        	busiUserEntity.setStationName(objUserRes.getStationName());
        }
        else
        {
			busiUserEntity.setUserID("1");
			busiUserEntity.setUserName("1");
			busiUserEntity.setEmpID("1");
			busiUserEntity.setEmpName("1");
			busiUserEntity.setDlrCode("1");
			busiUserEntity.setDlrName("1");
			busiUserEntity.setDlrShortName("1");
			busiUserEntity.setDlrID("1");
			busiUserEntity.setParentDlrCode("1");
			busiUserEntity.setParentDlrName("1");
			busiUserEntity.setParentDlrID("1");
			busiUserEntity.setDlrOrgType("1");
			busiUserEntity.setDlrStatus("1");
			busiUserEntity.setOrgType("1");
			busiUserEntity.setOrgCode("1");
			busiUserEntity.setOrgID("1");
			busiUserEntity.setOrgName("1");
			busiUserEntity.setPosID("1");
			busiUserEntity.setPosCode("1");
			busiUserEntity.setBelongFactoryId("1");
			busiUserEntity.setBelongFactoryCode("1");
			busiUserEntity.setSolutionId("1");
			busiUserEntity.setCompanyID("1");	
        	busiUserEntity.setMobile("1");
        	busiUserEntity.setStationId("1");
        	busiUserEntity.setStationName("1");
        }	
		return busiUserEntity;
	}
	
	/* 
	 * 
	 * 此方法返回的用户信息是虚拟用户，不是真实用户，是给定时任务调用，其他方法不要使用！！！
	 * 
	 * */
	public static UserBusiEntity getCurrentUserBusiInfo() {
//		UserEntity busiUser= getCurrentUser();
		UserBusiEntity busiUserEntity =new UserBusiEntity();
//		if(busiUser!=null)
//		{
			busiUserEntity.setUserID("1");
			busiUserEntity.setUserName("1");
			busiUserEntity.setEmpID("1");
			busiUserEntity.setEmpName("1");
			busiUserEntity.setDlrCode("1");
			busiUserEntity.setDlrName("1");
			busiUserEntity.setDlrID("1");
			busiUserEntity.setParentDlrCode("1");
			busiUserEntity.setParentDlrName("1");
			busiUserEntity.setParentDlrID("1");
			busiUserEntity.setDlrOrgType("1");
			busiUserEntity.setDlrStatus("1");
			busiUserEntity.setOrgType("1");
			busiUserEntity.setOrgCode("1");
			busiUserEntity.setOrgID("1");
			busiUserEntity.setOrgName("1");
			busiUserEntity.setPosID("1");
			busiUserEntity.setPosCode("1");
			busiUserEntity.setBelongFactoryId("1");
			busiUserEntity.setBelongFactoryCode("1");
			busiUserEntity.setSolutionId("1");
			busiUserEntity.setCompanyID("1");
		
		return busiUserEntity;
	}
	
	
	public static void setToken(String token) {
		TOKEN.set(token);//设置token
		//执行初始化回调
		if(initCallBacks!=null) {
			initCallBacks.forEach(m->{
				m.accept(token);
			});
		}
	}
	
	public static String getToken() {
		return TOKEN.get();
	}
	
	public static Map<String, Object> getContext(){
		return CONTEXT.get();
	}
	
	public static void setContext(Map<String, Object> context){
		CONTEXT.set(context);
	}

	public static void remove() {
		if(releaseCallBacks!=null) {
			releaseCallBacks.forEach(m->{
				m.accept(getToken());
			});
		}
		
		CONTEXT.remove();
		TOKEN.remove();
	}
	
}
