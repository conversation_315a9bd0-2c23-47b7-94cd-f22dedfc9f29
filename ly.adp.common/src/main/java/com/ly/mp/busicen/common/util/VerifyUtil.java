package com.ly.mp.busicen.common.util;

import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.StringHelper;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 校验工具类
 *
 * <AUTHOR>
 * @since 2021-1-4
 */
public class VerifyUtil {

	/**
	 * 判断EntityResult的rows是否为空
	 */
	public static <T> boolean isEntityResultEmpty(EntityResult<T> nResult) {
		if (nResult==null) {
			return true;
		}
		if (nResult.getRows()==null) {
			return true;
		}
		return false;
	}

	/**
	 * 校验EntityResult是否为成功，并且有msg信息
	 */
	public static <T> boolean isEntityResultMsgSuccess(EntityResult<T> nResult) {
		if ("1".equals(nResult.getResult()) && !StringHelper.IsEmptyOrNull(nResult.getMsg())) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 校验optResult是否为成功，并且有msg信息
	 */
	public static boolean isOptResultMsgSuccess(OptResult nResult) {
		if (!StringHelper.IsEmptyOrNull(nResult) && "1".equals(nResult.getResult()) && !StringHelper.IsEmptyOrNull(nResult.getMsg())) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 校验不能同时为空
	 */
	public static void checkAuth(String... str) {
		List<String> list = Arrays.asList(str);
		long count = list.stream().filter(s -> !StringHelper.IsEmptyOrNull(s)).count();
		if (count == 0) {
			throw BusicenException.create("userId与token不能同时为空");
		}
	}

	/**
	 * 校验不能同时为空 自定义异常内容（第一个参数）
	 */
	public static void checkStringNotAllNull(String msg, String... str) {
		List<String> list = Arrays.asList(str);
		long count = list.stream().filter(s -> !StringHelper.IsEmptyOrNull(s)).count();
		if (count == 0) {
			throw BusicenException.create(msg);
		}
	}

	/**
	 * 规则引擎DATACHECK格式的验证 通过返回true
	 *
	 * @return
	 */
	public static boolean xruleDataCheck(Map<String, Object> info) {
		boolean flag = true;
		try {
			if (!StringHelper.IsEmptyOrNull(info.get("flag"))) {
				if (info.get("flag").toString().equalsIgnoreCase("F")) {
					flag = false;
				}
			}
			if (!StringHelper.IsEmptyOrNull(info.get("FLAG"))) {
				if (info.get("FLAG").toString().equalsIgnoreCase("F")) {
					flag = false;
				}
			}
		} catch (Exception e) {
			return flag;
		}
		return flag;
	}

	/**
	 * 规则引擎DATACHECK格式的验证 全部通过返回true 多个校验
	 * @return
	 */
	public static boolean xruleDataCheck(List<Map<String, Object>> infoList) {
		boolean flag = true;
		for (Map<String, Object> info : infoList) {
			try {
				if (!StringHelper.IsEmptyOrNull(info.get("flag"))) {
					if (info.get("flag").toString().equalsIgnoreCase("F")) {
						flag = false;
						break;
					}
				}
				if (!StringHelper.IsEmptyOrNull(info.get("FLAG"))) {
					if (info.get("FLAG").toString().equalsIgnoreCase("F")) {
						flag = false;
						break;
					}
				}
			} catch (Exception e) {
				return flag;
			}
		}
		return flag;
	}

	/**
	 * 验证日期格式
	 *
	 * @param dateStr
	 * @param formatStr
	 * @return
	 */
	public static String checkDateFormat(String dateStr, String formatStr) {
		String result = "";
		try {
			new SimpleDateFormat(formatStr).parse(dateStr);
		} catch (ParseException p) {
			p.printStackTrace();
			result = "传入的日期字段:" + dateStr + "格式错误";
		} catch (Exception e) {
			e.printStackTrace();
			result = "传入的日期格式字符串:" + formatStr + "错误";
		}
		return result;
	}

	/**
	 * 校验map对象是否包含目标字段且不为空值
	 *
	 * @param targetKey
	 * @param map
	 * @return
	 */
	public static String checkRequired(String targetKey, Map<String, Object> map) {
		if (StringHelper.IsEmptyOrNull(map.get(targetKey))) {
			return targetKey + ",";
		} else {
			return "";
		}
	}

	/**
	 * 校验字符串最大长度
	 *
	 * @param str
	 * @param maxLength
	 * @return
	 */
	public static String checkLength(String str, int maxLength) {
		String result = "";
		if (StringHelper.IsEmptyOrNull(str)) {
			result = "该字符串为空";
		} else {
			if (str.length() > maxLength) {
				result = "该字符串超出最大限制长度";
			}
		}
		return result;
	}

	/**
	 * 24小时制 时间校验
	 *
	 * @param time
	 * @return boolean
	 */
	public static boolean checkTime(String time) {
		try {
			String[] temp = time.split(":");
			if ((temp[0].length() == 2 || temp[0].length() == 1) && temp[1].length() == 2) {
				int h, m;
				try {
					h = Integer.parseInt(temp[0]);
					m = Integer.parseInt(temp[1]);
				} catch (NumberFormatException e) {
					return false;
				}
				if (h >= 0 && h <= 24 && m <= 60 && m >= 0) {
					return true;
				}
			}
		} catch (Exception e) {
			return false;
		}
		return false;
	}

	/**
	 * 获取时间差
	 *
	 * @param starttime
	 * @param endtime
	 * @return
	 */
	public static Map<String, Object> getDistanceTimes(String starttime, String endtime) {
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Map<String, Object> map = new HashMap<String, Object>();
		Date one;
		Date two;
		long day = 0;
		long hour = 0;
		long min = 0;
		long sec = 0;
		try {
			one = df.parse(starttime);
			two = df.parse(endtime);
			long time1 = one.getTime();
			long time2 = two.getTime();
			long diff;
			if (time1 < time2) {
				diff = time2 - time1;
			} else {
				diff = time1 - time2;
			}
			day = diff / (24 * 60 * 60 * 1000);
			hour = (diff / (60 * 60 * 1000) - day * 24);
			min = ((diff / (60 * 1000)) - day * 24 * 60 - hour * 60);
			sec = (diff / 1000 - day * 24 * 60 * 60 - hour * 60 * 60 - min * 60);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		map.put("day", day);
		map.put("hour", hour);
		map.put("min", min);
		map.put("sec", sec);
		return map;
	}

	/**
	 * 手机号码脱敏处理
	 *
	 * @param list
	 */
	public static List<Map<String, Object>> phonePutOffSensitive(List<Map<String, Object>> list) {
		StringBuilder phone = null;
		for (Map<String, Object> map : list) {
			if (!StringHelper.IsEmptyOrNull(map.get("phone"))) {
				phone = new StringBuilder(map.get("phone").toString());
				if (phone.length() == 11) {
					map.put("phone", phone.replace(3, 7, "****").toString());
				} else {
					continue;
				}
			}
			if (!StringHelper.IsEmptyOrNull(map.get("contactTel"))) {
				phone = new StringBuilder(map.get("contactTel").toString());
				if (phone.length() == 11) {
					map.put("contactTel", phone.replace(3, 7, "****").toString());
				} else {
					continue;
				}
			}
			if (!StringHelper.IsEmptyOrNull(map.get("backupPhone"))) {
				phone = new StringBuilder(map.get("backupPhone").toString());
				if (phone.length() == 11) {
					map.put("backupPhone", phone.replace(3, 7, "****").toString());
				} else {
					continue;
				}
			}
		}
		return list;
	}

	/**
	 * 手机号码脱敏处理（实体）
	 *
	 * @param list
	 */
	// public static List<ClueSearchOut>
	// phonePutOffSensitiveSele(List<ClueSearchOut> list) {
	// StringBuilder phone = null;
	// for(ClueSearchOut map : list) {
	// if(!StringHelper.IsEmptyOrNull(map.getPhone())) {
	// phone = new StringBuilder(map.getPhone().toString());
	// if(phone.length() == 11 ) {
	// map.setPhone(phone.replace(3, 7, "****").toString());
	// } else if(phone.length() ==7||phone.length() ==8) {
	// map.setPhone(phone.replace(3, 5, "**").toString());
	// }
	// else {
	// continue;
	// }
	// }
	// if(!StringHelper.IsEmptyOrNull(map.getContactTel())) {
	// phone = new StringBuilder(map.getContactTel().toString());
	// if(phone.length() == 11) {
	// map.setContactTel(phone.replace(3, 7, "****").toString());
	// } else if(phone.length() ==7||phone.length() ==8) {
	// map.setContactTel(phone.replace(3, 5, "**").toString());
	// }
	// else {
	// continue;
	// }
	// }
	// if(!StringHelper.IsEmptyOrNull(map.getBackupPhone())) {
	// phone = new StringBuilder(map.getBackupPhone().toString());
	// if(phone.length() == 11) {
	// map.setBackupPhone( phone.replace(3, 7, "****").toString());
	// } else if(phone.length() ==7||phone.length() ==8) {
	// map.setBackupPhone( phone.replace(3, 5, "**").toString());
	// }
	//
	// else {
	// continue;
	// }
	// }
	//
	// }
	// return list;
	//
	// }

}