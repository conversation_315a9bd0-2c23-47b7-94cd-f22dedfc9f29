package com.ly.mp.busicen.common.helper;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;

/**
 * 
 * 以静态变量保存Spring ApplicationContext, 可在任何代码任何地方任何时候取出ApplicaitonContext.
 *  
 * <AUTHOR>
 * @version  [V2.00, 2016年3月8日]
 * @since V1.00
 */


/**
 * 其实就是spring加载后就找继承了这个接口的类，然后自动执行setApplicationContext方法，
 * 给它填个applicationContext，然后其他地方都可以通过这个SpringContextHolder获取applicationContext，
 * 进而拿到beans
 * 
 *
 */
@Configuration
public class SpringContextHolder implements ApplicationContextAware {

	private static ApplicationContext applicationContext = null;
	@Bean
	public PaginationInterceptor paginationInterceptor() {
		PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
		return paginationInterceptor;
	}
//	@Bean
//	public MybatisPlusInterceptor paginationInterceptor() {
//		MybatisPlusInterceptor paginationInterceptor = new MybatisPlusInterceptor();
//		PaginationInnerInterceptor paginationInnerInterceptor=new PaginationInnerInterceptor();
//		paginationInnerInterceptor.setDbType(DbType.MYSQL);
//		paginationInnerInterceptor.setOverflow(true);
//		paginationInterceptor.addInnerInterceptor(paginationInnerInterceptor);
//		return paginationInterceptor;
//	}
//    @Bean
//    public ConfigurationCustomizer configurationCustomizer() {
//        return configuration -> configuration.setUseDeprecatedExecutor(false);
//    }
    
//	@Bean
//	public CacheHelper cacheHelper() {
//		CacheHelper cacheHelper = new CacheHelper();
//		return cacheHelper;
//	}
	
	@Override
	 public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		 SpringContextHolder.applicationContext = applicationContext;
	    }
	 
	    @SuppressWarnings("unchecked")
		public static <T> T getBean(String beanName) {
	        if(applicationContext.containsBean(beanName)){
	            return (T) applicationContext.getBean(beanName);
	        }else{
	            return null;
	        }
	    }
	    
		public static <T> T getBean(Class<T> requiredType) {
			return applicationContext.getBean(requiredType);
		}
		public static <T> T getBean(String name,Class<T> requiredType) {
		
			return  applicationContext.getBean(name,requiredType);
		}
}