package com.ly.mp.busicen.common.util;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/// <summary>
/// Sql查询解析类
/// </summary>
/// <example>
/// <code>
/// <para>SqlQueryParser parser = new SqlQueryParser(); </para>
/// <para>DataTable dt = parser.Sql("SELECT * FROM T_MDM_ORG_DLR a WHERE 1=1 ")</para>
/// <para>      .Sql("DLR_ID", "AND a.DLR_ID = @DLR_ID") </para>
/// <para>      .Sql("DLR_CODE", "AND a.DLR_CODE like @DLR_CODE", true) </para>
/// <para>      .Query(dic); </para>
/// </code>
/// </example>
public class SqlQueryParser {

	private List<SqlSegment> lstSqlSegment = null;

	/// <summary>
	/// 构造SqlQueryParser的实例
	/// </summary>
	public SqlQueryParser() {
		lstSqlSegment = new ArrayList<>();
	}

	/// <summary>
	/// 增加不带参数的Sql子句.
	/// </summary>
	/// <param name="sqlClause">sql子句</param>
	/// <returns>返回SqlQueryParser的实例</returns>
	public SqlQueryParser sql(String sqlClause) {
		lstSqlSegment.add(new SqlSegment(sqlClause));
		return this;
	}

	/// <summary>
	/// 增加带参数的sql子句
	/// </summary>
	/// <param name="dicKey">键，对应字典键和绑定变量名字</param>
	/// <param name="sqlClause">sql子句</param>
	/// <param name="dbType">数据类型，默认为Varchar2</param>
	/// <returns>返回SqlQueryParser的实例</returns>
	public SqlQueryParser sql(String dicKey, String sqlClause) {
		lstSqlSegment.add(new BaseSqlSegment(dicKey, sqlClause));
		return this;
	}

	/// <summary>
	/// 增加带like参数的sql子句
	/// </summary>
	/// <param name="dicKey">键，对应字典键和绑定变量名字</param>
	/// <param name="sqlClause">sql子句</param>
	/// <param name="percentSignStyle">百分号显示方式，默认为Both</param>
	/// <returns>返回SqlQueryParser的实例</returns>
	public SqlQueryParser like(String dicKey, String sqlClause, PercentSignStyle percentSignStyle) {
		lstSqlSegment.add(new LikeSqlSegment(dicKey, sqlClause, percentSignStyle));
		return this;
	}

	public String parse(Map<String, Object> dicParam) {
		// 去掉参数(String类型)的前后空格
		for (Map.Entry<String, Object> entry : dicParam.entrySet()) {
			Object val = entry.getValue();
			if (val instanceof String) {
				dicParam.put(entry.getKey(), entry.getValue().toString().trim());
			}
		}
		
		StringBuilder builder = new StringBuilder();

		for (SqlSegment segment : lstSqlSegment) {
			segment.parse(dicParam, builder);
		}

		return builder.toString();
	}

	public static void main(String[] args) {
		Map<String, Object> dicParam = new HashMap<>();
		
		List<String> empIds = new ArrayList<>();
//		empIds.add("1064");
//		empIds.add("1485");
		
		dicParam.put("ENABLED", null);
		dicParam.put("USER_NAME", " adm ");
		dicParam.put("EMP_ID", empIds);

		SqlQueryParser parser = new SqlQueryParser();
		parser.sql("SELECT * FROM t_eap_sys_user u");
		parser.sql(" WHERE 1 = 1 ");

		parser.sql("ENABLED", "   AND u.ENABLED = :ENABLED ");
		parser.sql("EMP_ID", "   AND u.EMP_ID in(:EMP_ID) ");
		parser.like("USER_NAME", "   AND u.USER_NAME LIKE :USER_NAME ", PercentSignStyle.Left);
//		parser.like("USER_NAME", "   AND u.USER_NAME LIKE :USER_NAME ", PercentSignStyle.None);

		String sql = parser.parse(dicParam);
		System.out.println("sql : " + sql);
	}
}
