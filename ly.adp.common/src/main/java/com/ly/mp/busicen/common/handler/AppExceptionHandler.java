package com.ly.mp.busicen.common.handler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import com.ly.mp.busicen.common.exception.AppException;
import com.ly.mp.busicen.common.response.Result;
import com.ly.mp.busicen.common.response.ResultEnum;
import com.ly.mp.busicen.common.response.ResultUtil;

/**
 * 全局异常拦截器
 * <AUTHOR>
 * @date   2018-7-25
 */
@ControllerAdvice
public class AppExceptionHandler {
 
    private static final Logger logger = LoggerFactory.getLogger(AppExceptionHandler.class);
 
    /**
     * 系统异常捕获处理
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public Result javaExceptionHandler(Exception ex) {
        logger.error("捕获到Exception异常:" + ex.getMessage(), ex);
        //异常日志入库
 
        return ResultUtil.error(ResultEnum.FAIL.getCode(),ResultEnum.FAIL.getMsg());
    }
 
    /**
     * 自定义异常捕获处理
     * @param ex
     * @return
     */
    @ResponseBody
    @ExceptionHandler(value = AppException.class)
    public Result messageCenterExceptionHandler(AppException ex) {
        logger.error("捕获到AppException异常:" + ex.getMessage(), ex);
        //异常日志入库
 
        return ResultUtil.error(ResultEnum.FAIL.getCode(), ex.getMessage());
    }
 
}
