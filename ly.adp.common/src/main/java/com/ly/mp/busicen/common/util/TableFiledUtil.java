package com.ly.mp.busicen.common.util;

import java.beans.PropertyDescriptor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.PropertyUtilsBean;

import com.baomidou.mybatisplus.core.metadata.TableFieldInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.core.metadata.TableInfoHelper;
import com.ly.mp.busicen.common.constant.TableFiledExtend;
import com.ly.mp.component.helper.StringHelper;


public class TableFiledUtil {
	//实体类转为MAP
		public static Map<String, Object> beanToMap(Object obj) { 
	        Map<String, Object> params = new HashMap<String, Object>(0); 
	        try { 
	            
	            PropertyUtilsBean propertyUtilsBean = new PropertyUtilsBean(); 
	            PropertyDescriptor[] descriptors = propertyUtilsBean.getPropertyDescriptors(obj); 
	            for (int i = 0; i < descriptors.length; i++) { 
	                String name = descriptors[i].getName(); 

	                if (!"class".equals(name)) { 
	                    params.put(name, propertyUtilsBean.getNestedProperty(obj, name)); 
	                }
	                
	                
	            } 
	        } catch (Exception e) { 
	            e.printStackTrace(); 
	        } 
	        return params; 
		}

		//将数据库字段名转换为 mybatis实体类对应的字段名 
		//例如 t_csc_db_node 字段 NODE_CODE 转为 nodeCode 
		public static List<TableFiledExtend> tranDbFieldToMyBatis(List<Map<String, Object>> dbFieldList,Class<?> t){
			
			//返回变量
			List<TableFiledExtend> returnList= new ArrayList<>();
			
			//数据库表名
			String tableName = "";
			//获取mybatis映射的表信息
			TableInfo tableInfo = TableInfoHelper.getTableInfo(t);
			//TableInfo tableInfo= SqlHelper.table(t);
			//获取表名
			tableName = tableInfo.getTableName().toUpperCase();
			
			//将mybatis映射的表字段名称跟数据库字段名字做映射 例如 DbNode实体 nodeCode 映射为数据库字段 NODE_CODE
			Map<String, String> mybatisDbMap = new HashMap<String, String>();
			//主键不在getFieldList中
			mybatisDbMap.put(tableInfo.getKeyColumn(), tableInfo.getKeyProperty());
			TableFieldInfo fieldTemp;
			for( int i = 0 ; i < tableInfo.getFieldList().size() ; i++) {
				//dbFieldList.get(i).get(key)
				fieldTemp=tableInfo.getFieldList().get(i);
				//NODE_CODE:nodeCode
				if(!StringHelper.IsEmptyOrNull(fieldTemp.getColumn())){
					mybatisDbMap.put(fieldTemp.getColumn(),fieldTemp.getEl());
				}
			}
			
			//转换为实体类字段名
			String columnName;
			String mybatisColumnName;
			String columnComment;
			String columnType;
			float columnLength=0;
			boolean isPrimeKey;
			boolean isNullAble;
			String columnDefault;
			
			for( int i = 0 ; i < dbFieldList.size() ; i++) {
				
				try
				{
				Map<String, Object> tempMap = dbFieldList.get(i);
				
				if("LAST_UPDATED_DATE".equals(tempMap.get("COLUMN_NAME").toString())){
					columnName=tempMap.get("COLUMN_NAME").toString();
				}
				//字段名转换
				columnName=tempMap.get("COLUMN_NAME").toString();
				if(mybatisDbMap.containsKey(columnName)){
					mybatisColumnName=mybatisDbMap.get(columnName);
				}else{
					mybatisColumnName="";
				}
				
				columnComment=StringHelper.IsEmptyOrNull(tempMap.get("COLUMN_COMMENT"))?"": tempMap.get("COLUMN_COMMENT").toString();
				columnType=StringHelper.IsEmptyOrNull(tempMap.get("DATA_TYPE"))?"": tempMap.get("DATA_TYPE").toString();
				if(StringHelper.IsEmptyOrNull(tempMap.get("COLUMN_LENGTH")) && tempMap.get("COLUMN_LENGTH")!=null){
					columnLength=(float)tempMap.get("COLUMN_LENGTH");
				}
				if(!StringHelper.IsEmptyOrNull(tempMap.get("IS_KEY")) && "YES".equals(tempMap.get("IS_KEY").toString())){
					isPrimeKey=true;
				}
				else{
					isPrimeKey=false;
				}
				if(!StringHelper.IsEmptyOrNull(tempMap.get("IS_NULLABLE")) && "YES".equals(tempMap.get("IS_NULLABLE").toString())){
					isNullAble=true;
				}
				else{
					isNullAble=false;
				}
				columnDefault=StringHelper.IsEmptyOrNull(tempMap.get("COLUMN_DEFAULT"))?"": tempMap.get("COLUMN_DEFAULT").toString();
				
				TableFiledExtend filedExtend = new TableFiledExtend();
				
				filedExtend.setColumnName(columnName);
				filedExtend.setMybatisColumnName(mybatisColumnName);
				filedExtend.setColumnComment(columnComment);
				filedExtend.setColumnType(columnType);
				filedExtend.setColumnLength(columnLength);
				filedExtend.setColumnDefault(columnDefault);
				filedExtend.setIsPrimeKey(isPrimeKey);
				filedExtend.setIsNullAble(isNullAble);
				
				returnList.add(filedExtend);
				}
				catch(Exception e){
					String es=e.getMessage();
				}
				
			}
			

			return returnList;
			
		}

		/**
		 * 插入表时 校验必填字段
		 * <AUTHOR>
		 * @param filedList 字段列表
		 * @param model 实体类
		 * @return
		 */
		public static List<String> insertCheck(List<Map<String, Object>> dbFieldList,Object model){
			
			//返回 缺少字段
			List<String> errList= new ArrayList<>();
			
			//实体类对象转为MAP
			Map<String,Object> modelMap = new HashMap<String, Object>();
			modelMap = beanToMap(model);
			
			if(modelMap.size()<1){
				return errList;
			}

			//获取mybatis映射的表信息
			//TableInfo tableInfo= SqlHelper.table(model.getClass());
			//获取表名
			//String tableName = tableInfo.getTableName().toUpperCase();
					
			//将数据库字段名转换为 mybatis实体类对应的字段名
			List<TableFiledExtend> filedList = tranDbFieldToMyBatis(dbFieldList,model.getClass());
			
			//遍历其他字段编码
			String columnName="";
			for( int i = 0 ; i < filedList.size() ; i++) {
				//字段不可为空，且没有默认值时，需判断实体对应字段有没有值
			    if(!filedList.get(i).getIsNullAble() && StringHelper.IsEmptyOrNull(filedList.get(i).getColumnDefault())){
			    	columnName=filedList.get(i).getMybatisColumnName();
			    	if(!modelMap.containsKey(columnName) || StringHelper.IsEmptyOrNull(modelMap.get(columnName)) ){
				    	//将不存在的非空字段放进来
						errList.add(String.format("%s[%s]",filedList.get(i).getColumnComment(),columnName));
				    }
			    }
			}

		
			return errList;
		}
		
		/**
		 * 更新表时 校验必填字段
		 * <AUTHOR>
		 * @param filedList 字段列表
		 * @param model 实体类
		 * @return
		 */
		public static List<String> updateCheck(List<Map<String, Object>> dbFieldList,Object model){
			
			//返回 缺少字段
			List<String> errList= new ArrayList<>();
			
			//实体类对象转为MAP
			Map<String,Object> modelMap = new HashMap<String, Object>();
			modelMap = beanToMap(model);
			
			if(modelMap.size()<1){
				return errList;
			}

			//将数据库字段名转换为 mybatis实体类对应的字段名
			List<TableFiledExtend> filedList = tranDbFieldToMyBatis(dbFieldList,model.getClass());
			
			
			//校验主键
			//获取主键(数据库字段名，不是实体字段名)键值
			Map<String,Object> keyFiledMap = getKeyFiled(dbFieldList,model);
			if(keyFiledMap.size()<1 || StringHelper.IsEmptyOrNull(keyFiledMap.get("key"))){
				errList.add(String.format("%s[%s]", keyFiledMap.get("keyCN"),keyFiledMap.get("entitykey") ));
				return errList;
			}

			//遍历传入的实体类的字段
			for(String key : modelMap.keySet()){
				//只判断获取""字段，null的字段不会保存到数据库
				if(modelMap.get(key) != null && "".equals(modelMap.get(key).toString())){
					//判断该字段是否必填且没有默认值
					List<TableFiledExtend> matchfiledList = filedList.stream()
					.filter(f->key.equals(f.getMybatisColumnName())&&!f.getIsNullAble()&&StringHelper.IsEmptyOrNull(f.getColumnDefault()))
					.collect(Collectors.toList());
					
					//字段必填而传入的为空
					if(matchfiledList.size()>0){
						TableFiledExtend firstfiled = matchfiledList.get(0);
						if(!firstfiled.getIsNullAble()&&StringHelper.IsEmptyOrNull(firstfiled.getColumnDefault())) {
						//将不存在的非空字段放进来
						errList.add(String.format("%s[%s]", firstfiled.getColumnComment(),key));
						}
						errList.add(key);
					}
					
				}	    
			}
			
			return errList;
		}
		
		/**
		 * 获取实体 主键信息 
		 * @param dbFieldList
		 * @param model
		 * @return
		 */
		public static Map<String,Object> getKeyFiled(List<Map<String, Object>> dbFieldList,Object model){	
			Map<String,Object> result = new HashMap<String,Object>();
			
			//实体类对象转为MAP
			Map<String,Object> modelMap = new HashMap<String, Object>();
			modelMap = beanToMap(model);
					
			//将数据库字段名转换为 mybatis实体类对应的字段名
					List<TableFiledExtend> filedList = tranDbFieldToMyBatis(dbFieldList,model.getClass());
			//获取主键字段名称
					List<TableFiledExtend> keyfiledList = filedList.stream().filter(f->f.getIsPrimeKey())
							.collect(Collectors.toList());
					if(keyfiledList.size()>0){
						String keyfilename = keyfiledList.get(0).getColumnName();
						result.put("key",keyfilename);//字段编码
						result.put("entitykey",keyfiledList.get(0).getMybatisColumnName());//实体字段编码
						result.put("keyCN",keyfiledList.get(0).getColumnComment());//字段中文名
						result.put("value",modelMap.get(keyfiledList.get(0).getMybatisColumnName()));//字段值
						//result.put(keyfiledList.get(0).getMybatisColumnName(), modelMap.get(keyfilename));	
					}
					
			return result;
		}
}
