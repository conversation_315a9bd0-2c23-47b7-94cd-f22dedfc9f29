package com.ly.mp.busicen.common.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 创建时间 ：2018年8月27日 上午11:40:40
 */
public abstract class BaseEntity extends Model<BaseEntity> {


    /**
	 * 
	 */
	private static final long serialVersionUID = 8764329084145549259L;

	/**
     * 最后更新时间
     */
    @TableField("last_updated_date" )
    //@JsonProperty("LAST_UPDATED_DATE")
    private Date lastUpdatedDate;

    /**
     * 最后更新人员
     */
    @TableField("modifier" )
    private String modifier;

    /**
     * 创建时间
     */
    @TableField("created_date" )
    //@JsonProperty("CREATED_DATE")
    private Date createdDate;

    /**
     * 创建人
     */
    @TableField("creator" )
    private String creator;

    /**
     * 可用标记 1-可用，0-已删除
     */
    @TableField("is_enable" )
    private String isEnable;

    /**
     * 可用标记 1-可用，0-已删除
     */
    @TableField("update_control_id" )
    private String updateControlId;

    /**
     * 可用标记 1-可用
     */
    public static final String ENABLE = "1";
    /**
     * 可用标记 0-已删除
     */
    public static final String DISABLE = "0";

    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(String isEnable) {
        this.isEnable = isEnable;
    }

    public String getUpdateControlId() {
        return updateControlId;
    }

    public void setUpdateControlId(String updateControlId) {
        this.updateControlId = updateControlId;
    }


    /**
     * 获取实体主键值
     *
     * <AUTHOR>
     * @version 创建时间 ：2018年10月8日 上午8:39:53
     */
    public Serializable getPrimaryValue() {
        return this.pkVal();
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.MULTI_LINE_STYLE);
    }
}
