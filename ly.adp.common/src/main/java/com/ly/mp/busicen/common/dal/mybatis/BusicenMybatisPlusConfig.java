package com.ly.mp.busicen.common.dal.mybatis;

import java.util.Map;

import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.wrapper.MapWrapper;
import org.apache.ibatis.reflection.wrapper.ObjectWrapper;
import org.apache.ibatis.reflection.wrapper.ObjectWrapperFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;

@Configuration
public class BusicenMybatisPlusConfig {
	
	Logger log = LoggerFactory.getLogger(BusicenMybatisPlusConfig.class);
	
	private static ThreadLocal<Boolean> CamelCaseMapping = new ThreadLocal<Boolean>() {
		@Override
		protected Boolean initialValue() {
			return true;
		};
	};
	
	public static void useCamelCaseMapping(Boolean use) {
		CamelCaseMapping.set(use);
	}
	
	public static void resetCamelCaseMapping() {
		CamelCaseMapping.remove();
	}
	
	@Bean
	public PaginationInterceptor paginationInterceptor() {
		@SuppressWarnings("deprecation")
		PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
		return paginationInterceptor;
	}
	
	@Bean
    public ConfigurationCustomizer configurationCustomizer123() {
        return configuration -> configuration.setObjectWrapperFactory(new MapWrapperFactory());
    }

    
    public static class MapWrapperFactory implements ObjectWrapperFactory {
        @Override
        public boolean hasWrapperFor(Object object) {
            return object != null && object instanceof Map;
        }

        @SuppressWarnings({ "unchecked", "rawtypes" })
		@Override
        public ObjectWrapper getWrapperFor(MetaObject metaObject, Object object) {
            return new MyMapWrapper(metaObject, (Map) object);
        }
    }

    static class MyMapWrapper extends MapWrapper {
        MyMapWrapper(MetaObject metaObject, Map<String, Object> map) {
            super(metaObject, map);
        }

        @Override
        public String findProperty(String name, boolean useCamelCaseMapping) {
            if (useCamelCaseMapping&&CamelCaseMapping.get()
                    && ((name.charAt(0) >= 'A' && name.charAt(0) <= 'Z')
                    || name.contains("_"))) {
                return underlineToCamelhump(name);
            }
            return name;
        }

        /**
         * 将下划线风格替换为驼峰风格
         *
         * @param inputString
         * @return
         */
        private String underlineToCamelhump(String inputString) {
            StringBuilder sb = new StringBuilder();

            boolean nextUpperCase = false;
            for (int i = 0; i < inputString.length(); i++) {
                char c = inputString.charAt(i);
                if (c == '_') {
                    if (sb.length() > 0) {
                        nextUpperCase = true;
                    }
                } else {
                    if (nextUpperCase) {
                        sb.append(Character.toUpperCase(c));
                        nextUpperCase = false;
                    } else {
                        sb.append(Character.toLowerCase(c));
                    }
                }
            }
            return sb.toString();
        }
    }

}
