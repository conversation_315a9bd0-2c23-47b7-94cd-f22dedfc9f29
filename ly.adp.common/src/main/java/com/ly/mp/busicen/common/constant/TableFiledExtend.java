package com.ly.mp.busicen.common.constant;

public class TableFiledExtend {
	
	
	/**
     * 字段编码 大写
     */
	private String columnName;
	
	/**
     * mybatis对应的实体字段
     */
	private String mybatisColumnName;
	
	/**
     * 字段描述
     */
	private String columnComment;
	
	/**
     * 字段类型
     */
	private String columnType;
	
	/**
     * 字段长度
     */
	private float columnLength;
	
	/**
     * 是否主键
     */
	private boolean isPrimeKey;
	
	/**
     * 是否可为空
     */
	private boolean isNullAble;
	
	/**
     * 默认值
     */
	private String columnDefault;
	
	
    
	public String getColumnName() {
        return columnName;
    }

    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }
    
    public String getMybatisColumnName() {
        return mybatisColumnName;
    }

    public void setMybatisColumnName(String mybatisColumnName) {
        this.mybatisColumnName = mybatisColumnName;
    }
    
    
    public String getColumnComment() {
        return columnComment;
	}
	
	public void setColumnComment(String columnComment) {
		this.columnComment = columnComment;
	}
	
	public String getColumnType() {
	        return columnType;
	}
	
	public void setColumnType(String columnType) {
		this.columnType = columnType;
	}
	
	public float getColumnLength() {
	        return columnLength;
	}
	
	public void setColumnLength(float columnLength) {
		this.columnLength = columnLength;
	}
	
	public boolean getIsPrimeKey() {
	        return isPrimeKey;
	}
	
	public void setIsPrimeKey(boolean isPrimeKey) {
		this.isPrimeKey = isPrimeKey;
	}
	
	public boolean getIsNullAble() {
	        return isNullAble;
	}
	
	public void setIsNullAble(boolean isNullAble) {
		this.isNullAble = isNullAble;
	}
	
	public String getColumnDefault() {
        return columnDefault;
    }

    public void setColumnDefault(String columnDefault) {
        this.columnDefault = columnDefault;
    }

}
