/**
 * Copyright © 2019 eSunny Info. Tech Ltd. All rights reserved.
 *
 * 功能描述：
 * @Package: com.ly.mp.busicen.common.util
 * @author: ly-wyifan
 * @date: 2019年7月3日 下午8:49:55
 */
package com.ly.mp.busicen.common.util;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.busicen.common.context.BusicenConvert;
import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.helper.Reflections;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.helper.StringHelper;

/**
 * Copyright: Copyright (c) 2019 lanyou-wyifan
 *
 * @Description: 公共方法
 * @version: v1.0.0
 * @author: ly-wyifan
 * @date: 2019年7月3日 下午8:49:55
 *
 *        Modification History: Date Author Version Description
 *        ---------------------------------------------------------* 2019年7月3日
 *        ly-wyifan v1.0.0 修改原因
 */
public class BusicenUtils {
	public static saveOrUpdate Save = saveOrUpdate.Save;
    public static saveOrUpdate Saves = saveOrUpdate.Saves;
	public static saveOrUpdate Update = saveOrUpdate.Update;

	/**
	 *
	 * @Description: Ipage类型转换为ListResult ,供分页查询使用
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月3日 下午8:59:31
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月3日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	@Deprecated
	public static <T> ListResult<T> page2ListResult(IPage<T> page, int pageIndex, int pageSize) {
		ListResult<T> result = new ListResult<T>();
		result.setRows(page.getRecords());
		result.setRecords((int) page.getTotal());
		result.setPages((int) page.getPages());
		result.setPageindex((int) page.getCurrent());
		result.setResult("1");
		result.setMsg("操作成功");
		return result;
	}

	/**
	 *
	 * @Description: 更新Ipage类型转换为ListResult ,供分页查询使用
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月9日 下午9:13:20
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月9日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	public static <T> ListResult<T> page2ListResult(IPage<T> page) {
		ListResult<T> result = new ListResult<>();
		result.setRows(page.getRecords());
		result.setRecords((int) page.getTotal());
		result.setPages((int) page.getPages());
		result.setPageindex((int) page.getCurrent());
		result.setResult("1");
		result.setMsg("查询成功");
		return result;
	}

	/**
	 *
	 * @Description:返回ListResult<T> （ 自用到时会删掉，其他人请不要调用）
	 *
	 * @version: v1.0.0
	 * @author: ly-chenjunl
	 * @date: 2019年7月16日 上午11:05:01
	 */
	@Deprecated
	public static <T> ListResult<T> getQueryList(Class<T> clazz) {
		ListResult<T> result = new ListResult<T>();
		List<T> rcList = new ArrayList<T>();
		T model = null;
		try {
			model = clazz.newInstance();
		} catch (InstantiationException | IllegalAccessException e) {
		}
		rcList.add(model);
		result.setRows(rcList);
		result.setRecords(1);
		result.setPages(1);
		result.setPageindex(1);
		result.setResult("1");
		result.setMsg("sucess");
		return result;
	}

	/**
	 *
	 * @Description: 用于替换频繁的set方法。包括set
	 *               oemId,oemCode,groupId,groupCode,modifier,lastUpdateDate
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @date: 2019年7月25日 上午11:08:09
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月10日
	 *        ly-wyifan v1.0.0 修改原因 使用枚举类型
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeUserInfo(T object, saveOrUpdate enums, String token) {
		if (object == null) {
			return null;
		}
		UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
		if (object instanceof Map) {
			@SuppressWarnings("rawtypes")
			Map map = (Map) object;
			// 保存
			if (enums.equals(Save)) {
				if (userBusiEntity != null) {
					// oemId
					map.put("oemId", userBusiEntity.getOemID());
					// oemCode
					map.put("oemCode", userBusiEntity.getOemCode());
					// groupId
					map.put("groupId", userBusiEntity.getGroupID());
					// groupCode
					map.put("groupCode", userBusiEntity.getGroupCode());
					// 创建人名称
					map.put("createdName", userBusiEntity.getEmpName());
					// 修改人名称
					map.put("modifyName", userBusiEntity.getEmpName());
					// 最後修改人 modifier
					map.put("modifier", userBusiEntity.getUserID());
					// 创建人
					map.put("creator", userBusiEntity.getUserID());
				} else {
					// oemId
					map.put("oemId", "1");
					// oemCode
					map.put("oemCode", "1");
					// groupId
					map.put("groupId", "1");
					// groupCode
					map.put("groupCode", "1");
					// 创建人名称
					map.put("createdName", "sys");
					// 修改人名称
					map.put("modifyName", "sys");
					// 最後修改人 modifier
					map.put("modifier", "1");
					// 创建人
					map.put("creator", "1");
				}
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
				map.put("createdDate", LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
			} else {
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				if (userBusiEntity != null) {
					// 最後修改人 modifier
					map.put("modifier", userBusiEntity.getUserID());
					// 最後修改人名称 modifierName
					map.put("modifyName", userBusiEntity.getEmpName());
				} else {
					// 最後修改人 modifier
					map.put("modifier", "1");
					// 最後修改人名称 modifierName
					map.put("modifyName", "sys");
				}
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
			}
			return object;
		}
		Class<T> clazz = (Class<T>) object.getClass();
		try {
			// 保存
			if (userBusiEntity != null) {
				switch (enums) {
				case Save:
					// oemId
					Method method = clazz.getMethod("setOemId", String.class);
					if (null != method) {
						method.invoke(object, userBusiEntity.getOemID());
					}
					// oemCode
					Method method1 = clazz.getMethod("setOemCode", String.class);
					if (null != method1) {
						method1.invoke(object, userBusiEntity.getOemCode());
					}
					// groupId
					Method method2 = clazz.getMethod("setGroupId", String.class);
					if (null != method2) {
						method2.invoke(object, userBusiEntity.getGroupID());
					}
					// groupCode
					Method method3 = clazz.getMethod("setGroupCode", String.class);
					if (null != method3) {
						method3.invoke(object, userBusiEntity.getGroupCode());
					}

					Object createdName = Reflections.getFieldValue(object, "createdName");
					if(StringHelper.IsEmptyOrNull(createdName)){
						// 创建人名称
						Method method4 = clazz.getMethod("setCreatedName", String.class);
						if (null != method4) {
							method4.invoke(object, userBusiEntity.getEmpName());
						}
					}
					
					Object modifyName = Reflections.getFieldValue(object, "modifyName");
					if(StringHelper.IsEmptyOrNull(modifyName)){
						// 修改人名称
						Method method5 = clazz.getMethod("setModifyName", String.class);
						if (null != method5) {
							method5.invoke(object, userBusiEntity.getEmpName());
						}
					}
					
					Object modifier = Reflections.getFieldValue(object, "modifier");
					if(StringHelper.IsEmptyOrNull(modifier)){
						// 最後修改人 modifier
						Method method6 = clazz.getMethod("setModifier", String.class);
						if (null != method6) {
							method6.invoke(object, userBusiEntity.getUserID());
						}
					}
					
					Object creator = Reflections.getFieldValue(object, "creator");
					if(StringHelper.IsEmptyOrNull(creator)){
						// 创建人
						Method method7 = clazz.getMethod("setCreator", String.class);
						if (null != method7) {
							method7.invoke(object, userBusiEntity.getUserID());
						}
					}
					// 并发控制Id updateDateControlId
					Method method8 = clazz.getMethod("setUpdateControlId", String.class);
					if (null != method8) {
						method8.invoke(object, StringHelper.GetGUID());
					}
					
					Object createdDate = Reflections.getFieldValue(object, "createdDate");
						if(StringHelper.IsEmptyOrNull(createdDate)){
						// 创建时间 createdDate
						Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
						if (null != method9) {
							method9.invoke(object, LocalDateTime.now());
						}
					}
						
					Object lastUpdatedDate = Reflections.getFieldValue(object, "lastUpdatedDate");
					if(StringHelper.IsEmptyOrNull(lastUpdatedDate)){
						// 最後更新時間 lastUpdateDate
						Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
						if (null != method10) {
							method10.invoke(object, LocalDateTime.now());
						}
					}
					break;
				case Update:
					// 修改
					// 最後更新時間 lastUpdateDate
					Method method11 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
					method11.invoke(object, LocalDateTime.now());
					// 最後修改人 modifier
					Method method13 = clazz.getMethod("setModifier", String.class);
					method13.invoke(object, userBusiEntity.getUserID());
					// 最後修改人名称 modifierName
					Method method12 = clazz.getMethod("setModifyName", String.class);
					if (null != method12) {
						method12.invoke(object, userBusiEntity.getEmpName());
					}
					// 并发控制Id updateDateControlId
					Method method17 = clazz.getMethod("setUpdateControlId", String.class);
					method17.invoke(object, StringHelper.GetGUID());
				default:
					break;
				}
			} else {
				switch (enums) {
				case Save:
					// oemId
					Method method = clazz.getMethod("setOemId", String.class);
					method.invoke(object, "1");
					// oemCode
					Method method1 = clazz.getMethod("setOemCode", String.class);
					method1.invoke(object, "1");
					// groupId
					Method method2 = clazz.getMethod("setGroupId", String.class);
					method2.invoke(object, "1");
					// groupCode
					Method method3 = clazz.getMethod("setGroupCode", String.class);
					method3.invoke(object, "1");
					// 创建人名称
					Method method4 = clazz.getMethod("setCreatedName", String.class);
					method4.invoke(object, "sys");
					// 修改人名称
					Method method5 = clazz.getMethod("setModifyName", String.class);
					method5.invoke(object, "sys");
					// 最後修改人 modifier
					Method method6 = clazz.getMethod("setModifier", String.class);
					method6.invoke(object, "1");
					// 创建人
					Method method7 = clazz.getMethod("setCreator", String.class);
					method7.invoke(object, "1");
					// 并发控制Id updateDateControlId
					Method method8 = clazz.getMethod("setUpdateControlId", String.class);
					method8.invoke(object, StringHelper.GetGUID());
					// 创建时间 createdDate
					Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
					method9.invoke(object, LocalDateTime.now());
					// 最後更新時間 lastUpdateDate
					Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
					method10.invoke(object, LocalDateTime.now());
					break;
				case Update:
					// 修改
					// 最後更新時間 lastUpdateDate
					Method method11 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
					method11.invoke(object, LocalDateTime.now());
					// 最後修改人 modifier
					Method method13 = clazz.getMethod("setModifier", String.class);
					method13.invoke(object, "1");
					// 最後修改人名称 modifierName
					Method method12 = clazz.getMethod("setModifyName", String.class);
					method12.invoke(object, "sys");
					// 并发控制Id updateDateControlId
					Method method17 = clazz.getMethod("setUpdateControlId", String.class);
					method17.invoke(object, StringHelper.GetGUID());
				default:
					break;
				}
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return object;
	}

	/**
	 *
	 * @Description: 用于替换频繁的set方法。包括set
	 *               oemId,oemCode,groupId,groupCode,modifier,lastUpdateDate
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @date: 2019年7月25日 上午11:08:09
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月10日
	 *        ly-wyifan v1.0.0 修改原因 使用枚举类型 添加操作人用户参数，为空则取token里的用户
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeUserInfo(T object, saveOrUpdate enums, UserBusiEntity user, String token) {
		if (object == null) {
			return null;
		}
		UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
		if (userBusiEntity == null || StringHelper.IsEmptyOrNull(userBusiEntity.getUserID())) {
			userBusiEntity = user;
		}
		if (object instanceof Map) {
			@SuppressWarnings("rawtypes")
			Map map = (Map) object;
			// 保存
			if (enums.equals(Save)) {
				// oemId
				map.put("oemId", userBusiEntity.getOemID());
				// oemCode
				map.put("oemCode", userBusiEntity.getOemCode());
				// groupId
				map.put("groupId", userBusiEntity.getGroupID());
				// groupCode
				map.put("groupCode", userBusiEntity.getGroupCode());
				// 创建人名称
				map.put("createdName", userBusiEntity.getEmpName());
				// 修改人名称
				map.put("modifyName", userBusiEntity.getEmpName());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 创建人
				map.put("creator", userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
				map.put("createdDate", LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
			} else {
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				map.put("modifyName", userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
			}
			return object;
		}
		Class<T> clazz = (Class<T>) object.getClass();
		try {
			// 保存
			switch (enums) {
			case Save:
				// oemId
				Method method = clazz.getMethod("setOemId", String.class);
				method.invoke(object, userBusiEntity.getOemID());
				// oemCode
				Method method1 = clazz.getMethod("setOemCode", String.class);
				method1.invoke(object, userBusiEntity.getOemCode());
				// groupId
				Method method2 = clazz.getMethod("setGroupId", String.class);
				method2.invoke(object, userBusiEntity.getGroupID());
				// groupCode
				Method method3 = clazz.getMethod("setGroupCode", String.class);
				method3.invoke(object, userBusiEntity.getGroupCode());
				// 创建人名称
				Method method4 = clazz.getMethod("setCreatedName", String.class);
				method4.invoke(object, userBusiEntity.getEmpName());
				// 修改人名称
				Method method5 = clazz.getMethod("setModifyName", String.class);
				method5.invoke(object, userBusiEntity.getEmpName());
				// 最後修改人 modifier
				Method method6 = clazz.getMethod("setModifier", String.class);
				method6.invoke(object, userBusiEntity.getUserID());
				// 创建人
				Method method7 = clazz.getMethod("setCreator", String.class);
				method7.invoke(object, userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				Method method8 = clazz.getMethod("setUpdateControlId", String.class);
				method8.invoke(object, StringHelper.GetGUID());
				// 创建时间 createdDate
				Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
				method9.invoke(object, LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method10.invoke(object, LocalDateTime.now());
				break;
			case Update:
				// 修改
				// 最後更新時間 lastUpdateDate
				Method method11 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method11.invoke(object, LocalDateTime.now());
				// 最後修改人 modifier
				Method method13 = clazz.getMethod("setModifier", String.class);
				method13.invoke(object, userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				Method method12 = clazz.getMethod("setModifyName", String.class);
				method12.invoke(object, userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				Method method17 = clazz.getMethod("setUpdateControlId", String.class);
				method17.invoke(object, StringHelper.GetGUID());
			default:
				break;
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return object;
	}

	@SuppressWarnings("unchecked")
	public static <T> T invokeUserInfo(T object, saveOrUpdate enums, UserBusiEntity user) {
		if (object == null) {
			return null;
		}
		UserBusiEntity userBusiEntity = user;
		if (StringHelper.IsEmptyOrNull(user.getUserID())) {
			userBusiEntity = new UserBusiEntity();
			String one="1";
			String sys="sys";
			userBusiEntity.setOemID(one);
			userBusiEntity.setOemCode(one);
			userBusiEntity.setGroupCode(one);
			userBusiEntity.setGroupID(one);
			userBusiEntity.setEmpName(sys);
			userBusiEntity.setUserID(sys);
		}
		if (object instanceof Map) {
			@SuppressWarnings("rawtypes")
			Map map = (Map) object;
			// 保存
			if (enums.equals(Save)) {
				// oemId
				map.put("oemId", userBusiEntity.getOemID());
				// oemCode
				map.put("oemCode", userBusiEntity.getOemCode());
				// groupId
				map.put("groupId", userBusiEntity.getGroupID());
				// groupCode
				map.put("groupCode", userBusiEntity.getGroupCode());
				// 创建人名称
				map.put("createdName", userBusiEntity.getEmpName());
				// 修改人名称
				map.put("modifyName", userBusiEntity.getEmpName());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 创建人
				map.put("creator", userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
				map.put("createdDate", LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
			} else {
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				map.put("modifyName", userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
			}
			return object;
		}
		Class<T> clazz = (Class<T>) object.getClass();
		try {
			// 保存
			switch (enums) {
			case Save:
				// oemId
				Method method = clazz.getMethod("setOemId", String.class);
				method.invoke(object, userBusiEntity.getOemID());
				// oemCode
				Method method1 = clazz.getMethod("setOemCode", String.class);
				method1.invoke(object, userBusiEntity.getOemCode());
				// groupId
				Method method2 = clazz.getMethod("setGroupId", String.class);
				method2.invoke(object, userBusiEntity.getGroupID());
				// groupCode
				Method method3 = clazz.getMethod("setGroupCode", String.class);
				method3.invoke(object, userBusiEntity.getGroupCode());
				// 创建人名称
				Method method4 = clazz.getMethod("setCreatedName", String.class);
				method4.invoke(object, userBusiEntity.getEmpName());
				// 修改人名称
				Method method5 = clazz.getMethod("setModifyName", String.class);
				method5.invoke(object, userBusiEntity.getEmpName());
				// 最後修改人 modifier
				Method method6 = clazz.getMethod("setModifier", String.class);
				method6.invoke(object, userBusiEntity.getUserID());
				// 创建人
				Method method7 = clazz.getMethod("setCreator", String.class);
				method7.invoke(object, userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				Method method8 = clazz.getMethod("setUpdateControlId", String.class);
				method8.invoke(object, StringHelper.GetGUID());
				// 创建时间 createdDate
				Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
				method9.invoke(object, LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method10.invoke(object, LocalDateTime.now());
				break;
			case Update:
				// 修改
				// 最後更新時間 lastUpdateDate
				Method method11 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method11.invoke(object, LocalDateTime.now());
				// 最後修改人 modifier
				Method method13 = clazz.getMethod("setModifier", String.class);
				method13.invoke(object, userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				Method method12 = clazz.getMethod("setModifyName", String.class);
				method12.invoke(object, userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				Method method17 = clazz.getMethod("setUpdateControlId", String.class);
				method17.invoke(object, StringHelper.GetGUID());
			default:
				break;
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return object;
	}

	@SuppressWarnings("unchecked")
	public static <T> T invokeUserInfo(T object, SOU enums, String token) {
		if (object == null) {
			return null;
		}
		UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
		if (object instanceof Map) {
			@SuppressWarnings("rawtypes")
			Map map = (Map) object;
			// 保存
			if (enums.equals(SOU.Save)) {
				// oemId
				map.put("oemId", userBusiEntity.getOemID());
				// oemCode
				map.put("oemCode", userBusiEntity.getOemCode());
				// groupId
				map.put("groupId", userBusiEntity.getGroupID());
				// groupCode
				map.put("groupCode", userBusiEntity.getGroupCode());
				// 创建人名称
				map.put("createdName", userBusiEntity.getEmpName());
				// 修改人名称
				map.put("modifyName", userBusiEntity.getEmpName());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 创建人
				map.put("creator", userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
				map.put("createdDate", LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
			}else if(enums.equals(SOU.Saves)){
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 创建人
				map.put("creator", userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
				map.put("createdDate", LocalDateTime.now());
			}else if(enums.equals(SOU.Updates)){
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 创建人
				map.put("creator", userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
			}else {
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				map.put("modifyName", userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
			}
			return object;
		}
		Class<T> clazz = (Class<T>) object.getClass();
		try {
			// 保存
			switch (enums) {
                case Saves:

                  /*  // 修改人名称
                    Method method111 = clazz.getMethod("setModifyName", String.class);
                    method111.invoke(object, userBusiEntity.getEmpName());*/
                    // 最後修改人 modifier
                    Method method121 = clazz.getMethod("setModifier", String.class);
                    method121.invoke(object, userBusiEntity.getUserID());
                    // 创建人
                    Method method123 = clazz.getMethod("setCreator", String.class);
                    method123.invoke(object, userBusiEntity.getUserID());
                    // 并发控制Id updateDateControlId
                    Method method122 = clazz.getMethod("setUpdateControlId", String.class);
                    method122.invoke(object, StringHelper.GetGUID());
                    // 创建时间 createdDate
                    Method method114 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
                    method114.invoke(object, LocalDateTime.now());
                    // 最後更新時間 lastUpdateDate
                    Method method23 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
                    method23.invoke(object, LocalDateTime.now());
                    break;
				case Updates:

                  /*  // 修改人名称
                    Method method111 = clazz.getMethod("setModifyName", String.class);
                    method111.invoke(object, userBusiEntity.getEmpName());*/
					// 最後修改人 modifier
					Method method1221 = clazz.getMethod("setModifier", String.class);
					method1221.invoke(object, userBusiEntity.getUserID());
					/*// 创建人
					Method method1223 = clazz.getMethod("setCreator", String.class);
					method1223.invoke(object, userBusiEntity.getUserID());*/
					// 并发控制Id updateDateControlId
					Method method1222 = clazz.getMethod("setUpdateControlId", String.class);
					method1222.invoke(object, StringHelper.GetGUID());

					// 最後更新時間 lastUpdateDate
					Method method232 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
					method232.invoke(object, LocalDateTime.now());
					break;
			case Save:
				// oemId
				Method method = clazz.getMethod("setOemId", String.class);
				method.invoke(object, userBusiEntity.getOemID());
				// oemCode
				Method method1 = clazz.getMethod("setOemCode", String.class);
				method1.invoke(object, userBusiEntity.getOemCode());
				// groupId
				Method method2 = clazz.getMethod("setGroupId", String.class);
				method2.invoke(object, userBusiEntity.getGroupID());
				// groupCode
				Method method3 = clazz.getMethod("setGroupCode", String.class);
				method3.invoke(object, userBusiEntity.getGroupCode());
				// 创建人名称
				Method method4 = clazz.getMethod("setCreatedName", String.class);
				method4.invoke(object, userBusiEntity.getEmpName());
				// 修改人名称
				Method method5 = clazz.getMethod("setModifyName", String.class);
				method5.invoke(object, userBusiEntity.getEmpName());
				// 最後修改人 modifier
				Method method6 = clazz.getMethod("setModifier", String.class);
				method6.invoke(object, userBusiEntity.getUserID());
				// 创建人
				Method method7 = clazz.getMethod("setCreator", String.class);
				method7.invoke(object, userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				Method method8 = clazz.getMethod("setUpdateControlId", String.class);
				method8.invoke(object, StringHelper.GetGUID());
				// 创建时间 createdDate
				Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
				method9.invoke(object, LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method10.invoke(object, LocalDateTime.now());
				break;

			case Update:
				// 修改
				// 最後更新時間 lastUpdateDate
				Method method11 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method11.invoke(object, LocalDateTime.now());
				// 最後修改人 modifier
				Method method13 = clazz.getMethod("setModifier", String.class);
				method13.invoke(object, userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				Method method12 = clazz.getMethod("setModifyName", String.class);
				method12.invoke(object, userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				Method method17 = clazz.getMethod("setUpdateControlId", String.class);
				method17.invoke(object, StringHelper.GetGUID());
			default:
				break;
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return object;
	}

	public enum checkUpdatedControlId {
		success {

			@Override
			public String getMsg() {
				return "操作成功";
			}
		},
		fail {

			@Override
			public String getMsg() {
				return "并发操作,请重试!";
			}

		};

		public abstract String getMsg();
	}


	public enum saveOrUpdate {
		Save, Update,Saves,Updates
	}

	public enum SOU {
		Save, Update,Saves,Updates
	}

	/**
	 *
	 * @Description: 用于替换频繁的set方法。包括set
	 *               oemId,oemCode,groupId,groupCode,modifier,lastUpdateDate
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @date: 2019年7月10日 上午10:00:09
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月10日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	@SuppressWarnings("unchecked")
	@Deprecated
	public static <T> T invokeUserInfo(T object, int isSave, String token) {
		if (object == null) {
			return null;
		}
		UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
		if (object instanceof Map) {
			@SuppressWarnings("rawtypes")
			Map map = (Map) object;
			// 保存
			if (isSave == 1) {
				// oemId
				map.put("oemId", userBusiEntity.getOemID());
				// oemCode
				map.put("oemCode", userBusiEntity.getOemCode());
				// groupId
				map.put("groupId", userBusiEntity.getGroupID());
				// groupCode
				map.put("groupCode", userBusiEntity.getGroupCode());
				// 创建人名称
				map.put("createdName", userBusiEntity.getEmpName());
				// 修改人名称
				map.put("modifyName", userBusiEntity.getEmpName());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 创建人
				map.put("creator", userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
				// 创建时间 createdDate
				map.put("createdDate", LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
			} else {
				// 修改
				// 最後更新時間 lastUpdateDate
				map.put("lastUpdatedDate", LocalDateTime.now());
				// 最後修改人 modifier
				map.put("modifier", userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				map.put("modifyName", userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				map.put("updateControlId", StringHelper.GetGUID());
			}
			return object;
		}
		Class<T> clazz = (Class<T>) object.getClass();
		try {
			// 保存
			if (isSave == 1) {
				// oemId
				Method method = clazz.getMethod("setOemId", String.class);
				method.invoke(object, userBusiEntity.getOemID());
				// oemCode
				Method method1 = clazz.getMethod("setOemCode", String.class);
				method1.invoke(object, userBusiEntity.getOemCode());
				// groupId
				Method method2 = clazz.getMethod("setGroupId", String.class);
				method2.invoke(object, userBusiEntity.getGroupID());
				// groupCode
				Method method3 = clazz.getMethod("setGroupCode", String.class);
				method3.invoke(object, userBusiEntity.getGroupCode());
				// 创建人名称
				Method method4 = clazz.getMethod("setCreatedName", String.class);
				method4.invoke(object, userBusiEntity.getEmpName());
				// 修改人名称
				Method method5 = clazz.getMethod("setModifyName", String.class);
				method5.invoke(object, userBusiEntity.getEmpName());
				// 最後修改人 modifier
				Method method6 = clazz.getMethod("setModifier", String.class);
				method6.invoke(object, userBusiEntity.getUserID());
				// 创建人
				Method method7 = clazz.getMethod("setCreator", String.class);
				method7.invoke(object, userBusiEntity.getUserID());
				// 并发控制Id updateDateControlId
				Method method8 = clazz.getMethod("setUpdateControlId", String.class);
				method8.invoke(object, StringHelper.GetGUID());
				// 创建时间 createdDate
				Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
				method9.invoke(object, LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method10.invoke(object, LocalDateTime.now());
			} else {
				// 修改
				// 最後更新時間 lastUpdateDate
				Method method = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
				method.invoke(object, LocalDateTime.now());
				// 最後修改人 modifier
				Method method1 = clazz.getMethod("setModifier", String.class);
				method1.invoke(object, userBusiEntity.getUserID());
				// 最後修改人名称 modifierName
				Method method2 = clazz.getMethod("setModifyName", String.class);
				method2.invoke(object, userBusiEntity.getEmpName());
				// 并发控制Id updateDateControlId
				Method method7 = clazz.getMethod("setUpdateControlId", String.class);
				method7.invoke(object, StringHelper.GetGUID());
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}

		return object;
	}

	/**
	 *
	 * @Description: 针对job没有对应token时插入默认值
	 * @version: v1.0.0
	 * @author: ly-mengs
	 * @throws IllegalAccessException
	 * @throws InstantiationException
	 * @date: 2019年12月16日 19:59:09
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年12月16日
	 *        ly-mengs v1.0.0 修改原因
	 */
	// @SuppressWarnings("unchecked")
	// public static <T> T invokeUserInfo(T object,String token) {
	// if (object == null) {
	// return null;
	// }
	// Class<T> clazz = (Class<T>) object.getClass();
	// try {
	// // oemId
	// Method method = clazz.getMethod("setOemId", String.class);
	// method.invoke(object, "1");
	// } catch (Exception e) {
	// }
	// try {
	// // oemCode
	// Method method1 = clazz.getMethod("setOemCode", String.class);
	// method1.invoke(object, "1");
	// } catch (Exception e) {
	// }
	// try {
	// // groupId
	// Method method2 = clazz.getMethod("setGroupId", String.class);
	// method2.invoke(object, "1");
	// } catch (Exception e) {
	// }
	// try {
	// // groupCode
	// Method method3 = clazz.getMethod("setGroupCode", String.class);
	// method3.invoke(object, "1");
	// } catch (Exception e) {
	// }
	// try {
	// // 创建人名称
	// Method method4 = clazz.getMethod("setCreatedName", String.class);
	// method4.invoke(object, "SYS");
	// } catch (Exception e) {
	// }
	// try {
	// // 修改人名称
	// Method method5 = clazz.getMethod("setModifyName", String.class);
	// method5.invoke(object, "SYS");
	// } catch (Exception e) {
	// }
	// try {
	// // 最後修改人 modifier
	// Method method6 = clazz.getMethod("setModifier", String.class);
	// method6.invoke(object, "SYS");
	// } catch (Exception e) {
	// }
	// try {
	// // 创建人
	// Method method7 = clazz.getMethod("setCreator", String.class);
	// method7.invoke(object, "SYS");
	// } catch (Exception e) {
	// }
	// try {
	// // 并发控制Id updateDateControlId
	// Method method8 = clazz.getMethod("setUpdateControlId", String.class);
	// method8.invoke(object, UUID.randomUUID().toString());
	// } catch (Exception e) {
	// }
	// try {
	// // 创建时间 createdDate
	// Method method9 = clazz.getMethod("setCreatedDate", LocalDateTime.class);
	// method9.invoke(object, LocalDateTime.now());
	// } catch (Exception e) {
	// }
	// try {
	// // 最後更新時間 lastUpdateDate
	// Method method10 = clazz.getMethod("setLastUpdatedDate", LocalDateTime.class);
	// method10.invoke(object, LocalDateTime.now());
	// } catch (Exception e) {
	// }
	// try {
	// // IsEnable
	// Method method11 = clazz.getMethod("setIsEnable", String.class);
	// method11.invoke(object, "1");
	// } catch (Exception e) {
	// }
	// try {
	// // SdpOrgId
	// Method method12 = clazz.getMethod("setSdpOrgId", String.class);
	// method12.invoke(object, "2");
	// } catch (Exception e) {
	// }
	// try {
	// // SdpUserId
	// Method method13 = clazz.getMethod("setSdpUserId", String.class);
	// method13.invoke(object, "88888");
	// } catch (Exception e) {
	// }
	// try {
	// // IsSystem
	// Method method14 = clazz.getMethod("setIsSystem", String.class);
	// method14.invoke(object, "1");
	// } catch (Exception e) {
	// }
	// return object;
	// }

	/**
	 *
	 * Copyright: Copyright (c) 2019 ly-tianh
	 *
	 * @Description: eap实体赋值
	 * @version: v1.0.0
	 * @author: ly-tianh
	 * @date: 2019年7月24日 上午10:09:23 Modification History: Date Author Version
	 *        Description ---------------------------------------------------------*
	 *        2019年7月24日 ly-tianh v1.0.0 修改原因
	 */
	@SuppressWarnings("unchecked")
	public static <T> T invokeEapUserInfo(T object, int isSave, String token) {
		if (object == null) {
			return null;
		}
		UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
		if (userBusiEntity == null) {
			userBusiEntity = new UserBusiEntity();
			userBusiEntity.setUserID("1");
		}
		Class<T> clazz = (Class<T>) object.getClass();
		try {
			// 保存
			if (isSave == 1) {
				// 最後修改人 modifier
				Method method6 = clazz.getMethod("setLastUpdatedBy", String.class);
				method6.invoke(object, userBusiEntity.getUserID());
				// 创建人
				Method method7 = clazz.getMethod("setCreatedBy", String.class);
				method7.invoke(object, userBusiEntity.getUserID());
				// 创建时间 createdDate
				Method method9 = clazz.getMethod("setCreatedTime", LocalDateTime.class);
				method9.invoke(object, LocalDateTime.now());
				// 最後更新時間 lastUpdateDate
				Method method10 = clazz.getMethod("setLastUpdatedTime", LocalDateTime.class);
				method10.invoke(object, LocalDateTime.now());
			} else {
				// 修改
				// 最後更新時間 lastUpdateDate
				Method method = clazz.getMethod("setLastUpdatedTime", LocalDateTime.class);
				method.invoke(object, LocalDateTime.now());
				// 最後修改人 modifier
				Method method1 = clazz.getMethod("setLastUpdatedBy", String.class);
				method1.invoke(object, userBusiEntity.getUserID());
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}

		return object;
	}

	/**
	 *
	 * @Description: 扩展字段的分页查询
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月14日 下午4:27:15
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月14日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	public static <T> ListResult<T> convert2Dynamic(Class<T> clazz, ListResult<?> listResult) {
		if (listResult == null) {
			return null;
		}
		ListResult<T> result = new ListResult<>();
		List<T> convertList = BusicenConvert.convertList(clazz, listResult.getRows());
		BeanUtils.copyProperties(listResult, result, "rows");
		result.setRows(convertList);
		if ("0".equals(listResult.getResult())) {
			result.setMsg(listResult.getMsg());
		} else {
			result.setResult("1");
			result.setMsg("查询成功");
		}
		return result;
	}

	/**
	 *
	 * @Description: 扩展字段的单条查询
	 *
	 * @version: v1.0.0
	 * @author: 尹加豪
	 * @date: 2019年11月25日
	 */
	public static <T> EntityResult<T> convert2Dynamic(Class<T> clazz, EntityResult<?> entityResult) {
		if (entityResult == null) {
			return null;
		}
		EntityResult<T> result = new EntityResult<>();
		T convertObj = BusicenConvert.convert(clazz, entityResult.getRows());
		BeanUtils.copyProperties(entityResult, result, "rows");
		result.setRows(convertObj);
		// if ("0".equals(entityResult.getResult())) {
		// result.setMsg(entityResult.getMsg());
		// } else {
		// result.setResult("1");
		// result.setMsg("查询成功");
		// }
		return result;
	}

	/**
	 *
	 * @Description: List 转 String
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月16日 下午8:14:40
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月16日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	public static String listToString(List<String> mList) {
		String convertedListStr = "";
		if (null != mList && mList.size() > 0) {
			String[] mListArray = mList.toArray(new String[mList.size()]);
			for (int i = 0; i < mListArray.length; i++) {
				if (i < mListArray.length - 1) {
					convertedListStr += mListArray[i] + ",";
				} else {
					convertedListStr += mListArray[i];
				}
			}
			return convertedListStr;
		} else {
			return null;
		}
	}

	/**
	 *
	 * @Description: 驼峰转下划线
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月19日 下午2:00:43
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月19日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	public static String HumpToUnderline(String para) {
		StringBuilder sb = new StringBuilder(para);
		int temp = 0;// 定位
		if (!para.contains("_")) {
			for (int i = 0; i < para.length(); i++) {
				if (Character.isUpperCase(para.charAt(i))) {
					sb.insert(i + temp, "_");
					temp += 1;
				}
			}
		}
		return sb.toString().toUpperCase();
	}

	/**
	 *
	 * @Description: 下划线转驼峰命名
	 * <AUTHOR>
	 * @date 2019年8月29日
	 */
	public static String UnderLineToHump(String param) {
		StringBuilder sb = new StringBuilder();

		boolean nextUpperCase = false;
		for (int i = 0; i < param.length(); i++) {
			char c = param.charAt(i);
			if (c == '_') {
				if (sb.length() > 0) {
					nextUpperCase = true;
				}
			} else {
				if (nextUpperCase) {
					sb.append(Character.toUpperCase(c));
					nextUpperCase = false;
				} else {
					sb.append(Character.toLowerCase(c));
				}
			}
		}
		return sb.toString();
	}

	/**
	 *
	 * @Description: 下划线转驼峰命名
	 * <AUTHOR>
	 * @date 2019年12月26日
	 */
	public static Map<String, Object> UnderLineToHump(Map<String, Object> paramMap) {
		Map<String, Object> newMap = new HashMap<>();
		Iterator<Map.Entry<String, Object>> it = paramMap.entrySet().iterator();
		while (it.hasNext()) {
			Map.Entry<String, Object> entry = it.next();
			String key = entry.getKey();
			String newKey = UnderLineToHump(key);
			newMap.put(newKey, entry.getValue());
		}
		return newMap;
	}

	/**
	 *
	 * @Description: Manggo里指定返回字段时获取主表所有字段
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月19日 下午2:01:03
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月19日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	public static <T> String getEntityFields(T info) {
		Field[] fields = info.getClass().getDeclaredFields();
		StringBuffer buffer = new StringBuffer();
		for (int i = 0; i < fields.length; i++) {
			if ("serialVersionUID".contentEquals(fields[i].getName())) {
				continue;
			}
			buffer.append(HumpToUnderline("\"" + fields[i].getName()) + "\",");
		}
		buffer.deleteCharAt(buffer.length() - 1);
		return buffer.toString();
	}

	@SuppressWarnings("unchecked")
	public static <T> T getCodeForCheckRehedit(T object, String token) {
		if (StringHelper.IsEmptyOrNull(object)) {
			return null;
		}
		UserBusiEntity userBusiEntity = BusicenContext.getCurrentUserBusiInfo(token);
		Class<T> clazz = (Class<T>) object.getClass();
		Method method1;
		try {
			// 获得OEM_CODE
			method1 = clazz.getMethod("setOemCode", String.class);
			method1.invoke(object, userBusiEntity.getOemCode());
			// 获得GROUP_CODE
			Method method3 = clazz.getMethod("setGroupCode", String.class);
			method3.invoke(object, userBusiEntity.getGroupCode());
		} catch (Exception e) {
			e.printStackTrace();
		}

		return object;
	}

	/**
	 *
	 * @Description: Map 转 实体
	 *
	 * @version: v1.0.0
	 * @author: ly-wyifan
	 * @date: 2019年7月25日 下午5:08:12
	 *
	 *        Modification History: Date Author Version Description
	 *        ---------------------------------------------------------* 2019年7月25日
	 *        ly-wyifan v1.0.0 修改原因
	 */
	private static final String FORMATER = "yyyy-MM-dd HH:mm:ss";
	// private static final String FORMATER_T = "yyyy-MM-ddTHH:mm:ss";
	private static final String FORMATER_T = "yyyy-MM-dd['T'HH:mm:ss[Z]]";

//	public static <T> T map2Object(Map<String, Object> map, Class<T> clazz) {
//		if (map == null) {
//			return null;
//		}
//		T obj = null;
//		try {
//			obj = clazz.newInstance();
//
//			for (Class<?> clazzz = clazz; !clazzz.equals(Object.class); clazzz = clazzz.getSuperclass()) {
//				Field[] fields = clazzz.getDeclaredFields();
//				for (Field field : fields) {
//					Object value = map.get(field.getName());
//					if (value == null) {
//						continue;
//					}
//					int mod = field.getModifiers();
//					if (Modifier.isStatic(mod) || Modifier.isFinal(mod)) {
//						continue;
//					}
////					field.setAccessible(true);
//					if (field.getType().equals(LocalDateTime.class)) {
//						LocalDateTime temp = null;
//						if (value instanceof LocalDateTime) {
//							temp = (LocalDateTime) value;
//						} else if (value instanceof String) {
//							if (StringUtils.hasText(value.toString())) {
//								try {
//									if (value.toString().contains("T")) {
//										temp = LocalDateTime.parse(value.toString(),
//												DateTimeFormatter.ofPattern(FORMATER_T));
//									} else {
//										temp = LocalDateTime.parse(value.toString(),
//												DateTimeFormatter.ofPattern(FORMATER));
//									}
//
//								} catch (Exception e) {
//
//								}
//							}
//						} else if (value instanceof Long) {
//							try {
//								Instant instant = Instant.ofEpochMilli((Long) value);
//								ZoneId zone = ZoneId.systemDefault();
//								temp = LocalDateTime.ofInstant(instant, zone);
//							} catch (Exception e) {
//
//							}
//						} else if (value instanceof Integer) {
//							try {
//								Long timestamp = ((Integer) value).longValue() * 1000;
//								Instant instant = Instant.ofEpochMilli(timestamp);
//								ZoneId zone = ZoneId.systemDefault();
//								temp = LocalDateTime.ofInstant(instant, zone);
//							} catch (Exception e) {
//
//							}
//						} else if (value instanceof Timestamp) {
//							temp = ((Timestamp) value).toLocalDateTime();
//						}
//
////						field.set(obj, temp);
//						continue;
//
//					}
//
//					if (field.getType().equals(BigDecimal.class)) {
//						String bigDecimal = String.valueOf(map.get(field.getName()));
//						if (bigDecimal.equalsIgnoreCase("null")) {
////							field.set(obj, null);
//						} else {
//							field.set(obj, new BigDecimal(bigDecimal));
//						}
//						continue;
//					}
//
//					if (field.getType().equals(Integer.class)) {
//						String integer = String.valueOf(map.get(field.getName()));
//						if (integer.equalsIgnoreCase("null")) {
//							field.set(obj, null);
//						} else {
//							field.set(obj, new Integer(integer));
//						}
//						continue;
//					}
//
//					field.set(obj, map.get(field.getName()));
//
//				}
//			}
//
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return obj;
//	}

	public static ListToStr list2Str() {
		return new ListToStr();
	}

	public static go strToList() {
		return new go();
	}

	public static class go {

		List<String> list = new ArrayList<String>();

		@SuppressWarnings("null")
		public go add(String... str) {
			if (str == null) {
				return this;
			}
			for (String string : str) {
				list.add(string);
			}

			return this;
		}

		public Map<String, Object> build(Map<String, Object> mapParam) {
			if (list.size() == 0) {
				return mapParam;
			}
			for (String str : list) {
				if (!StringHelper.IsEmptyOrNull(mapParam.get(str))) {
					mapParam.put(str, Arrays.asList(mapParam.get(str).toString().split(",")));
				} else {
					mapParam.put(str, new ArrayList<String>());
				}
			}
			return mapParam;
		}

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static <T> T oemGroupforQuery(T info, String token) {
		String oemCode = BusicenContext.getCurrentUserBusiInfo(token).getOemCode();
		String groupCode = BusicenContext.getCurrentUserBusiInfo(token).getGroupCode();
		Class clazz = info.getClass();
		try {
			if (info instanceof Map) {
				if (StringHelper.IsEmptyOrNull(((Map) info).get("oemCode"))) {
					((Map) info).put("oemCode", oemCode);
				}
				if (StringHelper.IsEmptyOrNull(((Map) info).get("groupCode"))) {
					((Map) info).put("groupCode", groupCode);
				}
			} else {
				Field oemCodeField = clazz.getDeclaredField("oemCode");
				Field groupCodeField = clazz.getDeclaredField("groupCode");
//				oemCodeField.setAccessible(true);
//				groupCodeField.setAccessible(true);
				if (StringHelper.IsEmptyOrNull(oemCodeField.get(info))) {
					Method method = clazz.getMethod("setOemCode", String.class);
					method.invoke(info, oemCode);
				}
				if (StringHelper.IsEmptyOrNull(groupCodeField.get(info))) {
					Method method1 = clazz.getMethod("setGroupCode", String.class);
					method1.invoke(info, groupCode);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return info;

	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
//	public static <T> T oemGroupDlrforQuery(T info, String token) {
//		String oemCode = BusicenContext.getCurrentUserBusiInfo(token).getOemCode();
//		String groupCode = BusicenContext.getCurrentUserBusiInfo(token).getGroupCode();
//		String dlrId = BusicenContext.getCurrentUserBusiInfo(token).getDlrID();
//		Class clazz = info.getClass();
//		try {
//			if (info instanceof Map) {
//				if (StringHelper.IsEmptyOrNull(((Map) info).get("oemCode"))) {
//					((Map) info).put("oemCode", oemCode);
//				}
//				if (StringHelper.IsEmptyOrNull(((Map) info).get("groupCode"))) {
//					((Map) info).put("groupCode", groupCode);
//				}
//				if (StringHelper.IsEmptyOrNull(((Map) info).get("dlrId"))) {
//					((Map) info).put("dlrId", dlrId);
//				}
//			} else {
//				Field oemCodeField = clazz.getDeclaredField("oemCode");
//				Field groupCodeField = clazz.getDeclaredField("groupCode");
//				oemCodeField.setAccessible(true);
//				groupCodeField.setAccessible(true);
//				if (StringHelper.IsEmptyOrNull(oemCodeField.get(info))) {
//					Method method = clazz.getMethod("setOemCode", String.class);
//					method.invoke(info, oemCode);
//				}
//				if (StringHelper.IsEmptyOrNull(groupCodeField.get(info))) {
//					Method method1 = clazz.getMethod("setGroupCode", String.class);
//					method1.invoke(info, groupCode);
//				}
//				Field[] fields = clazz.getDeclaredFields();
//				for (int i = 0; i < fields.length; i++) {
//					Field f = fields[i];
//					if (f.getName().equals("dlrId")) {
//						f.setAccessible(true);
//						if (StringHelper.IsEmptyOrNull(f.get(info))) {
//							Method method1 = clazz.getMethod("setDlrId", String.class);
//							method1.invoke(info, dlrId);
//						}
//					}
//				}
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return info;
//
//	}

	public static Map<String, Object> entityToMap(Object entity) {
		if (entity == null) {
			return null;
		}
		Map<String, Object> result = new HashMap<String, Object>();
		try {
			BeanInfo beanInfo = Introspector.getBeanInfo(entity.getClass());
			PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
			for (PropertyDescriptor m : propertyDescriptors) {
				String name = m.getName();
				if (name.equals("callback") || name.equals("callbacks") || name.equals("class")) {
					continue;
				}
				Method method = m.getReadMethod();
				result.put(name, method.invoke(entity));
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	public static<T> T copyProperties(Class<T> classz, Object source) {
		Object o=null;
		if (classz!=null) {
			try {
				o=classz.newInstance();
//				copyProperties(o,source);
			} catch (IllegalAccessException | InstantiationException e) {
			}
		}
		return (T)o;

	}


//	public static void copyProperties(Object dest, Object source) {
//		if (source == null) {
//			return;
//		}
//		try {
//
//			BeanInfo beanInfo = Introspector.getBeanInfo(dest.getClass());
//			PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
//			for (PropertyDescriptor m : propertyDescriptors) {
//				if (m != null) {
//					Method writeMethod = m.getWriteMethod();
//					if (writeMethod == null) {
//						continue;
//					}
//					PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), m.getName());
//					if (sourcePd != null) {
//
//						if (m.getPropertyType() != sourcePd.getPropertyType()) {// 属性值名称一样，类型不一样特殊处理
//							if (m.getPropertyType() == LocalDateTime.class
//									&& sourcePd.getPropertyType() == String.class) {
//								Method readMethod = sourcePd.getReadMethod();
//								if (readMethod != null) {
//									try {
//										if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
////											readMethod.setAccessible(true);
//										}
//										// 通过反射获取source对象属性的值
//										Object value = readMethod.invoke(source);
//										if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
//											writeMethod.setAccessible(true);
//										}
//										if (null != value && value.toString().length() == 10) {
//											DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//											LocalDateTime localDate = LocalDateTime
//													.parse(value.toString() + " 00:00:00", df);
//											writeMethod.invoke(dest, localDate);
//										} else if (null != value && value.toString().length() == 19) {
//											String datevalue = value.toString().replace("T", " ");
//											DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//											LocalDateTime localDate = LocalDateTime.parse(datevalue.toString(), df);
//											writeMethod.invoke(dest, localDate);
//										} else if (null != value && value.toString().length() > 19) {
//											String datevalue = value.toString().replace("T", " ");
//											datevalue = datevalue.substring(0, 18);
//											DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//											LocalDateTime localDate = LocalDateTime.parse(datevalue.toString(), df);
//											writeMethod.invoke(dest, localDate);
//										}
//									} catch (Throwable ex) {
//										throw ex;
//									}
//								}
//							}
//						} else {
//							Method readMethod = sourcePd.getReadMethod();
//							if (readMethod != null) {
//								try {
//									if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
//										readMethod.setAccessible(true);
//									}
//									// 通过反射获取source对象属性的值
//									Object value = readMethod.invoke(source);
//									if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
//										writeMethod.setAccessible(true);
//									}
//									// 通过反射给target对象属性赋值
//									writeMethod.invoke(dest, value);
//								} catch (Throwable ex) {
//									throw ex;
//								}
//							}
//						}
//					}
//				}
//			}
//		} catch (Exception ex) {
//			throw new RuntimeException(ex);
//		}
//	}

	/**
	 * 获取accessToken
	 *
	 * <AUTHOR>
	 * @version 2020年6月9日 下午2:00:50
	 *
	 */

	public static String getAccessToken() {
		String accessToken = "";
		String saveJason = "";

		try {
			Long currenttime = System.currentTimeMillis();
			URL url = new URL(
					"https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=xxx&secret=xxx");
			HttpURLConnection urlcon = (HttpURLConnection) url.openConnection();
			urlcon.connect(); // 获取连接
			InputStream is = urlcon.getInputStream();
			BufferedReader buffer = new BufferedReader(new InputStreamReader(is));
			StringBuffer bs = new StringBuffer();
			String str = null;
			while ((str = buffer.readLine()) != null) {
				bs.append(str);
			}
			JSONObject json = (JSONObject) JSONObject.parse(bs.toString());
			System.out.println("------------" + json.getString("access_token"));
			accessToken = json.getString("access_token");
			Long currenttime2 = System.currentTimeMillis();
			// if((currenttime2-currenttime)/1000/60/60>1) {
			//
			// }

		} catch (Exception e) {
			e.printStackTrace();
		}

		return accessToken;
	}

	/**
	 *
	 * 验证用户是否正常登录
	 *
	 * <AUTHOR>
	 * @date 2021年4月29日
	 * @param user
	 */
	public static void validIsLogin(UserBusiEntity user) {
		if (user == null) { // 一般不会为空，写上也没错
			throw BusicenException.create("登录用户信息为空");
		}
		if ((StringHelper.IsEmptyOrNull(user.getUserID()) && StringHelper.IsEmptyOrNull(user.getEmpID()))
				|| (user.getUserID().equals("1") && user.getEmpID().equals("1"))) {
			throw BusicenException.create("登录过期，请重新登录");
		}
	}
}
