package com.ly.mp.busicen.common.handler;

import java.time.LocalDateTime;

import org.apache.ibatis.reflection.MetaObject;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ly.mp.busicen.common.constant.UserBusiEntity;
import com.ly.mp.busicen.common.context.BusicenContext;
import com.ly.mp.component.helper.StringHelper;

/**
 * mybatis的元对象字段填充控制器抽象类，实现公共字段自动写入
 * <AUTHOR>
 * 日期：2019-07-11
 */
public class MetaHandler implements MetaObjectHandler{

	/**
	 * 新增数据执行
	 */
	@Override
	public void insertFill(MetaObject metaObject) {
		try{
			//获取当前用户信息
			UserBusiEntity busiUser = BusicenContext.getCurrentUserBusiInfo();
			if (null == busiUser) {
				return;
			}
			
			Object modifier = this.getFieldValByName("modifier", metaObject);
			Object creator = this.getFieldValByName("creator", metaObject);
			Object oemId = this.getFieldValByName("oemId", metaObject);
			Object groupId = this.getFieldValByName("groupId", metaObject);
			Object oemCode = this.getFieldValByName("oemCode", metaObject);
			Object groupCode = this.getFieldValByName("groupCode", metaObject);
			Object createdName = this.getFieldValByName("createdName", metaObject);
			Object modifyName = this.getFieldValByName("modifyName", metaObject);
			Object createdDate = this.getFieldValByName("createdDate", metaObject);
			Object lastUpdatedDate = this.getFieldValByName("lastUpdatedDate", metaObject);


			if (null == modifier) {
				this.setFieldValByName("modifier", busiUser.getUserID(), metaObject);
			}
			if (null == modifyName) {
				this.setFieldValByName("modifyName", busiUser.getEmpName(), metaObject);
			}
			
			if (null == oemId) {
				this.setFieldValByName("oemId", busiUser.getOemID(), metaObject);
			}
			if (null == creator) {
				this.setFieldValByName("creator", busiUser.getUserID(), metaObject);
			}
			if (null == groupId) {
				this.setFieldValByName("groupId", busiUser.getGroupID(), metaObject);
			}
			if (null == oemCode) {
				this.setFieldValByName("oemCode", busiUser.getOemCode(), metaObject);
			}
			if (null == groupCode) {
				this.setFieldValByName("groupCode", busiUser.getGroupCode(), metaObject);
			}
			if (null == createdName) {
				this.setFieldValByName("createdName", busiUser.getEmpName(), metaObject);
			}
			if (null == createdDate) {
				this.setFieldValByName("createdDate", LocalDateTime.now(), metaObject);
			}
			if (null == lastUpdatedDate) {
				this.setFieldValByName("lastUpdatedDate", LocalDateTime.now(), metaObject);
			}
			this.setFieldValByName("updateControlId",StringHelper.GetGUID(), metaObject);
			}
			catch(Exception e){
				throw new RuntimeException(e);
			}
		
	}

	/**
	 * 更新数据执行
	 */
	@Override
	public void updateFill(MetaObject metaObject) {
		try{
			//获取当前用户信息
			UserBusiEntity user = BusicenContext.getCurrentUserBusiInfo();
			if (null == user) {
				return;
			}
			
			Object modifier = this.getFieldValByName("modifier", metaObject);
			Object oemId = this.getFieldValByName("oemId", metaObject);
			Object groupId = this.getFieldValByName("groupId", metaObject);
			Object oemCode = this.getFieldValByName("oemCode", metaObject);
			Object groupCode = this.getFieldValByName("groupCode", metaObject);
			Object modifyName = this.getFieldValByName("modifyName", metaObject);
			Object lastUpdatedDate = this.getFieldValByName("lastUpdatedDate", metaObject);

			if (null == modifier) {
				this.setFieldValByName("modifier", user.getUserID(), metaObject);
			}
			if (null == modifyName) {
				this.setFieldValByName("modifyName", user.getEmpName(),metaObject);
			}
			
			if (null == oemId) {
				this.setFieldValByName("oemId", user.getOemID(), metaObject);
			}
			if (null == groupId) {
				this.setFieldValByName("groupId", user.getGroupID(), metaObject);
			}
			if (null == oemCode) {
				this.setFieldValByName("oemCode", user.getOemCode(), metaObject);
			}
			if (null == groupCode) {
				this.setFieldValByName("groupCode", user.getGroupCode(), metaObject);
			}
			if (null == lastUpdatedDate) {
				this.setFieldValByName("lastUpdatedDate", LocalDateTime.now(), metaObject);
			}
			this.setFieldValByName("updateControlId",StringHelper.GetGUID(), metaObject);
			}
			catch(Exception e){
				throw new RuntimeException(e);
			}
	}

	@Override
	public boolean openInsertFill() {
		// TODO Auto-generated method stub
		return MetaObjectHandler.super.openInsertFill();
	}

	@Override
	public boolean openUpdateFill() {
		// TODO Auto-generated method stub
		return MetaObjectHandler.super.openUpdateFill();
	}

	
}
