/**   
 * Copyright © 2019 eSunny Info. Tech Ltd. All rights reserved.
 * 
 * 功能描述：
 * @Package: com.ly.mp.busicen.common.util 
 * @author: ly-wyifan   
 * @date: 2019年7月3日 下午10:13:32 
 */
package com.ly.mp.busicen.common.util;

import com.ly.mp.component.entities.OptResult;

/**   
* Copyright: Copyright (c) 2019 lanyou-wyifan
* 
* @Description: 该类的功能描述
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年7月3日 下午10:13:32 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年7月3日     ly-wyifan           v1.0.0               修改原因
*/
public class OptResultBuilder {
	
		private OptResult instance;
		
		public static OptResultBuilder create() {
			OptResultBuilder builder = new OptResultBuilder();
			builder.instance=new OptResult();			
			return builder;
		}
		
		public static OptResultBuilder createOk() {
			OptResultBuilder builder =create();
			builder.instance.setMsg("操作成功");
			builder.instance.setResult("1");
			return builder;
		}
		
		public static OptResultBuilder createFail() {
			return createFail("操作失败");
		}
		
		public static OptResultBuilder createFail(String msg) {
			OptResultBuilder builder =create();
			builder.instance.setMsg(msg);
			builder.instance.setResult("0");
			return builder;
		}
		
		public OptResultBuilder setResult(String result) {
			this.instance.setResult(result);
			return this;
		}
		
		public OptResultBuilder result(String result) {
			this.instance.setResult(result);
			return this;
		}
		
		public OptResultBuilder setMsg(String msg) {
			this.instance.setMsg(msg);
			return this;
		}
		
		public OptResultBuilder Msg(String msg) {
			this.instance.setMsg(msg);
			return this;
		}
		
		public OptResult build() {
			return instance;
		}
		

}
