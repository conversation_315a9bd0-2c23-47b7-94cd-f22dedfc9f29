package com.ly.mp.busicen.common.entity;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.ly.mp.component.entities.EntityResult;

@Component
public class EntityResultExt<T> extends EntityResult<T>{
	private static final long serialVersionUID = 1L;

	@Value("${busicen.errmsgstack}")
    public Boolean errmsgflag;
	
	private String errStackMsgString;

	public String getErrStackMsgString() {
		return errStackMsgString;
	}

	public void setErrStackMsgString(String errStackMsgString) {
		if(errmsgflag)
		{
			this.errStackMsgString = errStackMsgString;
		}
		else {
			this.errStackMsgString = "";
		}
	}
}
