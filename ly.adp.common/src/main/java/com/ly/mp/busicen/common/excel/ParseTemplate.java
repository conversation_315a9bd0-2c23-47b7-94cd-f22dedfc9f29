package com.ly.mp.busicen.common.excel;


/**
 * <AUTHOR>
 * @date 2017年6月5日
 *
 */
public class ParseTemplate {
	private String sheetName;// 工作簿
	private Integer dataTitleRow;
	private Integer dataBegRow;
	
	public static class ParseTemplateBuilder{
		private ParseTemplate parent=new ParseTemplate();
		public static ParseTemplateBuilder create() {
			return new ParseTemplateBuilder();
		}
		public ParseTemplate build() {
			return this.parent;
		}
		
		public ParseTemplateBuilder sheetName(String sheetName) {
			parent.setSheetName(sheetName);
			return this;
		}
		public ParseTemplateBuilder dataTitleRow(Integer dataTitleRow) {
			parent.setDataTitleRow(dataTitleRow);
			return this;
		}
		public ParseTemplateBuilder dataBegRow(Integer dataBegRow) {
			parent.setDataBegRow(dataBegRow);
			return this;
		}
	}

	public ParseTemplate(){
		
	}
	
	public Integer getDataTitleRow() {
		return dataTitleRow;
	}

	public void setDataTitleRow(Integer dataTitleRow) {
		this.dataTitleRow = dataTitleRow;
	}

	public String getSheetName() {
		return sheetName;
	}

	public void setSheetName(String sheetName) {
		this.sheetName = sheetName;
	}

	public Integer getDataBegRow() {
		return dataBegRow;
	}

	public void setDataBegRow(Integer dataBegRow) {
		this.dataBegRow = dataBegRow;
	}

}