package com.ly.mp.busicen.common.util;

//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.properties.ConfigurationProperties;

/*
 * 获取配置文件server节点配置
 * 配置节点必须是存在的，否则会报错导致程序启动不了
 */
//@ConfigurationProperties(prefix = "server")
//public class ConfigGetServerUtil
//{
//	private static String port;
//
//	public static String getPort() {
//		return port;
//	}
//    @Value("${server.port}")
//    public void setPort(String port) {
//    	ConfigGetServerUtil.port = port;
//    }
//}
