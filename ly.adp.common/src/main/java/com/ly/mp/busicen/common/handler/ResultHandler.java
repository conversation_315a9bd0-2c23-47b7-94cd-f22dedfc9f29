package com.ly.mp.busicen.common.handler;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.mp.busicen.common.constant.SuccessEnum;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;


/**
 * 封装响应
 * <AUTHOR>
 *
 */
public class ResultHandler {

	public static <T> ListResult<T> queryOk(IPage<T> page) {
		ListResult<T> listResult = new ListResult<T>();
		if (null != page) {
			listResult.setPageindex((int) page.getCurrent());
			listResult.setPages((int) page.getPages());
			listResult.setRecords((int) page.getTotal());
			listResult.setRows(page.getRecords());
		}
		listResult.setResult(SuccessEnum.SUCCESS.getResult());
		listResult.setMsg(SuccessEnum.SUCCESS.getMsg());
		return listResult;
	}
	
	public static <T> ListResult<T> queryOk(IPage<T> page,List<T> list) {
		if (null == page) {
			page = new Page<>();
		}
		page.setRecords(list);
		return queryOk(page);
	}
	
	public static <T> ListResult<T> queryError(String result, String msg) {
		ListResult<T> listResult = new ListResult<>();
		listResult.setResult(result);
		listResult.setMsg(msg);
		return listResult;
	}
	public static <T> ListResult<T> queryOk(String result, String msg) {
		ListResult<T> listResult = new ListResult<>();
		listResult.setResult(result);
		listResult.setMsg(msg);
		return listResult;
	}

	public static OptResult updateOk() {
		OptResult optResult = new OptResult();
		optResult.setResult(SuccessEnum.SUCCESS.getResult());
		optResult.setMsg(SuccessEnum.SUCCESS.getMsg());
		return optResult;
	}
	
	public static <T> EntityResult<T> updateOk(T info) {
		EntityResult<T> optResult = new EntityResult<T>();
		optResult.setResult(SuccessEnum.SUCCESS.getResult());
		optResult.setMsg(SuccessEnum.SUCCESS.getMsg());
		optResult.setRows(info);
		
		return optResult;
	}
	
	public static OptResult updateError(String result, String msg) {
		OptResult optResult = new OptResult();
		optResult.setResult(result);
		optResult.setMsg(msg);
		return optResult;
	}
	
	public static <T> EntityResult<T> updateError(String result, String msg, String extText) {
		EntityResult<T> optResult = new EntityResult<T>();
		optResult.setResult(result);
		optResult.setMsg(msg);
		optResult.setExtInfo(extText);
		
		return optResult;
	}
	
}
