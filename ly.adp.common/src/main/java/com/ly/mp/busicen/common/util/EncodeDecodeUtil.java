package com.ly.mp.busicen.common.util;

public class EncodeDecodeUtil {
	  //过滤通过页面表单提交的字符   
    private static String[][] FilterChars={{"<",""},{">",""},{"=",""},{"\"",""},{"\'",""},{"&",""},   
                                    {"/",""},{"\\",""},{"\n",""}};   

	
    /**  
     * 用特殊的字符连接字符串  
     * @param strings 要连接的字符串数组  
     * @param spilit_sign 连接字符  
     * @return 连接字符串  
     */  
    public static String stringConnect(String[] strings,String spilit_sign){   
      String str="";   
      for(int i=0;i<strings.length;i++){   
        str+=strings[i]+spilit_sign;   
      }   
      return str;   
    } 
    
    /**  
     * 过滤字符串里的的特殊字符  
     * @param str 要过滤的字符串  
     * @return 过滤后的字符串  
     */  
    public static String stringFilter(String str){   
      String[] str_arr=stringSpilit(str,"");   
      for(int i=0;i<str_arr.length;i++){   
        for(int j=0;j<FilterChars.length;j++){   
          if(FilterChars[j][0].equals(str_arr[i])) {
            str_arr[i]=FilterChars[j][1];
          }
        }   
      }   
      return (stringConnect(str_arr,"")).trim();   
    }   
    
    /**  
     * 分割字符串  
     * @param str 要分割的字符串  
     * @param spilit_sign 字符串的分割标志  
     * @return 分割后得到的字符串数组  
     */  
    public static String[] stringSpilit(String str,String spilit_sign){   
      String[] spilit_string=str.split(spilit_sign);   
      if(spilit_string[0].equals(""))   
      {   
        String[] new_string=new String[spilit_string.length-1];   
        for(int i=1;i<spilit_string.length;i++)   {
          new_string[i-1]=spilit_string[i];   
        }
          return new_string;   
      }   
      else  {
        return spilit_string;   
      }
    }   
    
    /**  
     * 字符串字符集转换  
     * @param str 要转换的字符串  
     * @return 转换过的字符串  
     */  
    public static String stringTransCharset(String str){   
      String new_str=null;   
      try{   
          new_str=new String(str.getBytes("iso-8859-1"),"GBK");   
      }   
      catch(Exception e){   
        e.printStackTrace();   
      }   
      return new_str;   
    }  
}