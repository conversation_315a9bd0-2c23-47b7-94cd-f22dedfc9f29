package com.ly.mp.busicen.common.context;

import java.util.function.Supplier;

import org.apache.poi.ss.formula.functions.T;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import com.ly.mp.busicen.common.helper.SpringContextHolder;


public class BusicenTransNormalTemplate {
	
	public static void doTrans(SupplierTemp supplier) {
		PlatformTransactionManager platformTransactionManager = SpringContextHolder.getBean(PlatformTransactionManager.class);
		DefaultTransactionDefinition def = new DefaultTransactionDefinition();		
		def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		def.setIsolationLevel(2);
	    TransactionStatus status = null;
	    try {	    	
	    	status=platformTransactionManager.getTransaction(def);
	    	supplier.doing();
	    	platformTransactionManager.commit(status);
	    }catch(Exception e) {
	    	platformTransactionManager.rollback(status);
	    	throw e;
	    }		
	}
	
	@SuppressWarnings("hiding")
	public static <T> T doTrans(Supplier<T> supplier) {
		PlatformTransactionManager platformTransactionManager = SpringContextHolder.getBean(PlatformTransactionManager.class);
		DefaultTransactionDefinition def = new DefaultTransactionDefinition();		
		def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);
		def.setIsolationLevel(2);
	    TransactionStatus status = null;
	    T result;
	    try {	    	
	    	status=platformTransactionManager.getTransaction(def);
	    	result = supplier.get();
	    	platformTransactionManager.commit(status);
	    }catch(Exception e) {
	    	platformTransactionManager.rollback(status);
	    	throw e;
	    }
	    return result;
	}
	
	@FunctionalInterface
	public static interface SupplierTemp{
		void doing();
	}
	
}
