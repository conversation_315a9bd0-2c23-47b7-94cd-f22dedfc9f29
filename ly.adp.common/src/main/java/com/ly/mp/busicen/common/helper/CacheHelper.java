package com.ly.mp.busicen.common.helper;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.szlanyou.common.redis.util.RedisUtil;

@Component
public class CacheHelper {
	@Autowired
	private RedisUtil redisUtil;
	
	private static CacheHelper cacheHelper;
	
	@PostConstruct
	public void init() {
		cacheHelper = this;
		cacheHelper.redisUtil = this.redisUtil;
	}
	        
	public static Object get(String cacheName,String key)
	{
		return cacheHelper.redisUtil.get(cacheName+"-"+key);
	}
	public static Object get(String key)
	{
		return cacheHelper.redisUtil.get(key);
	}
	public static boolean put(String cacheName,String key, String value,int timeLength)
	{
	    return cacheHelper.redisUtil.set(cacheName+"-"+key, value, timeLength);
	}
	public static boolean put(String cacheName,String key, String value)
	{
	    return cacheHelper.redisUtil.set(cacheName+"-"+key, value);
	}
	public static boolean put(String cacheName,String key, int value)
	{
	    return cacheHelper.redisUtil.set(cacheName+"-"+key, String.valueOf(value));
	}
	public static boolean put(String cacheName,String key, Object value)
	{
	    return cacheHelper.redisUtil.set(cacheName+"-"+key, value);
	}
	
	public static boolean put(String cacheName,String key, Object value,int timeLength)
	{
	    return cacheHelper.redisUtil.set(cacheName+"-"+key, value,timeLength);
	}
	
	public static void remove(String cacheName,String key)
	{
		 cacheHelper.redisUtil.del(cacheName+"-"+key);
	}
}
