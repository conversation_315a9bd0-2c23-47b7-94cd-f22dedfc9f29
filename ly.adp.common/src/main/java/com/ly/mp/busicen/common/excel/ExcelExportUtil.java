package com.ly.mp.busicen.common.excel;

import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import com.ly.mp.busicen.common.context.BusicenException;
import com.ly.mp.busicen.common.util.ConfigGetUtil;
import com.ly.mp.busicen.common.util.ListUtil;
import com.ly.mp.busicen.common.util.LogBuilder;
import com.ly.mp.busicen.common.util.MapUtil;
import com.ly.mp.component.helper.StringHelper;


public class ExcelExportUtil {
    private static Logger logger = LoggerFactory.getLogger(ExcelExportUtil.class);

    public static <T> void exportObject(ExcelDataObject<T> data, HttpServletResponse response) throws Exception {

        setHttpheaderObject(response, data.getTitle());
        SXSSFWorkbook wb = new SXSSFWorkbook();
        try {
            String sheetName = data.getTitle();
            if (null == sheetName) {
                sheetName = "Sheet1";
            }
            //			sheetName+=LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            SXSSFSheet sheet = wb.createSheet(sheetName);

            writeExcelObject(wb, sheet, data);

            wb.write(response.getOutputStream());
        } finally {
            wb.close();
            response.getOutputStream().flush();
            response.getOutputStream().close();
        }
    }

    public static void export(ExcelData data, HttpServletResponse response) throws Exception {
        setHttpheader(response, data.getTitle());
        Workbook wb = new SXSSFWorkbook();
        try {
            String sheetName = data.getTitle();
            if (null == sheetName) {
                sheetName = "Sheet1";
            }
            logger.error("导出002" + sheetName);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            sheetName += sdf.format(new Date(System.currentTimeMillis()));
            //sheetName+=LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            logger.error("导出003" + sheetName);
            Sheet sheet = wb.createSheet(sheetName);
            logger.error("导出004" + sheet);
            writeExcel(wb, sheet, data);
            logger.error("导出005" + sheet);
            wb.write(response.getOutputStream());
            logger.error("导出006" + sheet);

        } finally {
            wb.close();
            response.getOutputStream().flush();
            response.getOutputStream().close();
        }
    }

    private static <T> void writeExcelObject(Workbook wb, SXSSFSheet sheet, ExcelDataObject<T> data) {

        int rowIndex = 1;
        rowIndex = writeTitlesToExcel(wb, sheet,
                Arrays.asList(data.getColumns()).stream().map(m -> m[1]).collect(Collectors.toList()));
        writeRowsToExcelObject(wb, sheet, data.getData(), data.getColumns(), rowIndex);
        //		autoSizeColumnsObject(sheet, data.getColumns().length + 1);

    }

    private static void writeExcel(Workbook wb, Sheet sheet, ExcelData data) {

        int rowIndex = 1;
        rowIndex = writeTitlesToExcel(wb, sheet,
                Arrays.asList(data.getColumns()).stream().map(m -> m[1]).collect(Collectors.toList()));
        writeRowsToExcel(wb, sheet, data.getData(), data.getColumns(), rowIndex);
        //autoSizeColumns(sheet, data.getColumns().length + 1);

    }

    private static int writeTitlesToExcel(Workbook wb, Sheet sheet, List<String> titles) {
        int rowIndex = 0;
        int colIndex = 0;

        Font titleFont = wb.createFont();
        titleFont.setFontName("simsun");
        titleFont.setBold(true);
        // titleFont.setFontHeightInPoints((short) 14);
        titleFont.setColor(IndexedColors.BLACK.index);

        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        titleStyle.setFont(titleFont);
        setBorder(titleStyle, BorderStyle.THIN);

        Row titleRow = sheet.createRow(rowIndex);
        // titleRow.setHeightInPoints(25);
        colIndex = 0;

        for (String field : titles) {
            Cell cell = titleRow.createCell(colIndex);
            cell.setCellValue(!StringUtils.hasText(field) ? "" : field);
            cell.setCellStyle(titleStyle);
            colIndex++;
        }

        rowIndex++;
        return rowIndex;
    }

    private static <T> int writeRowsToExcelObject(Workbook wb, SXSSFSheet sheet, List<T> data,
                                                  String[][] columns, int rowIndex) {

        Font dataFont = wb.createFont();
        dataFont.setFontName("simsun");
        // dataFont.setFontHeightInPoints((short) 14);
        dataFont.setColor(IndexedColors.BLACK.index);


        // 普通格式单元格
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.LEFT);//左对齐
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setFont(dataFont);
        setBorder(dataStyle, BorderStyle.THIN);

        // 时间格式单元格
        CellStyle dataStyleDt = wb.createCellStyle();
        dataStyleDt.setAlignment(HorizontalAlignment.LEFT);//左对齐
        dataStyleDt.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyleDt.setFont(dataFont);
        setBorder(dataStyleDt, BorderStyle.THIN);
        dataStyleDt.setDataFormat(wb.createDataFormat().getFormat("yyyy-mm-dd HH:mm:ss"));// 展示的格式
        SimpleDateFormat yyyyMMddSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter yyyyMMddDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        for (int r = 0; r < data.size(); r++) {
            Object obj = data.get(r);
            Row dataRow = sheet.createRow(r + rowIndex);
            for (int i = 0; i < columns.length; i++) {
                try {
                    Cell cell = dataRow.createCell(i);
                    cell.setCellStyle(dataStyle);
                    Field field = obj.getClass().getDeclaredField(columns[i][0]);
//					field.setAccessible(true);
                    Object objData = field.get(obj);
                    String cellData = StringHelper.IsEmptyOrNull(objData) ? "" : String.valueOf(objData);
                    cell.setCellValue(cellData);
                    cell.setCellStyle(dataStyle);
                    if (objData instanceof LocalDateTime) {
                        // 单元格设置为日期格式
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellStyle(dataStyleDt);
                        cell.setCellValue(yyyyMMddSdf.parse(yyyyMMddDtf.format((LocalDateTime) objData)));
                    } else if (objData instanceof Timestamp) {
                        cell.setCellType(CellType.NUMERIC);
                        cell.setCellStyle(dataStyleDt);
                        cell.setCellValue(yyyyMMddSdf.format((Timestamp) objData));
                    }
                    field.setAccessible(false);
                } catch (Exception e) {
                    //					e.printStackTrace();
                }
            }

        }

        return rowIndex;
    }

    private static int writeRowsToExcel(Workbook wb, Sheet sheet, List<Map<String, Object>> data,
                                        String[][] columns, int rowIndex) {

        Font dataFont = wb.createFont();
        dataFont.setFontName("simsun");
        // dataFont.setFontHeightInPoints((short) 14);
        dataFont.setColor(IndexedColors.BLACK.index);

        // 普通格式单元格
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.LEFT);//左对齐
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setFont(dataFont);
        setBorder(dataStyle, BorderStyle.THIN);

        // 时间格式单元格
        CellStyle dataStyleDt = wb.createCellStyle();
        dataStyleDt.setAlignment(HorizontalAlignment.LEFT);//左对齐
        dataStyleDt.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyleDt.setFont(dataFont);
        setBorder(dataStyleDt, BorderStyle.THIN);
        dataStyleDt.setDataFormat(wb.createDataFormat().getFormat("yyyy-mm-dd HH:mm:ss"));// 展示的格式
        SimpleDateFormat yyyyMMddSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        DateTimeFormatter yyyyMMddDtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        for (int r = 0; r < data.size(); r++) {
            Map<String, Object> obj = data.get(r);
            Row dataRow = sheet.createRow(r + rowIndex);
            logger.error("导出001" + dataRow);
            for (int i = 0; i < columns.length; i++) {
                Cell cell = dataRow.createCell(i);
                Object objData = obj.get(columns[i][0]);
                cell.setCellValue(objData == null ? "" : String.valueOf(objData));
                cell.setCellStyle(dataStyle);
                if (objData instanceof LocalDateTime) {
                    // 单元格设置为日期格式
                    cell.setCellType(CellType.NUMERIC);
                    cell.setCellStyle(dataStyleDt);
                    try {
                        cell.setCellValue(yyyyMMddSdf.parse(yyyyMMddDtf.format((LocalDateTime) objData)));
                    } catch (ParseException e) {
                    }
                } else if (objData instanceof Timestamp) {
                    // 单元格设置为日期格式
                    cell.setCellType(CellType.NUMERIC);
                    cell.setCellStyle(dataStyleDt);
                    cell.setCellValue(yyyyMMddSdf.format((Timestamp) objData));
                }
            }
        }

        return rowIndex;
    }

    private static void autoSizeColumnsObject(SXSSFSheet sheet, int columnNumber) {

		/*for (int i = 0; i < columnNumber; i++) {
			sheet.autoSizeColumn(i, true);
		}*/
        try {
            // 获取当前列的宽度，然后对比本列的长度，取最大值 （数据量过大会无效）
            for (int columnNum = 0; columnNum <= columnNumber; columnNum++) {
                int columnWidth = sheet.getColumnWidth(columnNum) / 256;
                for (int rowNum = 0; rowNum <= sheet.getLastRowNum(); rowNum++) {
                    Row currentRow;
                    // 当前行未被使用过
                    if (sheet.getRow(rowNum) == null) {
                        currentRow = sheet.createRow(rowNum);
                    } else {
                        currentRow = sheet.getRow(rowNum);
                    }
                    if (currentRow.getCell(columnNum) != null) {
                        Cell currentCell = currentRow.getCell(columnNum);
                        int length = currentCell.toString().getBytes("GBK").length;
                        if ("yyyy-mm-dd HH:mm:ss".equals(currentCell.getCellStyle().getDataFormatString())) {
                            columnWidth = 5500;
                        } else if (columnWidth < length + 1) {
                            columnWidth = length + 1;
                        }
                    }
                }
                if (columnWidth * 256 > 5500) {
                    sheet.setColumnWidth(columnNum, 5500);
                } else {
                    sheet.setColumnWidth(columnNum, columnWidth * 256);
                }
            }
        } catch (Exception e) {
        }



		/*for (int i = 0; i < columnNumber; i++) {
			// 自动列宽
			sheet.trackAllColumnsForAutoSizing();
			sheet.autoSizeColumn(i);
			if (sheet.getColumnWidth(i)>5500) {
				sheet.setColumnWidth(i, 5500);
			}
			// 手动设置
			//			int orgWidth = sheet.getColumnWidth(i);
			//			int newWidth = sheet.getColumnWidth(i)  +100;
			//			if (newWidth > orgWidth) {
			//				sheet.setColumnWidth(i, newWidth);
			//			} else {
			//				sheet.setColumnWidth(i, orgWidth);
			//			}
		}*/
    }

    private static void autoSizeColumns(Sheet sheet, int columnNumber) {

        for (int i = 0; i < columnNumber; i++) {

            //	            int orgWidth = sheet.getColumnWidth(i);
            sheet.autoSizeColumn(i, true);
            //	            int newWidth = (int) (sheet.getColumnWidth(i)  +100);
            //	            if (newWidth > orgWidth) {
            //	                sheet.setColumnWidth(i, newWidth);
            //	            } else {
            //	                sheet.setColumnWidth(i, orgWidth);
            //	            }
        }
    }

    private static void setBorder(CellStyle style, BorderStyle border) {
        style.setBorderTop(border);
        style.setBorderLeft(border);
        style.setBorderRight(border);
        style.setBorderBottom(border);
    }

    public static void setHttpheaderObject(HttpServletResponse response, String fileName) throws Exception {
        // 告诉浏览器用什么软件可以打开此文件
        response.setHeader("content-Type", "application/vnd.ms-excel");
        fileName += ".xlsx";
        // 下载文件的默认名称
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    public static void setHttpheader(HttpServletResponse response, String fileName) throws Exception {
        // 告诉浏览器用什么软件可以打开此文件
        response.setHeader("content-Type", "application/vnd.ms-excel");
        fileName += LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".xlsx";
        // 下载文件的默认名称
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }

    //生成Excel上传到服务器
    public static String outPutFtp(ExcelData excelData1, ExcelData excelData2, ExcelData excelData3, Object fileName) throws Exception {
        SXSSFWorkbook wb = new SXSSFWorkbook();
        String fileUrl = "";
        FileOutputStream fout = null;
        try {

            if (StringHelper.IsEmptyOrNull(fileName)) {
                fileName = "file";
            }
            fileName += LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            //			fileUrl+=StringHelper.GetGUID();
            //判断文件夹是否存在
            File dirFile = new File("D:/upload/38215");
            if (!dirFile.exists()) {
                //文件不存在时,应该创建文件夹
                dirFile.mkdirs();
            }
            fileUrl = "D:/upload/38215/" + fileName + ".xls";
            String sheetName1 = excelData1.getTitle();
            if (null == sheetName1) {
                sheetName1 = "Sheet1";
            }
            String sheetName2 = excelData2.getTitle();
            if (null == sheetName2) {
                sheetName2 = "Sheet2";
            }
            String sheetName3 = excelData3.getTitle();
            if (null == sheetName3) {
                sheetName3 = "Sheet3";
            }
            SXSSFSheet sheet1 = wb.createSheet(sheetName1);
            SXSSFSheet sheet2 = wb.createSheet(sheetName2);
            SXSSFSheet sheet3 = wb.createSheet(sheetName3);
            if (excelData1.getColumns() != null) {
                writeExcel(wb, sheet1, excelData1);
            }
            if (excelData2.getColumns() != null) {
                writeExcel(wb, sheet2, excelData2);
            }
            if (excelData3.getColumns() != null) {
                writeExcel(wb, sheet3, excelData3);
            }
            fout = new FileOutputStream(fileUrl);
            wb.write(fout);

        } finally {
            wb.close();
            if (fout != null) {
                fout.flush();
                fout.close();
            }
        }
        return fileUrl;

    }


    /**
     * 导出Excel 实体
     */
    private static <T> void exportExcelObject(String title, String[][] exportColumns, List<T> list, HttpServletResponse response) {
        try {
            ExcelDataObject<T> build = ExcelDataObjectBuilder.<T>create().title(title).columns(exportColumns).data(list).build();
            ExcelExportUtil.exportObject(build, response);
        } catch (Exception e) {
            try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw);) {
                e.printStackTrace(pw);
                logger.error(LogBuilder.getErrParams(LogBuilder.Module.SYS, "exportExcelObject", sw.toString()));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            throw BusicenException.create(e.getMessage());
        }
    }

    /**
     * 导出Excel Map
     */
    private static void exportExcelMap(String title, String[][] exportColumns, List<Map<String, Object>> list, HttpServletResponse response) {
        try {
            ExcelData build = ExcelDataBuilder.create().title(title).columns(exportColumns).data(list).build();
            ExcelExportUtil.export(build, response);
        } catch (Exception e) {
            try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw);) {
                e.printStackTrace(pw);
                logger.error(LogBuilder.getErrParams(LogBuilder.Module.SYS, "exportExcelMap", sw.toString()));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            throw BusicenException.create(e.getMessage());
        }
    }

    /**
     * 导出Excel文件
     */
    @SuppressWarnings("unchecked")
    public static <T, Column> void exportExcel(String title, Column columns, List<T> list, HttpServletResponse response) {
        try {
            if (columns == null || response == null) {
                throw BusicenException.create("参数不能为空");
            }
            // 表头转换
            String[][] exportColumns = null;
            if (columns instanceof Map) {
                List<Map<String, String>> exportColumnsList = new ArrayList<>();
                ((Map<?, ?>) columns).forEach((k, v) -> {
                    exportColumnsList.add(MapUtil.<String, String>builder(String.valueOf(k), String.valueOf(v)).build());
                });
                exportColumns = ListUtil.listMapToStringArray(exportColumnsList);
            } else if (columns instanceof String[][]) {
                exportColumns = (String[][]) columns;
            } else if (columns instanceof List) {
                exportColumns = ListUtil.listMapToStringArray((List<Map<String, String>>) columns);
            } else if (columns instanceof Class) {
                exportColumns = MapUtil.getExcelHeader((Class<Column>) columns);
            } else {
                throw BusicenException.create("表头不正确");
            }
            // 空数据返回只有表头的文件
            if (StringHelper.IsEmptyOrNull(list)) {
                exportExcelMap(title, exportColumns, new ArrayList<>(), response);
                return;
            }
            // 根据泛型类型调用不同的导出方法
            if (list.get(0) instanceof Map) {
                exportExcelMap(title, exportColumns, (List<Map<String, Object>>) list, response);
                return;
            }
            exportExcelObject(title, exportColumns, list, response);
            return;
        } catch (Exception e) {
            try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw);) {
                e.printStackTrace(pw);
                logger.error(LogBuilder.getErrParams(LogBuilder.Module.SYS, "exportExcel", sw.toString()));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            throw BusicenException.create(e.getMessage());
        }
    }

    /**
     * 导出Excel文件
     */
    @SuppressWarnings("unchecked")
    public static <T, Column> void exportExcel(String title, Column columns, List<T> list, String filePath) {
        try {
            if (columns == null || StringHelper.IsEmptyOrNull(filePath)) {
                throw BusicenException.create("参数不能为空");
            }
            // 表头转换
            String[][] exportColumns = null;
            if (columns instanceof Map) {
                List<Map<String, String>> exportColumnsList = new ArrayList<>();
                ((Map<?, ?>) columns).forEach((k, v) -> {
                    exportColumnsList.add(MapUtil.<String, String>builder(String.valueOf(k), String.valueOf(v)).build());
                });
                exportColumns = ListUtil.listMapToStringArray(exportColumnsList);
            } else if (columns instanceof String[][]) {
                exportColumns = (String[][]) columns;
            } else if (columns instanceof List) {
                exportColumns = ListUtil.listMapToStringArray((List<Map<String, String>>) columns);
            } else if (columns instanceof Class) {
                exportColumns = MapUtil.getExcelHeader((Class<Column>) columns);
            } else {
                throw BusicenException.create("表头不正确");
            }
            // 空数据返回只有表头的文件
            if (StringHelper.IsEmptyOrNull(list)) {
                exportExcelMap(title, exportColumns, new ArrayList<>(), filePath);
                return;
            }
            // 根据泛型类型调用不同的导出方法
            if (list.get(0) instanceof Map) {
                exportExcelMap(title, exportColumns, (List<Map<String, Object>>) list, filePath);
                return;
            }
            exportExcelObject(title, exportColumns, list, filePath);
            return;
        } catch (Exception e) {
            try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw);) {
                e.printStackTrace(pw);
                logger.error(LogBuilder.getErrParams(LogBuilder.Module.SYS, "exportExcel", sw.toString()));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            throw BusicenException.create(e.getMessage());
        }
    }

    /**
     * 导出Excel Map
     */
    private static void exportExcelMap(String title, String[][] exportColumns, List<Map<String, Object>> list, String filePath) {
        try {
            ExcelData build = ExcelDataBuilder.create().title(title).columns(exportColumns).data(list).build();
            ExcelExportUtil.export(build, filePath);
        } catch (Exception e) {
            try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw);) {
                e.printStackTrace(pw);
                logger.error(LogBuilder.getErrParams(LogBuilder.Module.SYS, "exportExcelMap", sw.toString()));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            throw BusicenException.create(e.getMessage());
        }
    }

    /**
     * 导出Excel 实体
     */
    private static <T> void exportExcelObject(String title, String[][] exportColumns, List<T> list, String filePath) {
        try {
            ExcelDataObject<T> build = ExcelDataObjectBuilder.<T>create().title(title).columns(exportColumns).data(list).build();
            ExcelExportUtil.exportObject(build, filePath);
        } catch (Exception e) {
            try (StringWriter sw = new StringWriter(); PrintWriter pw = new PrintWriter(sw);) {
                e.printStackTrace(pw);
                logger.error(LogBuilder.getErrParams(LogBuilder.Module.SYS, "exportExcelObject", sw.toString()));
            } catch (Exception e1) {
                e1.printStackTrace();
            }
            throw BusicenException.create(e.getMessage());
        }
    }

    public static <T> void exportObject(ExcelDataObject<T> data, String filePath) throws Exception {

        SXSSFWorkbook wb = new SXSSFWorkbook();
        FileOutputStream fout = null;
        try {
            String sheetName = data.getTitle();
            if (null == sheetName) {
                sheetName = "Sheet1";
            }
            //			sheetName+=LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            SXSSFSheet sheet = wb.createSheet(sheetName);

            writeExcelObject(wb, sheet, data);
            fout = new FileOutputStream(filePath);
            wb.write(fout);
        } finally {
            wb.close();
            if (fout != null) {
                fout.flush();
                fout.close();
            }
        }
    }

    public static void export(ExcelData data, String filePath) throws Exception {
        Workbook wb = new SXSSFWorkbook();
        FileOutputStream fout = null;
        try {
            String sheetName = data.getTitle();
            if (null == sheetName) {
                sheetName = "Sheet1";
            }
            logger.error("导出002" + sheetName);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            sheetName += sdf.format(new Date(System.currentTimeMillis()));
            //sheetName+=LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            logger.error("导出003" + sheetName);
            Sheet sheet = wb.createSheet(sheetName);
            logger.error("导出004" + sheet);
            writeExcel(wb, sheet, data);
            logger.error("导出005" + sheet);
            fout = new FileOutputStream(filePath);
            wb.write(fout);
            logger.error("导出006" + sheet);

        } finally {
            wb.close();
            if (fout != null) {
                fout.flush();
                fout.close();
            }
        }
    }

    private static String uploadDir = "/upload";//文件上传到挂载盘的根目录
    private static String fileExportModule = "fileexport";

    private static String getExportMnt() {
        String mnt = ConfigGetUtil.getInstance().getExportMntDir();//挂载盘相对于java运行目录的地址
        if (StringHelper.IsEmptyOrNull(mnt)) {
            mnt = "springboot/project";
        }
        return mnt;
    }

    /**
     * 获取报表离线导出文件绝对路径
     *
     * @param fileName 导出的文件名称，如果不加.xls或者xlsx，会自动加.xlsx
     * @param module   模块，可空，传null会自动取默认值
     * @return
     */
    public static String getExportAbsolutePath(String fileName, String module) {
        //if(StringHelper.IsEmptyOrNull(module)) {
        module = fileExportModule;//指定目录，后续清理文件方便找目录
        //}
        String fileDir = getExportMnt() + uploadDir;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("HHmmss");
        Date date = new Date(System.currentTimeMillis());
        //格式：[java运行目录]/springboot/project/upload/[模块名]/[日期]/[时间]/[文件名]
        String absolutePath = System.getProperty("user.dir") + File.separator
                + fileDir + File.separator + module + File.separator
                + sdf.format(date) + File.separator
                + sdf2.format(date);
        //判断文件夹是否存在
        File dirFile = new File(absolutePath);
        if (!dirFile.exists()) {
            //不存在时,应该创建文件夹
            dirFile.mkdirs();
        }
        if (!fileName.endsWith(".xls") && !fileName.endsWith(".xlsx")) {
            fileName = fileName + ".xlsx";
        }
        absolutePath = absolutePath + File.separator + fileName;
        return absolutePath;
    }

    /**
     * 通过绝对路径计算出前端下载相对路径
     *
     * @param absolutePath
     * @return
     */
    public static String getExportRelationPath(String absolutePath) {
        if (StringHelper.IsEmptyOrNull(absolutePath)) {
            return "";
        }
        return absolutePath.substring(absolutePath.indexOf(uploadDir)).replace('\\', '/');
    }

    //获取绝对路径： [java运行目录]/springboot/project+相对路径
    public static String getAbsolutePathByRelationPath(String relationPath) {
        String absolutePath = System.getProperty("user.dir") + File.separator
                + getExportMnt() + relationPath;
        return absolutePath;
    }

}
