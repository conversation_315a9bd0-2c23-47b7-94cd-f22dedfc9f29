package com.ly.mp.busicen.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.ly.mp.busicen.common.controller.DbSwitch;
import com.ly.mp.busicen.common.dal.mybatis.BusicenMybatisPlusConfig;
import com.ly.mp.busicen.common.dal.mybatis.BusicenMybatisPlusConfig.MapWrapperFactory;
import com.ly.mp.busicen.common.helper.SpringContextHolder;

@Configuration
public class BusicenCommonConfig {
	
	@Bean
	public PaginationInterceptor paginationInterceptor() {
		PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
		return paginationInterceptor;
	}
	
	@Bean
    public ConfigurationCustomizer configurationCustomizer123() {
        return configuration -> configuration.setObjectWrapperFactory(new MapWrapperFactory());
    }
	
	
	@Bean
	public BusicenMybatisPlusConfig busicenMybatisPlusConfig() {
		return new BusicenMybatisPlusConfig();
	}
	

	
	@Bean
	public DbSwitch dbSwitch() {
		return new DbSwitch();
	}
	
	@Bean
	public SpringContextHolder SpringContextHolder() {
		return new SpringContextHolder();
	}
}
