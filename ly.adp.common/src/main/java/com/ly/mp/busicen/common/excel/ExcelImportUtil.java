package com.ly.mp.busicen.common.excel;

import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import com.ly.mp.busicen.common.context.BusicenException;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017年6月5日
 */
public class ExcelImportUtil {

    /**
     * 无穷大
     */
    private static final Long max = Long.MAX_VALUE;
    /**
     * 无穷小
     */
    private static final Long min = Long.MIN_VALUE;

    public static List<Map<String, String>> parseExcel(ParseTemplate parseTemplate, File file) throws Exception {
        return parseExcel(parseTemplate, new FileInputStream(file));
    }

    public static List<Map<String, String>> parseExcel(InputStream inputStream) throws Exception {
        return parseExcel(new ParseTemplate(), inputStream);
    }

    public static List<LinkedHashMap<String, String>> parseExcels(InputStream inputStream) throws Exception {
        return parseExcels(new ParseTemplate(), inputStream);
    }

    public static List<List<Map<String, String>>> parseExcelMulti(InputStream inputStream) throws Exception {
        return parseExcelMulti(new ParseTemplate(), inputStream);
    }

    public static List<List<Map<String, String>>> parseExcelMulti(ParseTemplate parseTemplate, InputStream inputStream) throws Exception {
        Workbook book = null;
        try {
            List<List<Map<String, String>>> result = new ArrayList<List<Map<String, String>>>();
            book = WorkbookFactory.create(inputStream);
            int k = 0;
            Sheet sheet = null;
            while (true) {
                try {
                    sheet = book.getSheetAt(k);
                    k++;
                } catch (Exception e) {
                    break;
                }

                Integer dataTitleInt;
                if ((dataTitleInt = parseTemplate.getDataTitleRow()) == null) {
                    dataTitleInt = 0;
                }
                Row dataTitleRow = sheet.getRow(dataTitleInt);
                int titleColNum = 0;
                List<String> dataTitleList = new ArrayList<String>();
                while (true) {
                    Cell cell = dataTitleRow.getCell(titleColNum);
                    if (cell == null) {
                        break;
                    }
                    // cell.setCellType(CellType.STRING);
                    String cellStr = cell.getStringCellValue();
                    dataTitleList.add(cellStr);
                    titleColNum++;
                }
                int dataBegRow = 0;
                if (parseTemplate.getDataBegRow() == null) {
                    dataBegRow = 1;
                } else {
                    dataBegRow = parseTemplate.getDataBegRow();
                }

                List<Map<String, String>> dataMapList = new ArrayList<>();
                // int firstRow = sheet.getFirstRowNum();
                int lastRow = sheet.getLastRowNum();
                // 去除double的科学计数法
                NumberFormat nf = NumberFormat.getInstance();
                nf.setGroupingUsed(false);
                // 百分比保留两位小数
                NumberFormat nf2 = NumberFormat.getPercentInstance();
                nf2.setGroupingUsed(false);
                nf2.setMaximumFractionDigits(2);
                for (int rowIndex = dataBegRow; rowIndex <= lastRow; rowIndex++) {
                    Row dataRow = sheet.getRow(rowIndex);
                    if (isEmptyRow(dataRow)) {
                        throw BusicenException.create("有空数据行，请检查文件");
                    }
                    // dataBegRow++;
                    Map<String, String> dataMap = new HashMap<String, String>();
                    for (int i = 0; i < dataTitleList.size(); i++) {
                        Cell cell = dataRow.getCell(i);
                        String key = dataTitleList.get(i);
                        if (cell == null) {
                            dataMap.put(key, "");
                        } else {
                            CellType cellTypeEnum = cell.getCellTypeEnum();
                            switch (cellTypeEnum) {
                                case STRING: // 字符串
                                    dataMap.put(key, cell.getStringCellValue() != null ? cell.getStringCellValue().trim() : "");
                                    break;
                                case NUMERIC:
                                    String name = cell.toString();
                                    try {
                                        if ("0.00%".equals(cell.getCellStyle().getDataFormatString()) || "0%".equals(cell.getCellStyle().getDataFormatString())) { // 百分比
                                            name = nf2.format(cell.getNumericCellValue());
                                        } else {
                                            try {
                                                // 数字格式
                                                name = nf.format(cell.getNumericCellValue());
                                            } catch (Exception e) {
                                            }
                                            try {
                                                // 日期格式
                                                Date dateCellValue = cell.getDateCellValue();
                                                new SimpleDateFormat(cell.getCellStyle().getDataFormatString()).format(dateCellValue);
                                                name = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dateCellValue);
                                            } catch (Exception e) {
                                            }
                                        }
                                    } catch (Exception e) {
                                    } finally {
                                        dataMap.put(key, name);
                                    }
                                    break;
                                case BLANK: // 空值
                                    dataMap.put(key, "");
                                    break;
                                case BOOLEAN: // 布尔
                                    dataMap.put(key, String.valueOf(cell.getBooleanCellValue()));
                                    break;
                                case ERROR: // 错误
                                default:
                                    dataMap.put(key, cell.toString());
                                    break;
                            }
                        }

                    }
                    dataMapList.add(dataMap);
                }

                result.add(dataMapList);
            }
            return result;
        } finally {
            if (book != null) {
                book.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    public static List<Map<String, String>> parseExcel(ParseTemplate parseTemplate, InputStream inputStream) throws Exception {
        Workbook book = null;
        try {
            book = WorkbookFactory.create(inputStream);
            Sheet sheet = book.getSheetAt(0);
            Integer dataTitleInt;
            if ((dataTitleInt = parseTemplate.getDataTitleRow()) == null) {
                dataTitleInt = 0;
            }
            Row dataTitleRow = sheet.getRow(dataTitleInt);
            int titleColNum = 0;
            List<String> dataTitleList = new ArrayList<String>();
            while (true) {
                Cell cell = dataTitleRow.getCell(titleColNum);
                if (cell == null) {
                    break;
                }
                // cell.setCellType(CellType.STRING);
                String cellStr = cell.getStringCellValue();
                dataTitleList.add(cellStr);
                titleColNum++;
            }
            int dataBegRow = 0;
            if (parseTemplate.getDataBegRow() == null) {
                dataBegRow = 1;
            } else {
                dataBegRow = parseTemplate.getDataBegRow();
            }

            List<Map<String, String>> dataMapList = new ArrayList<>();
            // int firstRow = sheet.getFirstRowNum();
            int lastRow = sheet.getLastRowNum();
            // System.out.println(lastRow);
            int blankrowbank = 0;
            for (int rowIndex = dataBegRow; rowIndex <= lastRow; rowIndex++) {
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow == null) {
                    throw new RuntimeException("BUSICENIMPORTEMPTYROW");
                }
                if (isEmptyRow(dataRow)) {
                    blankrowbank = 2;
                    continue;
                } else {
                    blankrowbank = blankrowbank == 2 ? 1 : 0;
                    if (blankrowbank == 1) {
                        throw new RuntimeException("BUSICENIMPORTEMPTYROW");
                    }
                }
                // dataBegRow++;
                Map<String, String> dataMap = new HashMap<String, String>();
                for (int i = 0; i < dataTitleList.size(); i++) {
                    Cell cell = dataRow.getCell(i);
                    if (cell == null) {
                        dataMap.put(dataTitleList.get(i), "");
                    } else {
                        cell.setCellType(CellType.STRING);
                        dataMap.put(dataTitleList.get(i), cell.getStringCellValue() != null ? cell.getStringCellValue().trim() : "");
                    }

                }
                dataMapList.add(dataMap);
            }
            return dataMapList;
        } finally {
            if (book != null) {
                book.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    public static boolean isEmptyRow(Row row) {
        if (row == null || row.toString().isEmpty()) {
            return true;
        } else {
            Iterator<Cell> it = row.iterator();
            boolean isEmpty = true;
            while (it.hasNext()) {
                Cell cell = it.next();
                if (cell.getCellTypeEnum() != CellType.BLANK) {
                    isEmpty = false;
                    break;
                }
            }

            return isEmpty;
        }
    }

    public static List<LinkedHashMap<String, String>> parseExcels(ParseTemplate parseTemplate, InputStream inputStream) throws Exception {
        Workbook book = null;
        try {
            book = WorkbookFactory.create(inputStream);
            Sheet sheet = book.getSheetAt(0);
            Integer dataTitleInt;
            if ((dataTitleInt = parseTemplate.getDataTitleRow()) == null) {
                dataTitleInt = 0;
            }
            Row dataTitleRow = sheet.getRow(dataTitleInt);
            int titleColNum = 0;
            List<String> dataTitleList = new ArrayList<String>();
            while (true) {
                Cell cell = dataTitleRow.getCell(titleColNum);
                if (cell == null) {
                    break;
                }
                String cellStr = "";
                cell.setCellType(CellType.STRING);
                cellStr = cell.getStringCellValue();
                if (dataTitleList.contains(cellStr)) {
                    throw new RuntimeException("列名[" + cellStr + "]重复");
                }
                dataTitleList.add(cellStr);
                titleColNum++;
            }
            Integer dataBegRow;
            if ((dataBegRow = parseTemplate.getDataBegRow()) == null) {
                dataBegRow = 1;
            }
            List<LinkedHashMap<String, String>> dataMapList = new ArrayList<>();
            while (true) {
                Row dataRow = sheet.getRow(dataBegRow);
                if (dataRow == null) {
                    break;
                }
                dataBegRow++;
                LinkedHashMap<String, String> dataMap = new LinkedHashMap<String, String>();
                for (int i = 0; i < dataTitleList.size(); i++) {
                    Cell cell = dataRow.getCell(i);
                    if (cell == null) {
                        dataMap.put(dataTitleList.get(i), "");
                        continue;
                    }
                    if (cell.toString().equals("∞")) {
                        cell.setCellValue(max);
                    } else if (cell.toString().equals("－∞")) {
                        cell.setCellValue(min);
                    }
                    if (cell.getCellTypeEnum() == CellType.NUMERIC) {
                        if (HSSFDateUtil.isCellDateFormatted(cell)) {
                            SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
                            dataMap.put(dataTitleList.get(i), String.valueOf(format.format(cell.getDateCellValue())));
                        } else {
                            cell.setCellType(CellType.STRING);
                            dataMap.put(dataTitleList.get(i), String.valueOf(cell.getStringCellValue()).toString());
                        }
                    } else {
                        cell.setCellType(CellType.STRING);
                        dataMap.put(dataTitleList.get(i), cell.getStringCellValue().toString());
                    }
                }
                dataMapList.add(dataMap);
            }
            return dataMapList;
        } catch (Exception e) {
            throw new RuntimeException("不能包含图片..请检查Excel文件后导入");
        } finally {
            if (book != null) {
                book.close();
            }
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
}
