package com.ly.mp.busicen.common.service;
/**
 * 自定义Service异常
 * 继承RuntimeException，支持spring事务默认异常回滚
 * <AUTHOR>
 * 日期：2019-07-04
 */
public class ServiceException extends Exception{
	
	private static final long serialVersionUID = 1L;
	private String result;
	private String massage;
	
	public ServiceException(String result, String massage) {
		super(massage);
		this.result = result;
		this.massage = massage;
	}

	public String getResult() {
		return result;
	}

	public void setResult(String result) {
		this.result = result;
	}

	public String getMassage() {
		return massage;
	}

	public void setMassage(String massage) {
		this.massage = massage;
	}
}
