package com.ly.mp.busicen.common.excel;

import java.util.List;

/**
 *
 * <AUTHOR>
 *
 */
public class ExcelDataObjectBuilder<T> {

	public ExcelDataObject<T> parent = new ExcelDataObject<T>();

	public static <T> ExcelDataObjectBuilder<T> create() {
		return new ExcelDataObjectBuilder<T>();
	}

	public ExcelDataObjectBuilder<T> title(String title) {
		this.parent.setTitle(title);
		return this;
	}

	public ExcelDataObjectBuilder<T> columns(String[][] columns) {
		this.parent.setColumns(columns);
		return this;
	}

	public ExcelDataObjectBuilder<T> data(List<T> data) {
		this.parent.setData(data);
		return this;
	}

	public ExcelDataObject<T> build() {
		return parent;
	}

}
