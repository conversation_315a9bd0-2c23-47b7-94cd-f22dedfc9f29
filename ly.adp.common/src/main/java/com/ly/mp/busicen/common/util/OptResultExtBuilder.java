package com.ly.mp.busicen.common.util;

import com.ly.mp.busicen.common.entity.OptResultExt;

public class OptResultExtBuilder {
	private OptResultExt instance;
	
	public static OptResultExtBuilder create() {
		OptResultExtBuilder builder = new OptResultExtBuilder();
		builder.instance=new OptResultExt();			
		return builder;
	}
	
	public static OptResultExtBuilder createOk() {
		OptResultExtBuilder builder =create();
		builder.instance.setMsg("操作成功");
		builder.instance.setResult("1");
		return builder;
	}
	
	public static OptResultExtBuilder createFail() {
		return createFail("操作失败");
	}
	
	public static OptResultExtBuilder createFail(String msg) {
		OptResultExtBuilder builder =create();
		builder.instance.setMsg(msg);
		builder.instance.setResult("0");
		return builder;
	}
	
	public OptResultExtBuilder setResult(String result) {
		this.instance.setResult(result);
		return this;
	}
	
	public OptResultExtBuilder result(String result) {
		this.instance.setResult(result);
		return this;
	}
	
	public OptResultExtBuilder setMsg(String msg) {
		this.instance.setMsg(msg);
		return this;
	}
	
	public OptResultExtBuilder Msg(String msg) {
		this.instance.setMsg(msg);
		return this;
	}
	
	public OptResultExt build() {
		return instance;
	}
	
}
