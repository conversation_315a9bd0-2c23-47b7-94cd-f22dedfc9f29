/**   
* @Description: 该函数的功能描述
*
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年8月23日 下午2:13:48 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年8月23日     ly-wyifan           v1.0.0               修改原因
*/
package com.ly.mp.busicen.common.util;


import java.util.List;

import com.ly.mp.busicen.common.entity.ListResultExt;



/**   
* @Description: 该函数的功能描述
*
* @version: v1.0.0
* @author: ly-wyifan
* @date: 2019年8月23日 下午2:13:48 
*
* Modification History:
* Date         Author          Version            Description
*---------------------------------------------------------*
* 2019年8月23日     ly-wyifan           v1.0.0               修改原因
*/
public class ListResultExtBuilder {
	private ListResultExt<?> parent = new ListResultExt<>();

	public static ListResultExtBuilder create() {		
		return new ListResultExtBuilder();
	}
	
	public static ListResultExtBuilder creatOk() {
		ListResultExtBuilder result = create();
		result.parent.setMsg("查询成功");
		result.parent.setResult("1");
		return result;
	}

	@SuppressWarnings("unchecked")
	public <T> ListResultExt<T> build() {
		return (ListResultExt<T>) parent;
	}

	public ListResultExtBuilder result(String result) {
		parent.setResult(result);
		return this;
	}

	public ListResultExtBuilder pageindex(Long pageindex) {
		parent.setPageindex(pageindex.intValue());
		return this;
	}

	public ListResultExtBuilder pages(Long pages) {
		parent.setPages(pages.intValue());
		return this;
	}

	public ListResultExtBuilder records(Long records) {
		parent.setRecords(records.intValue());
		return this;
	}

	public ListResultExtBuilder msg(String msg) {
		parent.setMsg(msg);
		return this;
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	public ListResultExtBuilder rows(List rows) {
		parent.setRows(rows);
		return this;
	}
	

}
