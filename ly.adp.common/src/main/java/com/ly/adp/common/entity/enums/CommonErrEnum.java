package com.ly.adp.common.entity.enums;

import com.ly.mp.busicen.common.context.BusicenException;

/**
 * api 异常枚举
 * <AUTHOR>
 * @Date 2024/7/4
 * @Version 1.0.0
 **/
public enum CommonErrEnum {
    BIZ_ERR("1600050001", "业务异常，异常信息自定义");

    private final String code;
    private final String errMsg;

    CommonErrEnum(String code, String errMsg) {
        this.code = code;
        this.errMsg = errMsg;
    }

    public String getCode() {
        return code;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public static CommonErrEnum fromCode(String code) {
        for (CommonErrEnum errEnum : CommonErrEnum.values()) {
            if (errEnum.getCode().equals(code)) {
                return errEnum;
            }
        }
        throw new IllegalArgumentException("Invalid CommonErrEnum code: " + code);
    }

    public static BusicenException createBizException(String errMsg) {
        return new BusicenException(errMsg);
    }

    @Override
    public String toString() {
        return this.errMsg;
    }
}
