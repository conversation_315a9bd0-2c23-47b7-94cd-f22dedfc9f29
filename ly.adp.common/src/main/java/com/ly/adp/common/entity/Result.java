package com.ly.adp.common.entity;

import com.ly.adp.common.entity.enums.CommonErrEnum;

/**
 * xpai 结果实体
 * <AUTHOR>
 * @Version 1.0.0
 **/
public class Result {
    /**
     * 执行结果
     */
    private boolean success;

    /**
     * 执行结果编码
     */
    private String code;

    /**
     * 执行结果内容
     */
    private String msg;

    public Result() {
    }

    public Result(boolean success, String code, String msg) {
        this.success = success;
        this.code = code;
        this.msg = msg;
    }

    private Result(Builder builder) {
        this.success = builder.success;
        this.code = builder.code;
        this.msg = builder.msg;
    }

    public boolean isSuccess() {
        return success;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public static class Builder {
        private boolean success;
        private String code;
        private String msg;

        public Builder success(boolean success) {
            this.success = success;
            return this;
        }

        public Builder code(String code) {
            this.code = code;
            return this;
        }

        public Builder msg(String msg) {
            this.msg = msg;
            return this;
        }

        public Builder msg(String format, Object... args) {
            this.msg = String.format(format, args);
            return this;
        }

        public Result build() {
            return new Result(this);
        }

        public static Result success() {
            return new Builder()
                    .success(true)
                    .code("1")
                    .msg("操作成功")
                    .build();
        }

        public static Result success(String msg, Object... args) {
            return new Builder()
                    .success(true)
                    .code("1")
                    .msg(msg, args)
                    .build();
        }

        public static Result failure() {
            return new Builder()
                    .success(false)
                    .code(CommonErrEnum.BIZ_ERR.getCode())
                    .msg("操作失败")
                    .build();
        }

        public static Result failure(String msg, Object... args) {
            return new Builder()
                    .success(false)
                    .code(CommonErrEnum.BIZ_ERR.getCode())
                    .msg(msg, args)
                    .build();
        }
    }
}
