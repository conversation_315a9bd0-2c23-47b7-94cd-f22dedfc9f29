package com.ly.adp.common.cloud;

import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.client.RestTemplate;

@Configuration
@Import({FeignInterceptorEx.class})
public class CloudInitializerEx {
    public CloudInitializerEx() {
    }

    @LoadBalanced
    @Bean
    RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        return restTemplate;
    }

    @Bean
    public SeataRequestInterceptor seataRequestInterceptor() {
        return new SeataRequestInterceptor();
    }

    @Bean
    public FeignInterceptorEx feignInterceptorEx() {
        return new FeignInterceptorEx();
    }
}
