package com.ly.adp.common.entity;

public class ParamPage<T> extends ParamBase<T> {

    int pageIndex = -1;

    int pageSize = -1;

    public Integer getPageIndex() {
        return pageIndex;
    }

    public void setPageIndex(Integer pageIndex) {
        if(pageIndex<0 &&pageIndex!=-1)
        {
            this.pageIndex=1;
        }
        else
        {
            this.pageIndex = pageIndex;
        }
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        if(pageSize<0 &&pageSize!=-1)
        {
            this.pageSize=10;
        }
        else
        {
            this.pageSize = pageSize;
        }
    }
}
