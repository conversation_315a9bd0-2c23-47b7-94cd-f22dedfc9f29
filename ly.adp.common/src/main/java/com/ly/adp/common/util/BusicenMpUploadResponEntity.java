package com.ly.adp.common.util;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BusicenMpUploadResponEntity implements Serializable {
    private static final long serialVersionUID = -3275060008229943719L;
    private String result;
    private String msg;
    private List<List<String>> path;

    public BusicenMpUploadResponEntity() {
        this.result = "1";
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<List<String>> getPath() {
        return path;
    }

    public void setPath(List<List<String>> path) {
        this.path = path;
    }


}
