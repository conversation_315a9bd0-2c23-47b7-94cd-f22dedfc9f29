package com.ly.adp.common.util;

import java.util.Map;

import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import com.ly.mp.component.entities.UserEntity;
import com.ly.mp.component.helper.JsonUtils;
import com.ly.mp.component.helper.SessionHelper;
import com.ly.mp.component.helper.StringHelper;

public class BusicenMpUtil {
    /**
     * 调用MP的文件上传服务
     *
     * @param serverHost  服务的域名地址
     * @param userName    登陆用户名
     * @param pwd         登陆用户密码
     * @param randomCode  随机码
     * @param filesToByte 上传文件的字节流
     * @param fileName    文件名，例如：text.txt
     * @return
     */
    public static BusicenMpUploadResponEntity uploadFile(String serverHost, byte[] filesToByte, String fileName, String token) {
        try {
            if (StringHelper.IsEmptyOrNull(token)) {
                token = StringHelper.GetGUID();

                UserEntity userEntity = new UserEntity();
                userEntity.setUserID("1");
                userEntity.setUserName("xtadmin");

                SessionHelper.put(token, userEntity);
            }


            //请求地址
            String url = serverHost + "/mp/file/busicen/upload.do";

            //HttpClient context = HttpClients.createDefault();
            HttpPost post = new HttpPost(url);
            //全部设置为30分钟
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(1000 * 60 * 60)
                    .setConnectTimeout(1000 * 60 * 60)
                    .setConnectionRequestTimeout(1000 * 60 * 60)
                    .build();
            HttpClient context = HttpClients.custom()
                    .setDefaultRequestConfig(requestConfig)
                    .build();

            post.setHeader("Authorization", token);
            post.setHeader("Accept-Language", "zh-CN,zh;q=0.9");

            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            builder.addBinaryBody("uploadfile", filesToByte, ContentType.MULTIPART_FORM_DATA, fileName);//添加文件

            post.setEntity(builder.build());
            HttpResponse response = context.execute(post);
            String res = "";
            /**请求发送成功，并得到响应**/
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                res = EntityUtils.toString(response.getEntity());
                JsonUtils jut = new JsonUtils();
                return jut.fromJson(res, BusicenMpUploadResponEntity.class);

            } else {
                BusicenMpUploadResponEntity entity = new BusicenMpUploadResponEntity();
                entity.setResult("0");
                entity.setMsg(response.toString());
                return entity;
            }
        } catch (Exception e) {
            BusicenMpUploadResponEntity entity = new BusicenMpUploadResponEntity();
            entity.setResult("0");
            entity.setMsg(e.toString());
            return entity;
        }
    }

}
