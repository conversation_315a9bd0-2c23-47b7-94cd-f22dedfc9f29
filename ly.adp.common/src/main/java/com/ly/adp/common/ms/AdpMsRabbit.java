package com.ly.adp.common.ms;

import com.ly.bucn.component.ms.*;
import com.rabbitmq.client.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;

import java.io.IOException;
import java.util.UUID;

public class AdpMsRabbit implements MsAdapter {

    private static Logger logger = LoggerFactory.getLogger(AdpMsRabbit.class);


    @Value("${adp.rabbitmq.direct.exchange:smart.ex.adp.public}")
    private String exchangeName;

    @Autowired
    RabbitMQPool rabbitMQPool;

    public AdpMsRabbit() {

    }

    @Async
    public void sendMessage(String queueName, String msg) {
        rabbitMQPool.publishTemplate(channel -> {
            AMQP.BasicProperties rabbitProperties = new AMQP.BasicProperties().builder()
                    .messageId(UUID.randomUUID().toString())
                    .contentEncoding("UTF-8")
                    .deliveryMode(2)
                    .contentType("json").build();

            //long start = System.currentTimeMillis();
            //fanout模式
            channel.exchangeDeclare(queueName, BuiltinExchangeType.FANOUT, true);
            channel.basicPublish(queueName, "", rabbitProperties, msg.getBytes("UTF-8"));
//            channel.exchangeDeclare(exchangeName, BuiltinExchangeType.DIRECT, true);
//            channel.queueDeclare(queueName, true, false, false, null);
//            channel.queueBind(queueName, exchangeName, queueName);
//            channel.basicPublish(exchangeName, queueName, rabbitProperties, msg.getBytes("UTF-8"));
//            long end = System.currentTimeMillis();
//            logger.debug("队列【" + queueName + "】消息发送成功：" + msg + "---耗时：" + (end - start) + "ms");

        });
    }


    @Override
    public MsProducers publisher() {
        return (topic, message) -> {
            sendMessage(topic, message);
        };
    }

    @Override
    public void init(MsContext msContext) {
        msContext.consumers().forEach((t, c) -> {
            try {
                rabbitMQPool.consumerTemplate(channel -> {
                    channel.exchangeDeclare(exchangeName, BuiltinExchangeType.DIRECT, true);
                    channel.queueDeclare(t, true, false, false, null);
                    channel.queueBind(t, exchangeName, t);
                    Consumer consumer = new DefaultConsumer(channel) {
                        @Override
                        public void handleDelivery(String consumerTag, Envelope envelope, AMQP.BasicProperties properties, byte[] body) throws IOException {
                            c.receive(t, new String(body, "UTF-8"));
                        }
                    };
                    channel.basicConsume(t, true, consumer);
                });
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }

    @Override
    public void destroy(MsContext msContext) {

    }

    @Override
    public String name() {
        return "adp.rabbit";
    }
}
