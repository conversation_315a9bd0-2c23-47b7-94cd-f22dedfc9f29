package com.ly.adp.common.entity.enums;

/**
 * 数据源枚举
 * <AUTHOR>
 * @Date 2024/7/5
 * @Version 1.0.0
 **/
public enum DataSourceEnum {

    mp_write("mp_write", "interfacecenter"),
    tidb("tidb", "busicen_1"),
    base("base", "mp");

    private String jdbcName;

    private String db;

    DataSourceEnum(String jdbcName, String db) {
        this.jdbcName = jdbcName;
        this.db = db;
    }

    public String getJdbcName() {
        return jdbcName;
    }

    public String getDb() {
        return db;
    }

    @Override
    public String toString() {
        return this.db;
    }
}
