package com.ly.adp.common.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ly.bucn.component.message.Message;
import com.ly.bucn.component.valid.field.ValidsContextSimple;
import com.ly.bucn.component.valid.field.ValidsResult;
import com.ly.mp.component.entities.EntityResult;
import com.ly.mp.component.entities.ListResult;
import com.ly.mp.component.entities.OptResult;
import com.ly.mp.component.helper.SpringContextHolder;

import java.util.List;
import java.util.function.Supplier;

public class BucnUtil {

    public static <T> ListResult<T> page2ListResult(IPage<T> page, List<T> list) {
        ListResult<T> result = new ListResult<>();
        result.setRows(list);
        result.setRecords((int) page.getTotal());
        result.setPages((int) page.getPages());
        result.setPageindex((int) page.getCurrent());
        result.setResult("1");
        result.setMsg("查询成功");
        return result;
    }

    public static void throwMessage(String msgId, Object... placeHolders) {
        String message = SpringContextHolder.getBean(Message.class).get(msgId, placeHolders);
        throw BucnException.create(message);
    }

    public static void fieldCheck(String validCode, Object data) {
        ValidsResult validsResult = ValidsContextSimple.ctx().valid(data, validCode);
        if (!validsResult.isValid()) {
            throw BucnException.create(validsResult.getMessage());
        }
    }


}
