package com.ly.adp.common.ms;

import com.ly.bucn.component.ms.MsAdapter;
import com.ly.bucn.component.ms.MsContext;
import com.ly.bucn.component.ms.MsProducers;

public class AdpMsDemo implements MsAdapter {
    @Override
    public MsProducers publisher() {
        return (topic, message) -> {
            String ms = String.format("消息队列[%s][%s]", topic, message);
            System.out.println(ms);
        };
    }

    @Override
    public void init(MsContext msContext) {

    }

    @Override
    public void destroy(MsContext msContext) {

    }

    @Override
    public String name() {
        return "adp.demo";
    }
}
