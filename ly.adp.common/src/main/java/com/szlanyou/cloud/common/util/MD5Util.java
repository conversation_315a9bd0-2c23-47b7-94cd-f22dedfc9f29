package com.szlanyou.cloud.common.util;

import java.nio.charset.Charset;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MD5Util {
    private static final Logger logger = LoggerFactory.getLogger(MD5Util.class);

    public static String encoderToken(String source) {
        String token = "";

        try {
            token = encodeHex(MessageDigest.getInstance("MD5").digest(source.getBytes()));
        } catch (NoSuchAlgorithmException var3) {
            logger.error(source + "\u751f\u6210token\u5931\u8d25:" + var3.getMessage(), var3);
        }

        return token;
    }

    private static String encodeHex(byte[] bytes) {
        StringBuffer buffer = new StringBuffer(bytes.length * 2);

        for(int i = 0; i < bytes.length; ++i) {
            if ((bytes[i] & 255) < 16) {
                buffer.append("0");
            }

            buffer.append(Long.toString((long)(bytes[i] & 255), 16));
        }

        return buffer.toString();
    }

    public static String md5(String encryptStr) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            String result = "";
            byte[] temp = md5.digest(encryptStr.getBytes(Charset.forName("UTF-8")));

            for(int i = 0; i < temp.length; ++i) {
                result = result + Integer.toHexString(255 & temp[i] | -256).substring(6);
            }

            return result.toLowerCase();
        } catch (NoSuchAlgorithmException var5) {
            logger.error("\u83b7\u53d6\u4fe1\u606f\u6458\u8981\u5b9e\u4f8b\u9519\u8bef", var5);
        } catch (Exception var6) {
            logger.error("\u8ba1\u7b97MD5\u7801\u5931\u8d25", var6);
        }

        return encryptStr;
    }
}

