

//apply plugin: 'io.spring.dependency-management'
apply plugin: 'application'


	
startScripts{

   // Properties prop = Properties();

    //prop.load("gradle.properties").
    if(project.findProperty("appMainClassName")){
        mainClassName=appMainClassName
       // println "name $mainClassName"
     }
    if(project.findProperty("appDefaultJvmOpts")){

        defaultJvmOpts.plus(appDefaultJvmOpts);
       // println  "jvm $defaultJvmOpts"
    }

  	//classpath += files('src/dist/XxxAPlaceHolderForAConfigxxX')
  	classpath = files('src/dist/XxxAPlaceHolderForAConfigxxX') + classpath


  	doLast {
       // unixStartScriptGenerator = new CustomUnixStartScriptGenerator()
       // windowsStartScriptGenerator = new CustomWindowsStartScriptGenerator()
        // classpath.add('$APP_HOME/conf')

    	def unixScriptFile    = file getUnixScript()

        def windowsScriptFile = file getWindowsScript()

        def lineclasspath;
         windowsScriptFile.text.eachLine {

            if(it.startsWith('set CLASSPATH')){
               // println it
                lineclasspath = it;
               // it = 'set CLASSPATH=%APP_HOME%\\lib\\*;%APP_HOME%\\conf'
               // println it
            }
        }
    	windowsScriptFile.text = windowsScriptFile.text.replace(lineclasspath, 'set CLASSPATH=%APP_HOME%\\lib\\*;%APP_HOME%\\conf')
    	unixScriptFile.text  = unixScriptFile.text.replace('$APP_HOME/lib/XxxAPlaceHolderForAConfigxxX', '$APP_HOME/conf')
	}
}



applicationDistribution.from(createBizDocs) {
    into "conf"
}

distributions {
  main {
    contents {
      eachFile { file ->
        //println file.path
        if (file.path.contains("/bin/")) {
        	file.setMode(0555)
        } else {
        	file.setMode(0444)
        }
      }
    }
  }
}
  