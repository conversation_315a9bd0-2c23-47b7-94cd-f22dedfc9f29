 buildscript {
		 ext {
					//springBootVersion = '2.0.4.RELEASE'
				}
		  repositories {
		    //jcenter()
			maven {
			  url 'https://maven.aliyun.com/repository/public/'
			}
			maven {
			  url 'https://maven.aliyun.com/repository/spring/'
			}
		maven{
			url 'https://devrepo.devcloud.cn-east-3.huaweicloud.com/04/nexus/content/repositories/0aeb4a9baf00f3e70f6bc018ff034740_1_0/'
			//authentication(userName: 'lanyou_hcf', password: 'lanyou_hcf#szlanyou.COM')
					credentials {
						username "${repositoryUsername}"
						password "${repositoryPassword}"
					}
		}
		    //maven{ url "${repositoryUrl}/repository/maven-public/"}
		  }
		  dependencies {
		  classpath("org.springframework.boot:spring-boot-gradle-plugin:${springBootVersion}")
			classpath('se.transmode.gradle:gradle-docker:1.2')
		  }
		}

//apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'

dependencyManagement {
	imports {
		//mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR8'
		//mavenBom 'com.alibaba.cloud:spring-cloud-alibaba-dependencies:2.2.3.RELEASE'
		mavenBom 'org.springframework.cloud:spring-cloud-dependencies:Hoxton.SR3'
		mavenBom 'com.alibaba.cloud:spring-cloud-alibaba-dependencies:2.2.0.RELEASE'
	}
}

 if(project.hasProperty('docker')){
	 apply plugin: 'org.springframework.boot'
	 //apply plugin: 'io.spring.dependency-management'
	 jar {
        enabled = true
    }
//	apply plugin: 'docker'
//
//
//	task buildDocker(type: Docker, dependsOn: bootJar) {
//		  push = false
//		  dockerfile = file('src/main/docker/Dockerfile')
//		  doFirst {
//		  	copy {
//			  from "$rootDir/skywalking-agent/"
//			  into "$stageDir/skywalking-agent/"
//			}
//			copy {
//			  from jar
//			  into stageDir
//			}
//		  }
//		}
	
 }


