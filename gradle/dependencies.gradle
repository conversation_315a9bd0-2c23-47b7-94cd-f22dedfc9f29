ext {
    springBootVersion = '2.2.10.RELEASE'
    //springBootVersion= '2.3.4.RELEASE'
    libs = [
/*
          gateway		: [
		  		  "com.netflix.archaius:archaius-core:0.7.4",
		  		  "org.ow2.asm:asm:5.0.4",
		  		  "org.hdrhistogram:HdrHistogram:2.1.9",
		  		  "com.netflix.hystrix:hystrix-core:1.5.6",
		  		  "com.netflix.hystrix:hystrix-javanica:1.5.6",
		  		  "com.netflix.hystrix:hystrix-metrics-event-stream:1.5.6",
		  		  "com.netflix.hystrix:hystrix-serialization:1.5.6",
		  		  "javax.inject:javax.inject:1",
		  		  "com.sun.jersey.contribs:jersey-apache-client4:1.19.1",
				  "com.sun.jersey:jersey-client:1.19.1",
				  "com.sun.jersey:jersey-core:1.19.1",
				  "com.google.code.findbugs:jsr305:3.0.1",
				  "javax.ws.rs:jsr311-api:1.1.1",
				  "com.netflix.netflix-commons:netflix-commons-util:0.1.1",
				  "com.netflix.netflix-commons:netflix-statistics:0.1.1",
				  "io.netty:netty-buffer:4.0.27.Final",
				  "io.netty:netty-codec:4.0.27.Final",
				  "io.netty:netty-codec-http:4.0.27.Final",
				  "io.netty:netty-common:4.0.27.Final",
				  "io.netty:netty-handler:4.0.27.Final",
				  "io.netty:netty-transport:4.0.27.Final",
				  "io.netty:netty-transport-native-epoll:4.0.27.Final",
				  "com.netflix.ribbon:ribbon:2.2.0",
				  "com.netflix.ribbon:ribbon-core:2.2.0",
				  "com.netflix.ribbon:ribbon-httpclient:2.2.0",
				  "com.netflix.ribbon:ribbon-loadbalancer:2.2.0",
				  "com.netflix.ribbon:ribbon-transport:2.2.0",
				  "io.reactivex:rxjava:1.1.5",
				  "io.reactivex:rxnetty:0.4.9",
				  "io.reactivex:rxnetty-contexts:0.4.9",
				  "io.reactivex:rxnetty-servo:0.4.9",
				  "com.netflix.servo:servo-core:0.10.1",
				  "com.netflix.servo:servo-internal:0.10.1",
				  "org.springframework.cloud:spring-cloud-commons:1.1.1.RELEASE",
				  "org.springframework.cloud:spring-cloud-context:1.1.1.RELEASE",
				  "org.springframework.cloud:spring-cloud-netflix-core:1.1.5.RELEASE",
				  "com.netflix.zuul:zuul-core:1.1.0"
          ],*/
swagger2Core: [
        "io.springfox:springfox-core:2.9.2",
        "io.springfox:springfox-schema:2.9.2",
        "io.springfox:springfox-spi:2.9.2",
        "io.springfox:springfox-spring-web:2.9.2",
        "io.springfox:springfox-swagger-common:2.9.2",
        "io.springfox:springfox-swagger-ui:2.9.2",
        "io.springfox:springfox-swagger2:2.9.2",
        "io.swagger:swagger-annotations:1.5.21",
        "io.swagger:swagger-models:1.5.21",
        "com.fasterxml:classmate:1.3.1"

],
jta         : [
        "com.atomikos:transactions:3.9.3",
        "com.atomikos:transactions-api:3.9.3",
        "com.atomikos:transactions-jdbc:3.9.3",
        "com.atomikos:transactions-jta:3.9.3",
        "com.atomikos:atomikos-util:3.9.3",
        "javax.transaction:jta:1.1"

],
tcc         : ["org.apache.commons:commons-lang3:3.4",
               /*
                "javax.transaction:jta:1.1",
                "javax.resource:javax.resource-api:1.7",
                "com.caucho:hessian:4.0.38",
                "javax.jms:javax.jms-api:2.0",

                  "org.bytesoft:bytejta-core:0.3.0",
                  "org.bytesoft:bytetcc-common:0.3.0",
                  "org.bytesoft:bytetcc-core:0.3.0",*/

],
cloudconfig : [
        /*
                  "com.szlanyou.apollo:apollo-client:1.2.8",
                  "com.szlanyou.apollo:apollo-core:1.3.0",
                  "com.google.inject:guice:4.2.2",
                  "com.szlanyou.foundation:foundation-service:2.5.6",
                  "com.szlanyou.framework:framework-foundation:1.5.0",
                  "org.codehaus.plexus:plexus-classworlds:2.5.1",
                  "org.codehaus.plexus:plexus-container-default:1.6",
                  "org.codehaus.plexus:plexus-utils:3.0.24",
                  "org.apache.xbean:xbean-reflect:4.5"
                  */

],
activemq    : [


        "org.springframework.boot:spring-boot-starter-activemq:${springBootVersion}",
        "org.apache.rocketmq:rocketmq-client:4.6.1",
        "org.apache.rocketmq:rocketmq-acl:4.6.1",
        "com.rabbitmq:amqp-client:5.7.1"
],
commons     : [
        //aad

        /*"org.springframework.security:spring-security-core:5.1.15.RELEASE",
        "org.springframework.security:spring-security-web:5.1.15.RELEASE",
        "org.springframework.security:spring-security-config:5.1.15.RELEASE",
*/
        "redis.clients:jedis:3.1.0",
        "org.apache.commons:commons-pool2:2.4.1",
        "com.alibaba:fastjson:1.2.83",

        //"cn.jpush.api:jpush-client:3.2.9",
        "com.google.code.gson:gson:2.8.0",
        "com.google.guava:guava:25.1-android",
        "com.google.collections:google-collections:1.0",

        //"com.lowagie:itext:2.1.7",
        //"com.szlanyou.iTextAsian:iTextAsian:1.0",

        //"com.szlanyou.render:render:1.0",
        //"com.szlanyou.tidy:Tidy:1.0",

        // 1.1.23 1.1.24有bug
        "com.alibaba:druid:1.1.21",
        //"org.apache.dubbo:dubbo:2.7.8" ,
        /*
        "com.szlanyou.zkclient:zkclient:0.1",
        "org.apache.zookeeper:zookeeper:3.4.6",
       "org.apache.curator:curator-recipes:4.1.0",
       */
        "commons-configuration:commons-configuration:1.8",
        "commons-codec:commons-codec:1.9",
        "org.apache.commons:commons-collections4:4.4",
        "commons-fileupload:commons-fileupload:1.3.3",
        "commons-io:commons-io:2.4",
        "commons-lang:commons-lang:2.4",
        "commons-net:commons-net:3.3",
        // "dom4j:dom4j:1.6.1",
        "jaxen:jaxen:1.1.6",
        "net.sf.ezmorph:ezmorph:1.0.6",

        "net.sf.json-lib:json-lib:2.4:jdk15",

        //"com.szlanyou.masmclient:MASMClient:1.0",
        //"mysql:mysql-connector-java:8.0.19",
        // 要支持jta，版本只能用 8.0.11
        "mysql:mysql-connector-java:8.0.16",
        "com.oracle.database.jdbc:ojdbc8:********",
        "cn.easyproject:orai18n:********.0",
        "com.szlanyou.sqljdbc42:sqljdbc42:4.2",
        "net.sourceforge.jtds:jtds:1.3.1",
        "org.apache.tomcat.embed:tomcat-embed-core:9.0.43",
        "org.apache.tomcat.embed:tomcat-embed-el:9.0.31",
        "org.apache.tomcat.embed:tomcat-embed-websocket:9.0.31",
        /*
      //  "org.apache.poi:poi:3.11",
      //  "org.apache.poi:poi-excelant:3.11",
      //  "org.apache.poi:poi-ooxml:3.11",
      //  "org.apache.poi:poi-ooxml-schemas:3.11",
     //   "org.apache.poi:poi-scratchpad:3.11",
      //  "org.apache.xmlbeans:xmlbeans:2.6.0",
        */

        "org.apache.poi:poi:4.1.2",
        "org.apache.poi:poi-excelant:4.1.2",
        "org.apache.poi:poi-ooxml:4.1.2",
        "org.apache.poi:poi-ooxml-schemas:4.1.2",
        "org.apache.poi:poi-scratchpad:4.1.2",
        "org.apache.xmlbeans:xmlbeans:3.1.0",
        "org.apache.poi:ooxml-schemas:1.4",
        "com.madgag:animated-gif-lib:1.4",

        "org.apache.httpcomponents:httpcore:4.4.1",
        "org.apache.httpcomponents:httpmime:4.4.1",

        "commons-logging:commons-logging:1.2",

        "com.google.zxing:core:3.2.0",
        "com.maxmind.geoip2:geoip2:2.12.0",
        "com.szlanyou.dbay:dbay-apns4j:1.0",

        "com.szlanyou.menagerie:menagerie:1.1",

        "org.redisson:redisson:3.11.6",
        /*
        "com.netflix.hystrix:hystrix-core:1.5.12",
        "com.netflix.hystrix:hystrix-javanica:1.5.12",
        "com.netflix.hystrix:hystrix-metrics-event-stream:1.5.12",
        */
        "org.apache.shardingsphere:sharding-jdbc-core:4.1.1",

        "org.mybatis:mybatis-typehandlers-jsr310:1.0.2",

        "org.springframework.cloud:spring-cloud-starter-openfeign",
        "com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery",
        "com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config",
        "com.alibaba.nacos:nacos-client:1.3.2",

        "com.fasterxml.jackson.core:jackson-databind:2.11.2",
        "org.apache.pdfbox:pdfbox:2.0.22",
        "com.github.ulisesbocchio:jasypt-spring-boot-starter:1.18",
        "com.alibaba:easyexcel:2.2.6",

        "com.mchange:c3p0:*******",
],
ai          : [
        "com.hp.hpl.jena:json-jena:1.0",
        "com.szlanyou.msc:Msc:1.0"
],
ztbi        : [

        "org.codehaus.jackson:jackson-core-asl:1.4.2",
        "org.codehaus.jackson:jackson-mapper-asl:1.4.2",
        "org.json:json:20180813",
        "com.szlanyou.olap4j:olap4j:1.0",
],
oauth       : [

        /* "org.springframework.security:spring-security-web:5.1.15.RELEASE",
         "org.springframework.security:spring-security-config:5.1.15.RELEASE",
         "org.springframework.security:spring-security-core:5.1.15.RELEASE",*/
        "org.springframework.security.oauth:spring-security-oauth2:2.3.5.RELEASE",
        /*"org.springframework.security:spring-security-core:3.2.10.RELEASE",
        "org.springframework.security:spring-security-web:3.2.10.RELEASE",
        "org.springframework.security:spring-security-config:3.2.10.RELEASE"*/
]

    ]
}

